<?php

namespace App\Filter;

/**
 * Class DateRangeFilter.
 */
final class DateRangeFilter extends BaseRangeFilter
{
    public function getRangeFieldType(): string
    {
        return 'date';
    }

    /**
     * @param \DateTimeInterface|null $from
     * @param \DateTimeInterface|null $to
     */
    public function getRangeQuery(string $property, $from = null, $to = null): array
    {
        $query = [];
        if ($from) {
            $query['gte'] = $from->format(\DATE_ATOM);
        }
        if ($to) {
            $query['lte'] = $to->format(\DATE_ATOM);
        }

        return [
            'range' => [
                $property => $query,
            ],
        ];
    }
}
