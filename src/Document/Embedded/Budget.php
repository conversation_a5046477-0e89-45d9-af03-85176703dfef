<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class Budget
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::FLOAT)]
    private ?float $amount = null;

    #[MongoDB\Field(type: Type::FLOAT)]
    private ?float $amountNet = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $currency = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $project;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    private ?Period $period = null;

    #[MongoDB\Field(type: Type::INT)]
    private ?int $year = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $notes = null;

    /**
     * @var BudgetBreakdown[]|Collection
     */
    #[MongoDB\EmbedMany(targetDocument: BudgetBreakdown::class)]
    private Collection|array $breakdown;

    /**
     * Budget constructor.
     */
    public function __construct()
    {
        $this->breakdown = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(?float $amount): void
    {
        $this->amount = $amount;
    }

    public function getAmountNet(): ?float
    {
        return $this->amountNet;
    }

    public function setAmountNet(?float $amountNet): void
    {
        $this->amountNet = $amountNet;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): void
    {
        $this->currency = $currency;
    }

    public function getProject()
    {
        return $this->project;
    }

    public function setProject($project): void
    {
        $this->project = $project;
    }

    public function getPeriod()
    {
        return $this->period;
    }

    public function setPeriod($period): void
    {
        $this->period = $period;
    }

    public function getYear(): ?int
    {
        return $this->year;
    }

    public function setYear(?int $year): void
    {
        $this->year = $year;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): void
    {
        $this->notes = $notes;
    }

    public function getBreakdown()
    {
        return $this->breakdown;
    }

    public function setBreakdown($breakdown): void
    {
        $this->breakdown = $breakdown;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
