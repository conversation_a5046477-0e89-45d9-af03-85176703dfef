<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class ProductRequirement
{
    #[MongoDB\Field(type: Type::STRING)]
    private string $requirement;
    #[MongoDB\Field(type: Type::STRING)]
    private string $type;
    #[MongoDB\Field(type: Type::RAW)]
    private array $values;

    public function getRequirement(): string
    {
        return $this->requirement;
    }

    public function setRequirement(string $requirement): void
    {
        $this->requirement = $requirement;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    public function setValues(array $values): void
    {
        $this->values = $values;
    }
}
