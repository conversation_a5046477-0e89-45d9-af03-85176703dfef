<?php

namespace App\Command;

use App\Legacy\Repository\LegacyContractRepository;
use App\Legacy\Repository\LegacyOldIdSetterInterface;
use App\Legacy\Repository\LegacyPlanRepository;
use App\Legacy\Repository\LegacyTenderRepository;
use App\Message\OldIdMessage;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:old-id:generate',
    description: 'Generate old id messages to queue',
)]
class OldIdSetterCommand extends Command
{
    private const CHUNK = 1000;

    public function __construct(
        private readonly LegacyTenderRepository $legacyTenderRepository,
        private readonly LegacyPlanRepository $legacyPlanRepository,
        private readonly LegacyContractRepository $legacyContractRepository,
        private readonly MessageBusInterface $messageBus,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function configure(): void
    {
        $this
            ->addArgument('isArchive', InputArgument::REQUIRED, 'Boolean is archive', null, [true, false])
            ->addArgument('start', InputArgument::REQUIRED, 'Id')
            ->addArgument('type', InputArgument::REQUIRED, 'tender or plan or contract', null, ['tender', 'plan', 'contract'])

        ;
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $isArchive = boolval($input->getArgument('isArchive'));
        $start = $input->getArgument('start');
        $type = $input->getArgument('type');
        $repository = null;
        if ($type === 'tender') {
            $repository = $this->legacyTenderRepository;
        }

        if ($type === 'plan') {
            $repository = $this->legacyPlanRepository;
        }

        if ($type === 'contract') {
            $repository = $this->legacyContractRepository;
        }

        if ($repository instanceof LegacyOldIdSetterInterface) {
            do {
                $items = $repository->getForOldIdSetter($start, self::CHUNK, $isArchive);
                foreach ($items as $item) {
                    $this->messageBus->dispatch(new OldIdMessage($item[$type . '_id'], $item['id'], $type));
                    $start = $item['id'];
                }
                $output->writeln("Last item in chunk is " . $start);

            } while(count($items) >= self::CHUNK);
        }

        return Command::SUCCESS;
    }
}
