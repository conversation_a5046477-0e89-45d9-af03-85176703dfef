<?php

namespace App\Messenger;

use App\Message\PlanMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class PlanSerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $plan = new PlanMessage($data['id']);

        return new Envelope($plan);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();
        if ($message instanceof PlanMessage) {
            $data = ['id' => $message->getId()];
        } else {
            throw new \Exception('Unsupported message class');
        }

        // I did not found stamps usage anywhere so simply disabled them
        // As their serialization throw next error: Serialization of 'Closure' is not allowed
        //        $allStamps = [];
        //        foreach ($envelope->all() as $stamps) {
        //            $allStamps = array_merge($allStamps, $stamps);
        //        }
        return [
            'body' => json_encode($data),
            'headers' => [
                //                'stamps' => serialize($allStamps)
            ],
        ];
    }
}
