<?php

namespace App\EventListener;

use App\Document\Contract;
use App\Document\Inspection;
use App\Document\Monitoring;
use App\Document\Tender;
use FOS\ElasticaBundle\Event\PreTransformEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class PreTransformElasticSearchEventListener implements EventSubscriberInterface
{
    public function doPreTransform(PreTransformEvent $event)
    {
        $object = $event->getObject();

        if ($object instanceof Tender) {
            $config = $object->getTenderConfig();
            if (is_array($config) && array_key_exists('restricted', $config) && $config['restricted']) {
                $object->setRestrictedMode(true);
            }
        }

        if ($object instanceof Contract) {
            $config = $object->getContractConfig();
            if (is_array($config) && array_key_exists('restricted', $config) && $config['restricted']) {
                $object->setRestrictedMode(true);
            }
        }

        if ($object instanceof Monitoring || $object instanceof Inspection) {
            if ($object->getRestricted()) {
                $object->setRestrictedMode(true);
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PreTransformEvent::class => 'doPreTransform',
        ];
    }
}
