<?php

namespace App\Legacy\Repository;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\ManagerRegistry;

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Modera Foundation
 */
abstract class LegacyRepository
{
    protected readonly Connection $connection;
    protected readonly Connection $archiveConnection;

    public function __construct(
        ManagerRegistry $managerRegistry
    ) {
        $connection = $managerRegistry->getConnection('legacy');
        $archiveConnection = $managerRegistry->getConnection('archive');

        if ($connection instanceof Connection) {
            $this->connection = $connection;
        } else {
            throw new Exception('Legacy connection not found');
        }

        if ($archiveConnection instanceof Connection) {
            $this->archiveConnection = $archiveConnection;
        } else {
            throw new Exception('Legacy archive connection not found');
        }
    }
}
