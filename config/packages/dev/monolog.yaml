monolog:
    channels: [ 'api' ]
    handlers:
#        console_very_verbose:
#            type:   console
#            bubble: false
#            verbosity_levels:
#                VERBOSITY_VERBOSE: NOTICE
#                VERBOSITY_VERY_VERBOSE: NOTICE
#                VERBOSITY_DEBUG: DEBUG
#            channels: ["doctrine"]
        #main:
        #    type: rotating_file
        #    max_files: 10
        #    path: "%kernel.logs_dir%/%kernel.environment%.log"
        #    level: debug
        #    channels: ["!event"]
        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        #firephp:
        #    type: firephp
        #    level: info
        #chromephp:
        #    type: chromephp
        #    level: info
        #console:
        #    type: console
        #    process_psr_3_messages: false
        #    channels: ["!event", "!doctrine", "!console"]
