<?php

namespace App\Validator;

use App\Repository\FilterRepository;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class FilterNameValidator extends ConstraintValidator
{
    public function __construct(
        private readonly FilterRepository $filterRepository,
        private readonly TokenStorageInterface $tokenStorage,
        private readonly JWTTokenManagerInterface $jwtManager,
        private readonly RequestStack $requestStack
    ) {
    }

    public function validate(mixed $value, Constraint $constraint)
    {
        if (!$constraint instanceof FilterName) {
            throw new UnexpectedTypeException($constraint, FilterName::class);
        }

        if (null === $value || '' === $value) {
            return;
        }

        $token = $this->tokenStorage->getToken();
        $decodedJwtToken = $this->jwtManager->decode($token);
        $userId = $decodedJwtToken['id'];
        $fields = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $filters = $this->filterRepository->findBy(['userId' => $userId, 'type' => $fields['type'], 'name' => $fields['name']]);

        if ($filters) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('{{ mixed }}', $value)
                ->addViolation();
        }
    }
}
