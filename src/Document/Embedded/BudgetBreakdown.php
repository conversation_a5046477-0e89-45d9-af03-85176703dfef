<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class BudgetBreakdown
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    private ?Value $value = null;
    
    #[MongoDB\EmbedOne(targetDocument: BudgetBreakdownAddress::class)]
    private ?BudgetBreakdownAddress  $address = null;
    
    #[MongoDB\EmbedOne(targetDocument: EmbeddedClassification::class)]
    private ?EmbeddedClassification $classification = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getValue(): ?Value
    {
        return $this->value;
    }

    public function setValue(?Value $value): void
    {
        $this->value = $value;
    }

    public function getAddress(): ?BudgetBreakdownAddress
    {
        return $this->address;
    }

    public function setAddress(?BudgetBreakdownAddress $address): void
    {
        $this->address = $address;
    }

    public function getClassification(): ?EmbeddedClassification
    {
        return $this->classification;
    }

    public function setClassification(?EmbeddedClassification $classification): void
    {
        $this->classification = $classification;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
