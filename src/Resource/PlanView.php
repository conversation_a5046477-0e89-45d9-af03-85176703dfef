<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\PlanDataProvider;
use App\Document\Plan;
use App\Filter\ClassificationFilter;
use App\Filter\DateRangeFilter;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;
use App\Filter\OrderFilter;
use App\Filter\TestModeFilter;
use App\Filter\ValueRangeFilter;
use App\Filter\YearFilter;
use App\Resource\Embedded\BudgetView;
use App\Resource\Embedded\DocumentView;
use App\Resource\Embedded\ItemView;
use App\Resource\Embedded\ProcuringEntityView;
use App\Resource\Embedded\ProjectView;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ApiResource(
    shortName: 'plan',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_plan')),
        new Get(provider: PlanDataProvider::class),
    ],
    order: ['datePublished' => 'DESC']
)]
#[ApiFilter(OrderFilter::class, properties: ['datePublished', 'dateCreated', 'dateModified', 'budget.amount'], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(KeywordFilter::class, properties: ['planIdText', 'budget.description', 'items.description', 'items.descriptionEn'])]
#[ApiFilter(MatchFilter::class, properties: [
    'status' => 'exact',
])]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification' => 'classification.id',
    'additionalClassification' => 'items.additionalClassifications.id',
    'procuringEntity' => 'procuringEntity.identifier.id',
])]
#[ApiFilter(ValueRangeFilter::class, properties: ['budget'])]
#[ApiFilter(DateRangeFilter::class, properties: ['tenderPeriodDate', 'datePublished'])]
#[ApiFilter(YearFilter::class, properties: ['budget.period.startDate'])]
#[ApiFilter(TestModeFilter::class, properties: ['testMode'])]
readonly class PlanView
{
    public ?string $id;

    #[SerializedName('is_masked')]
    public ?bool $isMasked;

    public ?string $mode;

    public ?string $status;

    /**
     * @var ItemView[]
     */
    public array $items;

    public mixed $cancellation;

    public mixed $revisions;

    public mixed $buyers;

    public mixed $milestones;

    /**
     * @var DocumentView[]
     */
    public array $documents;

    public BaseClassificationView $classification;

    /**
     * @SerializedName("planID")
     */
    public ?string $planId;

    public ?BudgetView $budget;

    public ?ProjectView $project;

    public ?\DateTimeInterface $datePublished;

    public ?string $owner;

    /**
     * @var BaseClassificationView[]
     */
    public array $additionalClassifications;

    public ?ProcuringEntityView $procuringEntity;

    public mixed $tender;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    /**
     * @SerializedName("tender_id")
     */
    public ?string $tenderId;

    public bool $testMode;

    public function __construct(Plan $plan)
    {
        $this->id = $plan->getId();
        $this->isMasked = $plan->getIsMasked();
        $this->mode = $plan->getMode();
        $this->status = $plan->getStatus();

        $items = [];
        foreach ($plan->getItems() as $item) {
            $items[] = new ItemView($item);
        }
        $this->items = [...$items];

        $this->cancellation = $plan->getCancellation();
        $this->revisions = $plan->getRevisions();
        $this->buyers = $plan->getBuyers();
        $this->milestones = $plan->getMilestones();

        $documents = [];
        foreach ($plan->getDocuments() as $document) {
            $documents[] = new DocumentView($document);
        }
        $this->documents = [...$documents];

        $this->classification = new BaseClassificationView($plan->getClassification());

        $this->planId = $plan->getPlanId();

        $budget = $plan->getBudget();
        $this->budget = ($budget) ? new BudgetView($budget) : null;

        $project = $plan->getProject();
        $this->project = ($project) ? new ProjectView($project) : null;

        $this->datePublished = $plan->getDatePublished();
        $this->owner = $plan->getOwner();

        $additionalClassifications = [];
        foreach ($plan->getAdditionalClassifications() as $classification) {
            $additionalClassifications[] = new BaseClassificationView($classification);
        }
        $this->additionalClassifications = [...$additionalClassifications];

        $procuringEntity = $plan->getProcuringEntity();
        $this->procuringEntity = ($procuringEntity) ? new ProcuringEntityView($procuringEntity) : null;

        $this->tender = $plan->getTender();
        $this->dateModified = $plan->getDateModified();
        $this->dateCreated = $plan->getDateCreated();
        $this->tenderId = $plan->getTenderId();
    }

    public function getTenderPeriodDate()
    {
        if (array_key_exists('tenderPeriod', $this->tender) && is_array($this->tender['tenderPeriod'])) {
            $tenderPeriod = $this->tender['tenderPeriod'];
            if (array_key_exists('startDate', $tenderPeriod) && (false !== strtotime($tenderPeriod['startDate']))) {
                return $tenderPeriod['startDate'];
            }
        }

        return null;
    }
}
