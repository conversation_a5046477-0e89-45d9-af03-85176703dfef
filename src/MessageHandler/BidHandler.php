<?php

namespace App\MessageHandler;

use App\Legacy\Repository\TenderBidsRepository;
use App\Message\BidMessage;
use App\Repository\TenderRepository;
use App\Service\BidService;
use Doctrine\ODM\MongoDB\MongoDBException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class BidHandler extends BaseHandler
{
    public function __construct(
        private readonly TenderBidsRepository $tenderBidsRepository,
        private readonly TenderRepository $tenderRepository,
        private readonly BidService $bidService,
    ) {
    }

    /**
     * @throws MongoDBException
     */
    public function __invoke(BidMessage $bidMessage)
    {
        if ($bid = $this->tenderBidsRepository->getBidById($bidMessage->getId())) {
            if ($tender = $this->tenderRepository->find($bid['tender_api_id'])) {
                $this->bidService->addBidToTender($tender, $bid);
            }
        }
    }
}
