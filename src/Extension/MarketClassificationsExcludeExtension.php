<?php

namespace App\Extension;

use ApiPlatform\Elasticsearch\Extension\RequestBodySearchCollectionExtensionInterface;
use ApiPlatform\Metadata\Operation;
use App\Resource\MarketClassificationView;

/**
 * Class ElasticExcludeExtension.
 */
class MarketClassificationsExcludeExtension implements RequestBodySearchCollectionExtensionInterface
{
    private const VIEWS = [
        MarketClassificationView::class
    ];

    public function applyToCollection(
        array $requestBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): array {
        if (!in_array($resourceClass, self::VIEWS)) {
            return $requestBody;
        }

        $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
            'range' => [
                'activeCategories' => [
                    "gt" => 0
                ],
            ],
        ];

        $requestBody['query']['constant_score']['filter']['bool']['must_not'][] = [
            'term' => [
                '_id' => '99999999-9' //restricted classification to show
            ]
        ];

        return $requestBody;
    }
}
