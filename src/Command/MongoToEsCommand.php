<?php

namespace App\Command;

use App\Document\MongoToEs;
use App\Message\MongoToEsMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class MongoToEsCommand extends Command
{
    protected static $defaultName = 'app:mongo-to-es';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct(self::$defaultName);
    }

    protected function configure()
    {
        $this->setDescription('Migrate data from MongoDB to ElasticSearch')
          ->setHelp('This command allows you to migrate data from MongoDB to ElasticSearch by putting document ids in a queue')
          ->addOption('type', 't', InputOption::VALUE_REQUIRED,
              'Type of document to migrate (tender, contract, etc)')
          ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL,
              'Limit number of documents to migrate, default -1 which is unlimited', -1);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $type = $input->getOption('type');
        $limit = $input->getOption('limit');

        $documentClass = DocumentType::getDocumentClass($type);
        $pageSize = 10000;
        if (-1 !== $limit && $limit < $pageSize) {
            $pageSize = $limit;
        }

        while (true) {
            $pageDocument = $this->documentManager->getRepository(MongoToEs::class)->findOneBy(['type' => $type]);

            $queryBuilder = $this->documentManager->createQueryBuilder($documentClass)
              ->select('id', 'dateCreated')
              ->sort('dateCreated', 'ASC')
              ->limit($pageSize);

            if ($pageDocument) {
                $output->writeln("Migrating $type documents from ".$pageDocument->getDateCreated()->format('Y-m-d H:i:s'));
                $queryBuilder->addAnd(
                    $queryBuilder->expr()->field('dateCreated')->gt($pageDocument->getDateCreated())
                );
            } else {
                $output->writeln("Migrating $type documents from the beginning");
            }

            $result = $queryBuilder->getQuery()->execute();
            if (0 === count($result)) {
                break;
            }

            foreach ($result as $document) {
                $newDate = $document->getDateCreated();
                $this->messageBus->dispatch(new MongoToEsMessage($document->getId(), $type));
            }

            $this->documentManager->clear();

            if (!$pageDocument) {
                $pageDocument = new MongoToEs();
                $pageDocument->setType($type);
            }

            $pageDocument->setDateCreated($newDate);
            $this->documentManager->persist($pageDocument);

            $this->documentManager->flush();

            if (-1 !== $limit) {
                $limit -= $pageSize;
                if ($limit <= 0) {
                    break;
                }
            } else {
                if (count($result) < $pageSize) {
                    break;
                }
            }
        }

        $output->writeln("Migrating $type documents finished");

        return 0;
    }
}
