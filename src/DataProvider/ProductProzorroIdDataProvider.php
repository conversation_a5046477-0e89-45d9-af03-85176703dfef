<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Document\Product;
use App\Manager\ProductManager;
use App\Resource\ProductView;
use App\Service\CriteriaFilterService;

final readonly class ProductProzorroIdDataProvider implements ProviderInterface
{
    public function __construct(
        private ProductManager $productManager,
        private CriteriaFilterService $criteriaFilterService,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        /** @var Product $document */
        $document = $this->productManager->findByProzorroId($uriVariables['id']);

        if (!$document) {
            return null;
        }

        return new ProductView($document, [], $this->criteriaFilterService);
    }
}
