<?php

namespace App\MessageHandler;

use App\Document\Agreement;
use App\Message\AgreementMessage;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;

#[AsMessageHandler]
class AgreementHandler extends BaseHandler
{
    /**
     * @throws MongoDBException
     * @throws GuzzleException
     * @throws \Throwable
     */
    public function __invoke(AgreementMessage $agreement): void
    {
        try {
            try {
                $response = $this->requestPublicApi('agreements/'.$agreement->getId());
            } catch (GuzzleException $e) {
                echo $e->getMessage();

                return;
            }

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];

            if (array_key_exists('config', $result)) {
                $data['agreementConfig'] = $result['config'];
            }

            $prozorroId = $data['id'];
            unset($data['id']);

            $object = $this->getDocumentManager()->getRepository(Agreement::class)->findOneBy(
                ['prozorroId' => $prozorroId]
            );

            $object = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data, JSON_THROW_ON_ERROR),
                    Agreement::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
                );
            $object->setProzorroId($prozorroId);

            $this->save($object);
        } catch (\Throwable $throwable) {
            $this->getLogger()->critical('Unandled Error received: '.$throwable->getMessage(), [$this, $throwable]);
            throw $throwable;
        }
    }
}
