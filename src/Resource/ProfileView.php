<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\ProfileDataProvider;
use App\Document\Agreement;
use App\Document\Category;
use App\Document\Profile;
use App\Filter\ClassificationFilter;
use App\Filter\OrderFilter;
use App\Resource\Embedded\AgreementView;
use App\Resource\Embedded\ImageView;
use App\Resource\Embedded\UnitView;
use App\Resource\Embedded\ValueView;
use Doctrine\Common\Collections\Criteria;
use App\Service\CriteriaFilterService;

#[ApiResource(
    shortName: 'profile',
    operations: [
        new GetCollection(normalizationContext: ['groups' => 'profile:list'],
            provider: CollectionProvider::class,
            stateOptions: new Options(index: 'dzo_profile')),
        new Get(normalizationContext: ['groups' => 'profile:read'],
            provider: ProfileDataProvider::class,
            stateOptions: new Options(index: 'dzo_profile')),
    ],
    order: ['classification.id' => Criteria::ASC],
    paginationItemsPerPage: 10,
    paginationType: 'page'
)]
#[ApiFilter(OrderFilter::class, properties: [
    'title',
    'classification.id',
    'popularity',
], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification' => 'classification.id',
    'additionalClassification' => 'additionalClassifications.id',
    'relatedCategoryId' => 'relatedCategoryId',
])]
readonly class ProfileView
{
    #[ApiProperty(identifier: true)]
    public ?string $id;

    public ?string $prozorroId;

    public ?string $status;

    public ?string $title;

    public ?string $description;
    public ?string $agreementID;

    public ?string $owner;

    public mixed $criteria;

    protected ?\DateTimeInterface $dateModified;

    protected ?\DateTimeInterface $dateCreated;

    public ?MarketClassificationView $classification;
    public ?CategoryView $category;
    public ?string $relatedCategory;
    public ?string $relatedCategoryId;

    public ?ValueView $value;

    public ?UnitView $unit;

    public mixed $additionalClassifications;

    /** @var ImageView[] */
    public array $images;

    public ?AgreementView $agreement;

    public function __construct(
        Profile $profile,
        private CriteriaFilterService $criteriaFilterService,
        ?Category $category = null,
        ?string $awsCdnUrl = null,
        ?Agreement $agreement = null,
    )
    {
        $this->id = $profile->getId();
        $this->prozorroId = $profile->getProzorroId();
        $this->status = $profile->getStatus();
        $this->title = $profile->getTitle();
        $this->description = $profile->getDescription();
        $this->agreementID = $profile->getAgreementID();
        $this->owner = $profile->getOwner();
        $this->criteria = $this->criteriaFilterService->filterArchivedRequirements($profile->getCriteria());
        $this->additionalClassifications = $profile->getAdditionalClassifications();
        $this->dateModified = $profile->getDateModified();
        $this->dateCreated = $profile->getDateCreated();
        $this->classification = new MarketClassificationView($profile->getClassification());
        $this->category = $category ? new CategoryView($category, $this->criteriaFilterService) : null;
        $this->relatedCategory = $profile->getRelatedCategory();
        $this->relatedCategoryId = $profile->getRelatedCategoryId();
        $this->value = $profile->getValue() ? new ValueView($profile->getValue()) : null;
        $this->unit = $profile->getUnit() ? new UnitView($profile->getUnit()) : null;

        $images = [];
        foreach ($profile->getImages() as $image) {
            $images[] = new ImageView($image, $awsCdnUrl);
        }
        $this->images = [...$images];

        if ($agreement) {
            $this->agreement = new AgreementView($agreement);
        }
    }
}
