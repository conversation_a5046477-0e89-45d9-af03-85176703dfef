<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\MonitoringDataProvider;
use App\Document\Monitoring;
use App\Filter\MatchFilter;
use App\Filter\OrderFilter;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ApiResource(
    shortName: 'monitoring',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_monitoring')),
        new Get(provider: MonitoringDataProvider::class),
    ],
)]
#[ApiFilter(OrderFilter::class, properties: ['dateModified'], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(MatchFilter::class, properties: ['tenderId' => 'exact'])]
readonly class MonitoringView
{
    public ?string $id;

    #[SerializedName('tender_id')]
    public ?string $tenderId;

    #[SerializedName('tender_id')]
    public ?string $monitoringId;

    public ?string $status;

    public mixed $reasons;

    public mixed $procuringStages;

    public mixed $monitoringPeriod;

    public mixed $eliminationPeriod;

    public mixed $eliminationReport;

    public mixed $posts;

    public mixed $parties;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $endDate;

    public ?\DateTimeInterface $dateCreated;

    public ?string $tenderOwner;

    public mixed $decision;

    public mixed $conclusion;

    public mixed $appeal;

    public mixed $documents;

    public mixed $cancellation;

    public ?bool $hasInspection;

    /**
     * MonitoringView constructor.
     */
    public function __construct(Monitoring $monitoring)
    {
        if ($monitoring->getRestricted()) {
            $monitoring->setRestrictedMode(true);
        }

        $this->id = $monitoring->getId();
        $this->tenderId = $monitoring->getTenderId();
        $this->monitoringId = $monitoring->getMonitoringId();
        $this->status = $monitoring->getStatus();
        $this->reasons = $monitoring->getReasons();
        $this->procuringStages = $monitoring->getProcuringStages();
        $this->monitoringPeriod = $monitoring->getMonitoringPeriod();
        $this->eliminationPeriod = $monitoring->getEliminationPeriod();
        $this->eliminationReport = $monitoring->getEliminationReport();
        $this->posts = $monitoring->getPosts();
        $this->parties = $monitoring->getParties();
        $this->dateModified = $monitoring->getDateModified();
        $this->endDate = $monitoring->getEndDate();
        $this->dateCreated = $monitoring->getDateCreated();
        $this->tenderOwner = $monitoring->getTenderOwner();
        $this->decision = $monitoring->getDecision();
        $this->conclusion = $monitoring->getConclusion();
        $this->appeal = $monitoring->getAppeal();
        $this->documents = $monitoring->getDocuments();
        $this->cancellation = $monitoring->getCancellation();
        $this->hasInspection = $monitoring->getHasInspection();
    }
}
