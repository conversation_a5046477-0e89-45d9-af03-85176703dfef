<?php

namespace App\Document;

use App\Document\Embedded\Buyer;
use App\Document\Embedded\Document;
use App\Document\Embedded\Item;
use App\Document\Embedded\Organization;
use App\Document\Embedded\Period;
use App\Document\Embedded\ProcuringEntity;
use App\Document\Embedded\Value;
use App\Helper\RestrictedDataHelper;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[MongoDB\Document]
class Contract implements HasDateModifiedInterface
{
    use RestrictedDataHelper;

    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::BOOL, nullable: true)]
    #[SerializedName('is_masked')]
    protected ?bool $isMasked = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $mode = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $data;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    private ?Value $value = null;

    /**
     * @var Collection|Document[]
     */
    #[MongoDB\EmbedMany(targetDocument: Document::class)]
    protected Collection|array $documents;

    #[MongoDB\Field(name: 'tender_id', type: Type::STRING, nullable: true)]
    #[SerializedName('tender_id')]
    private ?string $tenderId = null;

    /**
     * @var Collection|Item[]
     */
    #[MongoDB\EmbedMany(targetDocument: Item::class)]
    protected Collection|array $items;

    #[MongoDB\EmbedOne(targetDocument: Buyer::class)]
    protected ?Buyer $buyer = null;

    #[MongoDB\Field(name: 'bid_owner', type: Type::STRING, nullable: true)]
    #[SerializedName('bid_owner')]
    protected ?string $bidOwner = null;

    /**
     * @var Collection|Organization[]
     */
    #[MongoDB\EmbedMany(targetDocument: Organization::class)]
    private Collection|array $suppliers;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $contractNumber = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    private ?Period $period = null;

    #[MongoDB\Field(type: Type::DATE)]
    private $dateSigned;

    #[MongoDB\Field(type: Type::DATE)]
    #[MongoDB\Index(order: Criteria::DESC)]
    private ?\DateTimeInterface $dateModified = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\EmbedOne(targetDocument: ProcuringEntity::class)]
    protected ?ProcuringEntity $procuringEntity = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $owner = null;

    #[MongoDB\Field(name: 'awardID', type: Type::RAW)]
    #[SerializedName('awardID')]
    private ?string $awardId = null;

    #[MongoDB\Field(name: 'contractID', type: Type::STRING, nullable: true)]
    #[SerializedName('contractID')]
    private ?string $contractId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::DATE)]
    private $date;

    #[MongoDB\Field(type: Type::RAW)]
    private $changes;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    private ?Value $amountPaid = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $terminationDetails;

    // Next fields are retrieved from tender

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $procurementMethodType = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $procurementMethodRationale = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $contractConfig;

    private bool $restrictedMode = false;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private ?int $oldId = null;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->items = new ArrayCollection();
        $this->suppliers = new ArrayCollection();
    }

    public function getBidOwner(): ?string
    {
        return $this->bidOwner;
    }

    public function getBuyer(): ?Buyer
    {
        return $this->buyer;
    }

    public function setBuyer(?Buyer $buyer): void
    {
        $this->buyer = $buyer;
    }

    public function setBidOwner(?string $bidOwner): void
    {
        $this->bidOwner = $bidOwner;
    }

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
        foreach ($this->suppliers as $supplier) {
            $supplier->setRestrictedMode($restrictedMode);
        }
        foreach ($this->items as $item) {
            $item->setRestrictedMode($restrictedMode);
        }
        foreach ($this->documents as $document) {
            $document->setRestrictedMode($restrictedMode, false);
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getIsMasked(): ?bool
    {
        return $this->isMasked;
    }

    public function setIsMasked(?bool $isMasked): void
    {
        $this->isMasked = $isMasked;
    }

    public function getMode(): ?string
    {
        return $this->mode;
    }

    public function setMode(?string $mode): void
    {
        $this->mode = $mode;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setData($data): void
    {
        $this->data = $data;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    /**
     * @return Value|null
     */
    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value): void
    {
        $this->value = $value;
    }

    /**
     * @return Document[]|Collection
     */
    public function getDocuments()
    {
        return $this->documents;
    }

    /**
     * @param Document[]|Collection $documents
     */
    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function getTenderId(): ?string
    {
        return $this->tenderId;
    }

    public function setTenderId(?string $tenderId): void
    {
        $this->tenderId = $tenderId;
    }

    /**
     * @return Item[]|Collection
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param Item[]|Collection $items
     */
    public function setItems($items): void
    {
        $this->items = $items;
    }

    public function getSuppliers()
    {
        return $this->suppliers;
    }

    public function setSuppliers($suppliers): void
    {
        $this->suppliers = $suppliers;
    }

    public function getContractNumber(): ?string
    {
        return $this->contractNumber;
    }

    public function setContractNumber(?string $contractNumber): void
    {
        $this->contractNumber = $contractNumber;
    }

    public function getPeriod(): ?Period
    {
        return $this->period;
    }

    public function setPeriod(?Period $period): void
    {
        $this->period = $period;
    }

    public function getDateSigned()
    {
        return $this->dateSigned;
    }

    public function setDateSigned($dateSigned): void
    {
        $this->dateSigned = $dateSigned;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    public function setDateModified($dateModified): void
    {
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getProcuringEntity(): ?ProcuringEntity
    {
        return $this->procuringEntity;
    }

    public function setProcuringEntity(?ProcuringEntity $procuringEntity): void
    {
        $this->procuringEntity = $procuringEntity;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): void
    {
        $this->owner = $owner;
    }

    public function getAwardId(): ?string
    {
        return $this->awardId;
    }

    public function setAwardId(?string $awardId): void
    {
        $this->awardId = $awardId;
    }

    public function getContractId(): ?string
    {
        return $this->contractId;
    }

    public function setContractId(?string $contractId): void
    {
        $this->contractId = $contractId;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getDate()
    {
        return $this->date;
    }

    public function setDate($date): void
    {
        $this->date = $date;
    }

    public function getChanges()
    {
        if ($this->restrictedMode && is_array($this->changes)) {
            $restrictedChanges = $this->changes;
            foreach ($restrictedChanges as $key => &$restrictedChange) {
                if (array_key_exists('modifications', $restrictedChange)) {
                    $restrictedChange['modifications'] = array_map(function ($modification) {
                        if (array_key_exists('items', $modification)) {
                            $modification['items'] = array_map(function ($item) {
                                if (array_key_exists('deliveryAddress', $item)) {
                                    $item['deliveryAddress'] = $this->restrictAddressData($item['deliveryAddress']);
                                }
                                if (array_key_exists('deliveryLocation', $item)) {
                                    $item['deliveryLocation'] = $this->restrictLocationData($item['deliveryLocation']);
                                }

                                return $item;
                            }, $modification['items']);
                        }

                        return array_merge($modification, [
                            'type' => 'Приховано',
                            'description' => 'Приховано',
                        ]);
                    }, $restrictedChange['modifications']);
                }

                if (array_key_exists('suppliers', $restrictedChange)) {
                    $restrictedChange['suppliers'] = array_map(function ($supplier) {
                        return $this->restrictCompanyData($supplier);
                    }, $restrictedChange['suppliers']);
                }

                $restrictedChanges[$key] = array_merge($restrictedChange, [
                    'rationale' => 'Приховано',
                    'rationale_en' => 'Hidden',
                    'rationale_ru' => 'Скрыто',
                ]);
            }

            return $restrictedChanges;
        }

        return $this->changes;
    }

    public function setChanges($changes): void
    {
        $this->changes = $changes;
    }

    public function getAmountPaid(): ?Value
    {
        return $this->amountPaid;
    }

    public function setAmountPaid(?Value $amountPaid): void
    {
        $this->amountPaid = $amountPaid;
    }

    public function getTerminationDetails()
    {
        return $this->terminationDetails;
    }

    public function setTerminationDetails($terminationDetails): void
    {
        $this->terminationDetails = $terminationDetails;
    }

    public function getProcurementMethodType()
    {
        return $this->procurementMethodType;
    }

    public function setProcurementMethodType($procurementMethodType): void
    {
        $this->procurementMethodType = $procurementMethodType;
    }

    public function getProcurementMethodRationale()
    {
        return $this->procurementMethodRationale;
    }

    public function setProcurementMethodRationale($procurementMethodRationale): void
    {
        $this->procurementMethodRationale = $procurementMethodRationale;
    }

    public function getProcurementMethodTypeFilter()
    {
        $filter = $this->procurementMethodType ?? '';
        $rationale = $this->procurementMethodRationale ?? '';
        if ('reporting' === $filter && 'catalogue' === substr($rationale, 0, 9)) {
            $rationale = 'catalogue';
        }
        if ('' !== $rationale) {
            $rationale = '.'.$rationale;
        }

        return $filter.$rationale;
    }

    /**
     * @return string|null
     */
    public function getContractIdText()
    {
        return $this->contractId;
    }

    public function getContractConfig()
    {
        return $this->contractConfig;
    }

    public function setContractConfig($contractConfig): void
    {
        $this->contractConfig = $contractConfig;
    }

    public function getOldId(): ?int
    {
        return $this->oldId;
    }

    public function setOldId(?int $oldId): Contract
    {
        $this->oldId = $oldId;
        return $this;
    }
}
