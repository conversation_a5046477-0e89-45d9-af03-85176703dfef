<?php

namespace App\Tests\Resource;

use App\Document\Profile;
use App\Document\Embedded\EmbeddedClassification;
use App\Document\Category;
use App\Resource\ProfileView;
use App\Resource\MarketClassificationView;
use App\Resource\CategoryView;
use App\Service\CriteriaFilterService;
use PHPUnit\Framework\TestCase;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * @covers \App\Resource\ProfileView
 */
class ProfileViewTest extends TestCase
{
    private $criteriaFilterServiceMock;
    private $profileMock;
    private $classificationMock;
    private $categoryMock;

    protected function setUp(): void
    {
        $this->criteriaFilterServiceMock = $this->createMock(CriteriaFilterService::class);
        $this->profileMock = $this->createMock(Profile::class);
        $this->classificationMock = $this->createMock(EmbeddedClassification::class);
        $this->categoryMock = $this->createMock(Category::class);

        $this->profileMock->method('getClassification')->willReturn($this->classificationMock);
        $this->profileMock->method('getImages')->willReturn(new ArrayCollection());

        $this->profileMock->method('getAdditionalClassifications')->willReturn([]);
        $this->profileMock->method('getAgreementID')->willReturn(null);
        $this->profileMock->method('getValue')->willReturn(null);
        $this->profileMock->method('getUnit')->willReturn(null);

        $this->classificationMock->method('getId')->willReturn('test-class-id');
        $this->classificationMock->method('getDescription')->willReturn('Test Classification');
        $this->classificationMock->method('getScheme')->willReturn('CPV');

        $this->categoryMock->method('getClassification')->willReturn($this->classificationMock);
        $this->categoryMock->method('getImages')->willReturn(new ArrayCollection());
        $this->categoryMock->method('getCriteria')->willReturn([]);
        $this->categoryMock->method('getAgreementID')->willReturn(null);
    }

    public function testConstructorFiltersCriteria(): void
    {
        $unfilteredCriteria = [
            ['requirementGroups' => [['requirements' => [['id' => 'p_req1', 'isArchived' => true], ['id' => 'p_req2', 'isArchived' => false]]]]]
        ];
        $expectedFilteredCriteria = [
            ['requirementGroups' => [['requirements' => [['id' => 'p_req2', 'isArchived' => false]]]]]
        ];

        $this->profileMock->method('getCriteria')->willReturn($unfilteredCriteria);

        $this->criteriaFilterServiceMock
            ->expects($this->once())
            ->method('filterArchivedRequirements')
            ->with($this->equalTo($unfilteredCriteria))
            ->willReturn($expectedFilteredCriteria);

        $profileView = new ProfileView(
            profile: $this->profileMock,
            criteriaFilterService: $this->criteriaFilterServiceMock,
            category: null,
            awsCdnUrl: null
        );

        $this->assertEquals($expectedFilteredCriteria, $profileView->criteria);
    }

    public function testConstructorSetsBasicPropertiesAndCategory(): void
    {
        $this->profileMock->method('getId')->willReturn('prof-123');
        $this->profileMock->method('getProzorroId')->willReturn('proz-prof-123');
        $this->profileMock->method('getTitle')->willReturn('Test Profile Title');
        $this->profileMock->method('getStatus')->willReturn('active');

        $this->profileMock->method('getCriteria')->willReturn([]);

        $this->criteriaFilterServiceMock
            ->method('filterArchivedRequirements')
            ->willReturn([]);

        $profileView = new ProfileView(
            profile: $this->profileMock,
            criteriaFilterService: $this->criteriaFilterServiceMock,
            category: $this->categoryMock,
            awsCdnUrl: 'http://example.com'
        );

        $this->assertEquals('prof-123', $profileView->id);
        $this->assertIsArray($profileView->criteria);
        $this->assertInstanceOf(MarketClassificationView::class, $profileView->classification);
        $this->assertInstanceOf(CategoryView::class, $profileView->category);
    }
}
