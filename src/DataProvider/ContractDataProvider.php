<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\ContractManager;
use App\Resource\ContractView;

/**
 * Class ContractDataProvider.
 */
final class ContractDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly ContractManager $contractManager
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->contractManager->find($uriVariables['id']);
        if (!$document || $document->getIsMasked()) {
            return null;
        }

        return new ContractView($document);
    }
}
