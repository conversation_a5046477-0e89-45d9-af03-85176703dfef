<?php

namespace App\Command;

use App\Message\AgreementMessage;
use App\Message\CategoryMessage;
use App\Message\ContractMessage;
use App\Message\InspectionMessage;
use App\Message\MonitoringMessage;
use App\Message\PlanMessage;
use App\Message\ProductMessage;
use App\Message\ProfileMessage;
use App\Message\TenderMessage;
use App\MessageHandler\AgreementHandler;
use App\MessageHandler\CategoryHandler;
use App\MessageHandler\ContractHandler;
use App\MessageHandler\InspectionHandler;
use App\MessageHandler\MonitoringHandler;
use App\MessageHandler\PlanHandler;
use App\MessageHandler\ProductHandler;
use App\MessageHandler\ProfileHandler;
use App\MessageHandler\TenderHandler;
use App\Service\MarketCacheService;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\MongoDBException;
use League\Flysystem\FilesystemOperator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\SerializerInterface;

#[AsCommand(
    name: 'sync:object',
    description: 'Sync one object',
)]
class SyncObjectCommand extends Command
{
    private const CONTRACT = 'contract';
    private const INSPECTION = 'inspection';
    private const MONITORING = 'monitoring';
    private const PLAN = 'plan';
    private const TENDER = 'tender';
    private const CATEGORY = 'category';
    private const PROFILE = 'profile';
    private const PRODUCT = 'product';
    private const AGREEMENT = 'agreement';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly MarketCacheService $marketCacheService,
        private readonly string $prozorroPublicPointUrl,
        private readonly string $prozorroPublicPointPassword,
        private readonly string $prozorroMonitoringUrl,
        private readonly string $prozorroMonitoringAuthName,
        private readonly string $prozorroMonitoringAuthPass,
        private readonly string $prozorroMarketApiLink,
        private readonly string $prozorroMarketApiPassword,
        private readonly FilesystemOperator $marketStorage,
        private readonly MessageBusInterface $messageBus,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function configure()
    {
        $this
            ->addArgument('object', InputArgument::REQUIRED)
            ->addArgument('objectId', InputArgument::REQUIRED)
        ;
    }

    /**
     * @throws \Throwable
     * @throws MongoDBException
     */
    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $object = $input->getArgument('object');
        $objectId = $input->getArgument('objectId');
        $inputOutput = new SymfonyStyle($input, $output);

        $documentManager = $this->documentManager;
        $serializer = $this->serializer;
        $logger = $this->logger;
        $marketStorage = $this->marketStorage;
        $cacheService = $this->marketCacheService;
        $prozorroPublicPointUrl = $this->prozorroPublicPointUrl;
        $prozorroPublicPointPassword = $this->prozorroPublicPointPassword;
        $prozorroMonitoringUrl = $this->prozorroMonitoringUrl;
        $prozorroMonitoringAuthName = $this->prozorroMonitoringAuthName;
        $prozorroMonitoringAuthPass = $this->prozorroMonitoringAuthPass;
        $prozorroMarketApiLink = $this->prozorroMarketApiLink;
        $prozorroMarketApiPassword = $this->prozorroMarketApiPassword;
        $messageBus = $this->messageBus;

        switch ($object) {
            case self::CONTRACT:
                $contractHandler = new ContractHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);
                $contractHandler(new ContractMessage($objectId));
                break;
            case self::AGREEMENT:
                $contractHandler = new AgreementHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);
                $contractHandler(new AgreementMessage($objectId));
                break;
            case self::INSPECTION:
                $inspectHandler = new InspectionHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus, $prozorroMonitoringUrl, $prozorroMonitoringAuthName, $prozorroMonitoringAuthPass);
                $inspectHandler(new InspectionMessage($objectId));
                break;
            case self::MONITORING:
                $monitorHandler = new MonitoringHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus, $prozorroMonitoringUrl, $prozorroMonitoringAuthName, $prozorroMonitoringAuthPass);
                $monitorHandler(new MonitoringMessage($objectId));
                break;
            case self::PLAN:
                $planHandler = new PlanHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);
                $planHandler(new PlanMessage($objectId));
                break;
            case self::TENDER:
                $tenderHandler = new TenderHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);
                $tenderHandler(new TenderMessage($objectId));
                break;
            case self::CATEGORY:
                $categoryHandler = new CategoryHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $this->prozorroMarketApiLink, $this->prozorroMarketApiPassword, $this->marketStorage, $messageBus);
                $categoryHandler(new CategoryMessage($objectId));
                break;
            case self::PROFILE:
                $profileHandler = new ProfileHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $this->prozorroMarketApiLink, $this->prozorroMarketApiPassword, $this->marketStorage, $messageBus);
                $profileHandler(new ProfileMessage($objectId));
                break;
            case self::PRODUCT:
                $productHandler = new ProductHandler($documentManager, $serializer, $logger, $cacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $this->prozorroMarketApiLink, $this->prozorroMarketApiPassword, $this->marketStorage, $messageBus);
                $productHandler(new ProductMessage($objectId));
                break;
            default:
                $inputOutput->error('You should use contract/inspection/monitoring/plan/tender id');

                return 0;
        }

        return Command::SUCCESS;
    }
}
