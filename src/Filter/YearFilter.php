<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

/**
 * Class YearFilter.
 */
final class YearFilter extends AbstractSearchFilter
{
    /**
     * @param \DateTimeInterface|null $from
     * @param \DateTimeInterface|null $to
     */
    public function getRangeQuery(string $property, $from = null, $to = null): array
    {
        $query = [];
        if ($from) {
            $query['gte'] = $from->format(\DATE_ATOM);
        }
        if ($to) {
            $query['lte'] = $to->format(\DATE_ATOM);
        }

        return [
            'range' => [
                $property => $query,
            ],
        ];
    }

    /**
     * @todo update? remove? replace with getRangeQuery?
     */
    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        return [];
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];
        foreach ($this->getProperties(self::class) as $property) {
            $description[$property.''] = [
                'property' => $property,
                'type' => 'date',
                'required' => false,
            ];
        }

        return $description;
    }

    /**
     * @throws \Exception
     */
    public function apply(array $clauseBody, string $resourceClass, ?Operation $operationName = null, array $context = []): array
    {
        $searches = [];
        $ranges = [];
        foreach ($context['filters'] ?? [] as $property => $value) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }

            $year = (new \DateTime($value))->format('Y');

            $ranges[$property] = [
                'from' => $year.'-01-01T00:00:00',
                'to' => $year.'-12-31T23:59:59',
            ];
        }

        foreach ($ranges as $rangeField => $rangeKeys) {
            $searchQuery = $this->getRangeQuery(
                $rangeField,
                new \DateTime(str_replace(' ', '+', trim($rangeKeys['from']))),
                new \DateTime(str_replace(' ', '+', trim($rangeKeys['to'])))
            );

            $searches[] = $searchQuery;
        }

        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);

        return $query;
    }
}
