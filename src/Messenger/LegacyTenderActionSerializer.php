<?php

namespace App\Messenger;

use App\Legacy\Message\LegacyTenderActionMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class LegacyTenderActionSerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $LegacyTenderActionMessage = new LegacyTenderActionMessage($data['action'], $data['tenderId'], $data['userId']);

        return new Envelope($LegacyTenderActionMessage);
    }

    public function encode(Envelope $envelope): array
    {
    }
}
