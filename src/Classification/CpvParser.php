<?php

namespace App\Classification;

use App\Document\Classification;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class CpvParser.
 */
class CpvParser extends ClassificationParser
{
    public static $LANG_UK = 'uk';

    public static $LANG_EN = 'en';

    protected string $scheme = 'cpv';

    public function parse(array $urls): void
    {
        $url = $urls[self::$LANG_UK];

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Starting with '.$this->scheme);
        }
        try {
            $response = $this->client->request('GET', $url);
        } catch (GuzzleException $exception) {
            $this->logger->error($exception->getMessage(), [$exception]);
            throw $exception;
        }

        $result = json_decode((string) $response->getBody(), true);

        $resultEn = [];
        try {
            if (null !== $this->inputOutput) {
                $this->inputOutput->comment('Starting with lang '.self::$LANG_EN);
            }
            $responseEn = $this->client->request('GET', $urls[self::$LANG_EN]);
            $resultEn = json_decode((string) $responseEn->getBody(), true);
        } catch (GuzzleException $exception) {
        }

        $totalDocuments = count($result);
        if (null !== $this->inputOutput) {
            $this->inputOutput->comment($totalDocuments.' items found...');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsById = [];
        foreach ($result as $id => $description) {
            $document = $this->manager->find($id);
            $document = $document ? $document : new Classification();
            $document->setScheme('CPV');
            $document->setId($id);
            $document->setDescription($description);
            $document->setSelectable(true);

            if (array_key_exists($id, $resultEn)) {
                $document->setDescriptionEn($resultEn[$id]);
            }

            $idIdx = explode('-', $document->getId())[0];
            $documentsById[$idIdx] = $document;
        }

        if (null !== $this->inputOutput) {
            $this->inputOutput->progressFinish();
            $this->inputOutput->comment('Sorting ...');
        }

        uasort(
            $documentsById,
            function ($a, $b) {
                $id1 = intval(explode('-', $a->getId())[0]);
                $id2 = intval(explode('-', $b->getId())[0]);
                if ($id1 === $id2) {
                    return 0;
                }

                return ($id1 > $id2) ? 1 : -1;
            }
        );

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Building document tree');
            $this->inputOutput->progressStart($totalDocuments);
        }

        foreach ($documentsById as $document) {
            $this->setParent($document, $documentsById);
        }

        if (null !== $this->inputOutput) {
            $this->inputOutput->progressFinish();
            $this->inputOutput->comment('Storing to database');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsCount = 0;
        foreach ($documentsById as $document) {
            ++$documentsCount;
            $needFlush = (0 === $documentsCount % 50) || ($documentsCount === $totalDocuments);

            $this->manager->save($document, $needFlush);
            if (null !== $this->inputOutput) {
                $this->inputOutput->progressAdvance(1);
            }
        }
    }

    /**
     * @param Classification[] $documentsById
     */
    private function setParent(Classification $document, array $documentsById): void
    {
        $idIdx = explode('-', $document->getId())[0];
        $parentIdx = $idIdx;
        for ($i = strlen($parentIdx) - 1; $i > 1; --$i) {
            if ('0' === $parentIdx[$i]) {
                continue;
            }
            $parentIdx[$i] = '0';
            if (array_key_exists($parentIdx, $documentsById)) {
                $parentDocument = $documentsById[$parentIdx];
                $parentDocument->setChildren(true);
                $document->setParentId($parentDocument->getId());
                $document->setTreeLevel($parentDocument->getTreeLevel() + 1);
                break;
            }
        }
    }
}
