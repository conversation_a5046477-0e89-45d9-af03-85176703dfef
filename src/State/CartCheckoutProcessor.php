<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Document\CartItem;
use App\Document\Profile;
use App\Dto\LegacyTenderRedirectResponseDto;
use App\Legacy\Dto\LegacyTenderDto;
use App\Legacy\Repository\LegacyTenderRepository;
use App\Legacy\Repository\LegacyUserRepository;
use App\Manager\CartItemManager;
use App\Manager\CartManager;
use App\Manager\CategoryManager;
use App\Manager\ProductManager;
use App\Manager\ProfileManager;
use App\Security\JwtCheckerTrait;
use App\Service\ProfileCheckService;
use DateTimeZone;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Service\CriteriaFilterService;
use App\Constants\DzoConstants;

readonly class CartCheckoutProcessor implements ProcessorInterface
{
    use JwtCheckerTrait;

    private CriteriaFilterService $criteriaFilterService;

    private const LOCALIZATION_TITLE = DzoConstants::LOCALIZATION_TITLE;

    private const LOCALIZATION_TITLE_COUNTRIES = DzoConstants::LOCALIZATION_TITLE_COUNTRIES;

    private const TECH_FEATURES_CLASSIFICATION_ID = DzoConstants::TECH_FEATURES_CLASSIFICATION_ID;

    public function __construct(
        private ProfileManager $profileManager,
        private CartManager $cartManager,
        private CartItemManager $cartItemManager,
        private ProductManager $productManager,
        private ProfileCheckService $profileCheckService,
        private LegacyUserRepository $legacyUserRepository,
        private LegacyTenderRepository $legacyTenderRepository,
        private CategoryManager $categoryManager,
        CriteriaFilterService $criteriaFilterService,
    ) {
        $this->criteriaFilterService = $criteriaFilterService;
    }

    public function process(
        mixed $data,
        Operation $operation,
        array $uriVariables = [],
        array $context = []
    ): ?LegacyTenderRedirectResponseDto {
        if (!$data) {
            return null;
        }

        if (!$data->items || count($data->items) === 0) {
            throw new NotFoundHttpException('Items is empty');
        }

        if (!$userId = $this->checkJwt()) {
            return null;
        }
        if (!$user = $this->legacyUserRepository->getUserByNewId($userId)) {
            return null;
        }

        $cartItems = $this->cartItemManager->getRepository()->findByIds($data->items);
        $tenderTitles = $tenderDescriptions = $cpvIds = $cpvDescriptions = $productConfigs = [];

        $fullData = [
            'data' => [
                'procurementMethod'          => 'selective',
                'procurementMethodType'      => 'priceQuotation',
                'procurementMethodRationale' => null,
                'items'                      => [],
                'title_en'                   => '',
                'title_ru'                   => '',
                'description_en'             => '',
                'description_ru'             => '',
                'value'                      => [
                    'currency'              => 'UAH',
                    'amount'                => '',
                    'valueAddedTaxIncluded' => 'true',
                ],
                'mainProcurementCategory'    => 'goods',
                'criteria'                   => [],
                'guarantee'                  => ['amount' => ''],
                'tenderPeriod'               => ['endDate' => '', 'startDate' => null],
                'config'                     => [],
                'awardCriteria'              => 'lowestCost',
                'funders'                    => [],
                'features'                   => [],
                'enquieryPeriod'             => ['endData' => null],
                'procuringEntity'            => [
                    'kind'         => $user['procuringEntity_kind'],
                    'name'         => $user['procuringEntity_name'],
                    'name_en'      => $user['procuringEntity_name_en'],
                    'name_ru'      => $user['procuringEntity_name_ru'],
                    'identifier'   => [
                        'scheme'       => $user['procuringEntity_identifier_scheme'],
                        'id'           => $user['procuringEntity_identifier_id'],
                        'legalName'    => $user['procuringEntity_name'],
                        'legalName_en' => $user['procuringEntity_name_en'],
                        'legalName_ru' => $user['procuringEntity_name_ru'],
                    ],
                    'address'      => [
                        'locality'      => $user['procuringEntity_address_locality'],
                        'streetAddress' => $user['procuringEntity_address_streetAddress'],
                        'postalCode'    => $user['procuringEntity_address_postalCode'],
                        'countryName'   => $user['procuringEntity_address_countryName'],
                        'region'        => $user['procuringEntity_address_region'],
                    ],
                    'contactPoint' => [
                        'name'      => $user['procuringEntity_contactPoint_name'],
                        'name_en'   => $user['procuringEntity_contactPoint_name_en'],
                        'name_ru'   => $user['procuringEntity_contactPoint_name_ru'],
                        'telephone' => $user['procuringEntity_contactPoint_telephone'],
                        'url'       => $user['procuringEntity_contactPoint_url'],
                        'email'     => $user['procuringEntity_contactPoint_email'],
                        'faxNumber' => $user['procuringEntity_contactPoint_faxNumber'],
                    ],
                ],
            ],
        ];
        $pubDate = (new \DateTime())->setTimezone(new DateTimeZone('Europe/Kiev'));

        $newTender = new LegacyTenderDto();
        $newTender
            ->setUserId($user['id'])
            ->setProcuringEntityName($user['procuringEntity_name'] ?? $user['login'])
            ->setProcuringEntityNameEn($user['procuringEntity_name_en'])
            ->setProcuringEntityNameRu($user['procuringEntity_name_ru'])
            ->setProcuringEntityIdentifierId($user['procuringEntity_identifier_id'])
            ->setRegionId($user['region_id'])
            ->setCreatorId($user['id'])
            ->setCreatorUserId($user['id'])
            ->setTestMode($user['test_mode'])
            ->setPubDate($pubDate);

        $category = null;
        $dynamicCriteria = [];
        /** @var CartItem $cartItem */
        foreach ($cartItems as $cartItem) {
            $document = $type = null;
            if ($cartItem->getProfileId()) {
                $type = 'profile';
                $document = $this->profileManager->find($cartItem->getProfileId());
            }

            if ($cartItem->getCategoryId()) {
                $type = 'category';
                $document = $this->categoryManager->find($cartItem->getCategoryId());
            }

            /** @var Profile $profile */
            if ($document) {
                $itemId = md5(uniqid(rand(), true));
                if (!$category) {
                    $tenderTitles[] = $document->getTitle();
                }
                $tenderDescriptions[] = $document->getDescription();
                $cpvId = $document->getClassification()->getScheme() . '_' . $document->getClassification()->getId();
                $cpvIds[] = $document->getClassification()->getId();
                $cpvDescriptions[] = $document->getClassification()->getDescription();
                $profileCriteria = null;
                foreach ($document->getCriteria() as $criterion) {
                    if ($this->checkIfCriterionTechFeatures($criterion)) {
                        $profileCriteria = $criterion;
                        break;
                    }
                }
                // fallback
                if ($profileCriteria === null) {
                    $profileCriteria = $document->getCriteria()[0];
                }
                $criteriaArray = [
                    'title'             => 'Технічні характеристики предмета закупівлі',
                    'description'       => $document->getTitle(),
                    'relatesTo'         => 'item',
                    'relatedItem'       => $itemId,
                    'legislation'       => $profileCriteria['legislation'] ?? null,
                    'source'            => $profileCriteria['source'] ?? null,
                    'classification'    => $profileCriteria['classification'] ?? null,
                    'requirementGroups' => [
                        [
                            'description'  => 'Технічні характеристики',
                            'requirements' => [],
                        ]
                    ],
                ];

                $localizedItem = false;
                if ($type == 'profile' && 'active' == $document->getStatus()) {
                    foreach ($this->getRequirements($document, $type) as $requirementIterator) {
                        $criteriaArray['requirementGroups'][0]['requirements'][] = iterator_to_array(
                            $requirementIterator
                        );
                    }
                } else {
                    foreach ($cartItem->getConfig() as $config) {
                        $productConfigs[$cartItem->getId()][] = $config;
                        if ('Наявність локалізації' === ($config['title'] ?? '') && !empty($config['expectedValue'])) {
                            $localizedItem = true;
                        }
                        foreach ($this->getRequirements($document, $type, $config) as $requirementIterator) {
                            $requirement = iterator_to_array($requirementIterator);
                            if (in_array(
                                    $requirement['title'],
                                    array_column($profileCriteria['requirementGroups'][0]['requirements'], 'title')
                                ) !== false) {
                                $requirement['id'] = md5(uniqid(rand(), true));
                                $criteriaArray['requirementGroups'][0]['requirements'][] = $requirement;
                                if (isset($requirement['expectedValues'])) {
                                    $dynamicCriteria[$requirement['id']] = $requirement['expectedValues'];
                                }

                                if (isset($requirement['minValue']) && isset($requirement['maxValue'])) {
                                    $dynamicCriteria[$requirement['id']] = [
                                        'min' => $requirement['minValue'],
                                        'max' => $requirement['maxValue'],
                                    ];
                                }
                            }
                        }
                    }
                }

                if ($type == 'category') {
                    $criteriaArray = $this->criteriaFilterService->filterArchivedRequirements($criteriaArray);

                }

                $fullData['data']['criteria'][] = $criteriaArray;
                if (!empty($localization)) {
                    $fullData['data']['criteria'][] = $this->addLocalizationCriteria(
                        $document->getCriteria(),
                        $itemId,
                        $localization
                    );
                }

                $fullData['data']['agreement']['id'] = $document->getAgreementID();
                $unitId = null;
                if ($document->getUnit()) {
                    $unitId = $this->legacyTenderRepository->findUnitId($document->getUnit()->getCode());
                }

                $item = [
                    'description'      => $document->getTitle(),
                    'description_en'   => '',
                    'description_ru'   => '',
                    'relatedLot'       => '',
                    'id'               => $itemId,
                    'category'         => $type == 'profile' ? $document->getRelatedCategory(
                    ) : $document->getProzorroId(),
                    'profile'          => $type == 'profile' ? $document->getProzorroId() : null,
                    'quantity'         => $cartItem->getQuantity(),
                    'unit_id'          => $unitId,
                    'cpv_id'           => $cpvId,
                    'cpv_description'  => $document->getClassification()->getDescription(),
                    'dkpp_id'          => '',
                    'dkpp_description' => '',
                    'country_id'       => $user['country_id'],
                    'region_id'        => $user['region_id'],
                    'deliveryAddress'  => [
                        'locality'      => $user['procuringEntity_address_locality'],
                        'streetAddress' => $user['procuringEntity_address_streetAddress'],
                        'postalCode'    => $user['procuringEntity_address_postalCode'],
                        'countryName'   => $user['procuringEntity_address_countryName'],
                        'region'        => $user['procuringEntity_address_region'],
                    ],
                    'deliveryDate'     => ['startDate' => '', 'endDate' => ''],
                    'classification'   => [
                        'description' => $document->getClassification()->getDescription(),
                        'id'          => $document->getClassification()->getId(),
                        'scheme'      => $document->getClassification()->getScheme(),
                    ],
                    'unit'             => [
                        'code' => $document->getUnit()->getCode(),
                        'name' => $document->getUnit()->getName(),
                    ],
                ];

                if ($localizedItem) {
                    $item['local_origin_level'] = ['on' => [1]];
                }

                $fullData['data']['items'][] = $item;
            }
        }
        $title = implode(', ', $tenderTitles);
        $description = implode(', ', $tenderDescriptions);
        $fullData['data']['title'] = $title;
        $fullData['data']['description'] = $description;
        $post = $fullData;
        if (!empty($dynamicCriteria)) {
            $post['dynamic_criteria_value'] = $dynamicCriteria;
        }

        $post['tender_method'] = 'selective_priceQuotation';
        $post['tender_type'] = 'simple';
        $post['do'] = 'save';

        $post['productConfigs'] = $productConfigs;

        $newTender
            ->setTitle($title)
            ->setDescription($description)
            ->setCpvId($cpvIds[0])
            ->setCpvDescription($cpvDescriptions[0])
            ->setFullData(json_encode($fullData))
            ->setPost(json_encode($post));

        try {
            if ($tenderId = $this->legacyTenderRepository->createPriceQuotingTender($newTender)) {
                $this->cartItemManager->removeCollection($cartItems);
            }
        } catch (\Exception $exception) {
            return null;
        }

        return new LegacyTenderRedirectResponseDto('/tenders/' . $tenderId);
    }

    private function getRequirements(object $document, string $type, ?array $config = null): \Iterator
    {
        //@todo refactor
        foreach ($document->getCriteria() as $criteria) {
            // add only tech features here for profile
            if ($type === 'profile' && !$this->checkIfCriterionTechFeatures($criteria)) {
                continue;
            }

            foreach ($criteria['requirementGroups'] as $requirementGroup) {
                foreach ($requirementGroup['requirements'] as $key => $requirement) {
                    if ((($type == 'profile' && 'general' == $document->getStatus()) || $type == 'category') &&
                        !empty($config) && $requirement['title'] == $config['title']) {
                        $valuesCount = isset($config['expectedValues']) ? count($config['expectedValues']) : 0;
                        if ($config['dataType'] == 'boolean' && $valuesCount > 1) {
                            continue;
                        }

                        if (isset($config['expectedValues']) && $valuesCount == 1 && $config['dataType'] !== 'text') {
                            $requirement['expectedValue'] = $config['expectedValues'][$valuesCount - 1];
                            unset($requirement['expectedValues']);
                        }

                        if (isset($config['expectedValues']) && $config['dataType'] == 'text') {
                            $requirement['expectedValues'] = $config['expectedValues'];
                            unset($requirement['expectedValue']);
                        }

                        if (isset($config['expectedValues']) && $valuesCount > 1) {
                            $requirement['expectedValues'] = $config['expectedValues'];
                        }

                        if (isset($config['minValue'])) {
                            $requirement['minValue'] = $config['minValue'];
                        } else {
                            unset($requirement['minValue']);
                        }

                        if (isset($config['maxValue'])) {
                            $requirement['maxValue'] = $config['maxValue'];
                        } else {
                            unset($requirement['maxValue']);
                        }

                        yield $requirement;
                    } elseif ($type == 'profile' && 'active' == $document->getStatus()) {
                        yield $requirement;
                    }
                }
            }
        }
    }

    private function addLocalizationCriteria(
        array $localizationCriteria,
        string $itemId,
        array $localizationRequirements
    ) {
        $localCrt = [];
        foreach ($localizationCriteria as $crt) {
            if ($crt['title'] == 'Створення передумов для сталого розвитку та модернізації вітчизняної промисловості') {
                $localCrt = $crt;
                break;
            }
        }

        $localizationCriteria = [
            'title'             => 'Створення передумов для сталого розвитку та модернізації вітчизняної промисловості',
            'description'       => $localCrt['description'],
            'relatesTo'         => 'item',
            'relatedItem'       => $itemId,
            'legislation'       => $localCrt['legislation'] ?? null,
            'source'            => $localCrt['source'] ?? null,
            'classification'    => $localCrt['classification'] ?? null,
            'requirementGroups' => [
            ],
        ];

        if (isset($localizationRequirements[self::LOCALIZATION_TITLE])) {
            $localizationCriteria['requirementGroups'][] = $localCrt['requirementGroups'][0];
        }

        if (isset($localizationRequirements[self::LOCALIZATION_TITLE_COUNTRIES])) {
            $localizationCriteria['requirementGroups'][] = $localCrt['requirementGroups'][1];
        }

        return $localizationCriteria;
    }

    private function checkIfCriterionTechFeatures(array $criterion): bool
    {
        return array_key_exists('classification', $criterion) && is_array($criterion['classification'])
            && array_key_exists('id', $criterion['classification'])
            && $criterion['classification']['id'] === CartCheckoutProcessor::TECH_FEATURES_CLASSIFICATION_ID;
    }
}
