<?php

namespace App\Document\Embedded;

use App\Document\BaseIdentifier;
use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class EmbeddedIdentifier extends BaseIdentifier
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    public function getId(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
