<?php

namespace App\Legacy\Repository;

use App\Legacy\Helper\DatabaseHelper;

class TenderBidsRepository extends LegacyRepository
{
    public function tenderBids()
    {
        $query = $this->connection->prepare(
            'SELECT user_id, tender_api_id FROM ' . DatabaseHelper::getTableName('tender_bids')
        );

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function tenderBidsBy(string $userOldId, string $tenderId)
    {
        $query = $this->connection->prepare(
            'SELECT user_id, tender_api_id 
            FROM ' . DatabaseHelper::getTableName('tender_bids') . ' AS bids ' .
            'WHERE bids.user_id = :userOldId AND bids.tender_api_id = :tenderId'
        );

        $query->bindValue('userOldId', $userOldId);
        $query->bindValue('tenderId', $tenderId);

        return $query->executeQuery()->fetchOne();
    }

    public function getBids(int $start, int $limit): false|array
    {
        $query = 'SELECT tb.id, tb.tender_api_id, tb.api_id, tb.pay, tb.signed, ucd.new_user_id, tbv.amount, tbv.value_currency, tbv.value_valueAddedTaxIncluded FROM ' . DatabaseHelper::getTableName(
                'tender_bids'
            ) . ' AS tb ' .
            'LEFT JOIN ' . DatabaseHelper::getTableName('users_new_dzo') . ' AS ucd ON ucd.user_id = tb.user_id ' .
            'LEFT JOIN ' . DatabaseHelper::getTableName('tender_bids_values') . ' AS tbv ON tb.id = tbv.bid_id ' .
            'WHERE tb.id >= :start ORDER BY tb.id ASC LIMIT ' . $limit;


        $query = $this->connection->prepare($query);

        $query->bindValue('start', $start);

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function getBidById(int $bidId)
    {
        $query = 'SELECT tb.id, tb.tender_api_id, tb.api_id, tb.pay, tb.signed, ucd.new_user_id, tbv.amount, tbv.value_currency, tbv.value_valueAddedTaxIncluded FROM ' . DatabaseHelper::getTableName(
                'tender_bids'
            ) . ' AS tb ' .
            'LEFT JOIN ' . DatabaseHelper::getTableName('users_new_dzo') . ' AS ucd ON ucd.user_id = tb.user_id ' .
            'LEFT JOIN ' . DatabaseHelper::getTableName('tender_bids_values') . ' AS tbv ON tb.id = tbv.bid_id ' .
            'WHERE tb.id >= :id';


        $query = $this->connection->prepare($query);

        $query->bindValue('id', $bidId);

        return $query->executeQuery()->fetchAssociative();
    }
}
