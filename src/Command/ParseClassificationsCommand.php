<?php

namespace App\Command;

use App\Classification\CcceParser;
use App\Classification\CpvParser;
use App\Classification\GmdnParser;
use App\Classification\InnParser;
use App\Classification\RoadsParser;
use App\Manager\ClassificationManager;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Populate with classifications from remote repo
 * Class ClassifiersFromFileCommand.
 */
class ParseClassificationsCommand extends Command
{
    protected static $defaultName = 'api:classifications:parse';

    private array $classificationSources;

    protected function configure()
    {
        $this
            ->setDescription('Update classifications in database')
            ->addArgument('scheme', InputArgument::REQUIRED, "scheme ['CPV']");
    }

    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * ApiClassifiersSyncListCommand constructor.
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ClassificationManager $manager,
        array $classificationSources,
        private Client $client = new Client()
    ) {
        parent::__construct(self::$defaultName);
        $this->classificationSources = $classificationSources;
    }

    /**
     * @throws GuzzleException
     * @throws MappingException
     * @throws MongoDBException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $inputOutput = new SymfonyStyle($input, $output);

        $scheme = $input->getArgument('scheme');

        switch ($scheme) {
            case 'cpv':
                $scheme = 'cpv_'.CpvParser::$LANG_UK;

                $url = array_key_exists($scheme, $this->classificationSources) ? $this->classificationSources[$scheme] : null;
                if (null === $url) {
                    $inputOutput->error('No CPV data found!');
                }

                $urls = [CpvParser::$LANG_UK => $url];

                $scheme = 'cpv_'.CpvParser::$LANG_EN;
                if (array_key_exists($scheme, $this->classificationSources)) {
                    $urls[CpvParser::$LANG_EN] = $this->classificationSources[$scheme];
                }

                $parser = new CpvParser($this->logger, $this->manager, $inputOutput);
                $parser->parse($urls);

                break;
            case 'gmdn':
                $url = array_key_exists($scheme, $this->classificationSources) ? $this->classificationSources[$scheme] : null;

                if (null === $url) {
                    $inputOutput->error('No gmdn data found!');
                }

                $parser = new GmdnParser($this->logger, $this->manager, $inputOutput);
                $parser->parse([$url]);

                break;
            case 'ccce_ua':
                $url = array_key_exists($scheme, $this->classificationSources) ? $this->classificationSources[$scheme] : null;

                if (null === $url) {
                    $inputOutput->error('No ccce_ua data found!');
                }

                $parser = new CcceParser($this->logger, $this->manager, $inputOutput);
                $parser->parse([$url]);

                break;
            case 'inn':
                $url = array_key_exists($scheme, $this->classificationSources) ? $this->classificationSources[$scheme] : null;

                if (null === $url) {
                    $inputOutput->error('No inn data found!');
                }

                $parser = new InnParser($this->logger, $this->manager, $inputOutput);
                $parser->parse([$url]);

                break;
            case 'roads':
                $url = array_key_exists($scheme, $this->classificationSources) ? $this->classificationSources[$scheme] : null;

                if (null === $url) {
                    $inputOutput->error('No roads data found!');
                }

                $parser = new RoadsParser($this->logger, $this->manager, $inputOutput);
                $parser->parse([$url]);
                break;
            default:
                $inputOutput->error('Wrong scheme!');

                return 0;
        }
        $this->manager->getDocumentManager()->flush();
        $inputOutput->text('done!');

        return 0;
    }
}
