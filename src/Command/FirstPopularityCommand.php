<?php

namespace App\Command;

use App\Message\PopularityMessage;
use App\Repository\TenderRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:popularity:queue',
    description: 'Start popularity for 30 days',
)]
class FirstPopularityCommand extends Command
{
    private const FIRST = 'first';
    private const INCREASE = 'increase';
    private const DECREASE = 'decrease';

    public function __construct(
        private readonly TenderRepository $tenderRepository,
        private readonly MessageBusInterface $messageBus,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function configure()
    {
        $this
            ->addArgument('mode', InputArgument::REQUIRED, 'Type of document to fix dateCreated field: plan, contract or tender');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $mode = $input->getArgument('mode');
        if (!in_array($mode, [self::FIRST, self::INCREASE, self::DECREASE])) {
            throw new \InvalidArgumentException('Incorrect mode');
        }
        if ($mode === self::FIRST) {
            $pqTenders = $this->tenderRepository->findPqTenders();
            $increase = true;
        } elseif ($mode === self::DECREASE) {
            $pqTenders = $this->tenderRepository->findPq30dTenders();
            $increase = false;
        } else {
            $pqTenders = $this->tenderRepository->findPqYesterdayTenders();
            $increase = true;
        }

        foreach ($pqTenders as $tender) {
            $this->messageBus->dispatch(new PopularityMessage($tender->getId(), $increase));
        }

        return Command::SUCCESS;
    }
}
