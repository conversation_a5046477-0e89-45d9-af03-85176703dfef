<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document]
#[MongoDB\InheritanceType('COLLECTION_PER_CLASS')]
class Classification extends BaseClassification
{
    /**
     * We need this special property, because by default API Platform generates get url using $id property
     * And here $id property can contain wrong characters. Also we don't need single item get for classification.
     * So instead we should provide $iri variable.
     */
    protected string $iri = 'iri_for_api_platfrom';

    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $descriptionEn = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $name = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $nameEn = null;

    #[MongoDB\Field(type: Type::STRING)]
    protected string $parentId = '';

    #[MongoDB\Field(type: Type::INT)]
    protected int $treeLevel = 0;

    #[MongoDB\Field(type: Type::BOOL)]
    protected bool $selectable = false;

    #[MongoDB\Field(type: Type::BOOL)]
    protected bool $children = false;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getNameEn(): ?string
    {
        return $this->nameEn;
    }

    public function setNameEn(?string $nameEn): void
    {
        $this->nameEn = $nameEn;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function setDescriptionEn(?string $descriptionEn = null): void
    {
        $this->descriptionEn = $descriptionEn;
    }

    public function getParentId(): string
    {
        return $this->parentId;
    }

    public function setParentId(string $parentId): void
    {
        $this->parentId = $parentId;
    }

    public function getTreeLevel(): int
    {
        return $this->treeLevel;
    }

    public function setTreeLevel(int $treeLevel): void
    {
        $this->treeLevel = $treeLevel;
    }

    public function getSelectable(): bool
    {
        return $this->selectable;
    }

    public function setSelectable(bool $selectable): void
    {
        $this->selectable = $selectable;
    }

    public function getChildren(): bool
    {
        return $this->children;
    }

    public function setChildren(bool $children): void
    {
        $this->children = $children;
    }

    public function getIri(): string
    {
        return $this->iri;
    }

    public function getSearchId(): ?string
    {
        return $this->id;
    }
}
