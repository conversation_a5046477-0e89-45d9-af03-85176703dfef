<?php

namespace App\Manager;

use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

/**
 * Class AbstractManager.
 */
abstract class AbstractManager
{
    abstract public function getRepository(): DocumentRepository;

    abstract public function getDocumentManager(): DocumentManager;

    /**
     * @return object|null
     *
     * @throws LockException
     * @throws MappingException
     */
    public function find($id)
    {
        return $this->getRepository()->find($id);
    }
}
