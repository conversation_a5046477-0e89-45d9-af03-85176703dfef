<?php

namespace App\Manager;

use App\Document\Tender;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use FOS\ElasticaBundle\Finder\FinderInterface;

/**
 * Class TenderManager.
 */
class TenderManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;


    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    /**
     * TenderManager constructor.
     */
    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly FinderInterface $tendersFinder,
    ) {
        $this->repository = $documentManager->getRepository(Tender::class);
    }

    /**
     * @return Tender|null
     *
     * @throws LockException
     * @throws MappingException
     */
    public function find($id)
    {
        return $this->getRepository()->find($id);
    }

    public function getLastOneMonitoredDocument(): ?Tender
    {
        $objects = $this->getRepository()->findBy([], ['monitoringDateModified' => 'DESC'], 1);

        return array_pop($objects);
    }

    public function findSendedBids(string $userId)
    {
        $query = [
            'query' => [
                "nested" => [
                    "path"  => "dzoData",
                    "query" => [
                        "nested" => [
                            "path"  => "dzoData.bids",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["match" => ["dzoData.bids.userId" => $userId]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
        ];

        return $this->tendersFinder->findPaginated($query);
    }
}
