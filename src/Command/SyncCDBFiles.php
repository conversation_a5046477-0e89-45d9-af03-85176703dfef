<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:sync:cdb-files',
    description: 'Sync json files from CDB',
)]
class SyncCDBFiles extends Command
{
    public function __construct(
        private readonly array $cdbFilesData,
        private readonly string $listsDir,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        foreach ($this->cdbFilesData as $fileData) {
            $jsonString = file_get_contents($fileData['url']);

            file_put_contents($this->listsDir.'/'.$fileData['name'], $jsonString);
        }

        return Command::SUCCESS;
    }
}
