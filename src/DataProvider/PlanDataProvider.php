<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\PlanManager;
use App\Resource\PlanView;

/**
 * Class PlanDataProvider.
 */
final class PlanDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly PlanManager $planManager
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->planManager->find($uriVariables['id']);
        if (!$document || $document->getIsMasked()) {
            return null;
        }

        return new PlanView($document);
    }
}
