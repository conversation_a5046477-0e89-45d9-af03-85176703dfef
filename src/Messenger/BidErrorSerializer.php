<?php

namespace App\Messenger;

use App\Message\BidErrorMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class BidErrorSerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('Invalid JSON: ' . $e->getMessage());
        }

        if (!isset($data['id']) || !isset($data['message'])) {
            throw new MessageDecodingFailedException('Fields "id" and/or "message" are missing.');
        }

        $item = new BidErrorMessage($data['id'], $data['message']);

        return new Envelope($item);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();

        if ($message instanceof BidErrorMessage) {
            $data = [
                'id' => $message->getId(),
                'message' => $message->getMessage()
            ];
        } else {
            throw new \Exception('Unsupported message: ' . get_class($message));
        }

        return [
            'body' => json_encode($data, JSON_THROW_ON_ERROR),
            'headers' => [],
        ];
    }
}
