<?php

namespace App\Manager;

use App\Document\Category;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

class CategoryManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    /**
     * PlanManager constructor.
     */
    public function __construct(private readonly DocumentManager $documentManager)
    {
        $this->repository = $documentManager->getRepository(Category::class);
    }

    /**
     * @return Category|null
     *
     * @throws LockException
     * @throws MappingException
     */
    public function find($id)
    {
        return $this->getRepository()->find($id);
    }
}
