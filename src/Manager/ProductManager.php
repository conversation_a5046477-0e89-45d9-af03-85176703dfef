<?php

namespace App\Manager;

use App\Constants\DzoConstants;
use App\Document\Product;
use App\Dto\CartItemConfigDto;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Elastica\Client;
use FOS\ElasticaBundle\Finder\FinderInterface;

class ProductManager extends AbstractManager
{
    use ManagerTrait;

    private DocumentRepository $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly FinderInterface $productsFinder,
        private readonly Client $client
    ) {
        $this->repository = $documentManager->getRepository(Product::class);
    }

    public function find($id)
    {
        return $this->getRepository()->find($id);
    }

    public function findByProzorroId(string $prozorroId)
    {
        return $this->getRepository()->findOneBy(['prozorroId' => $prozorroId]);
    }

    public function findProductByRequirements(Collection $requirements, bool $isReturnProducts = false, ?string $categoryId = null): bool|array
    {
        $query = ['query' => ['bool' => ['must' => []]]];

        if ($categoryId) {
            $query['query']['bool']['must'][] = ['term' => ['relatedCategory' => $categoryId]];
        }

        /** @var CartItemConfigDto $requirement */
        foreach ($requirements as $requirement) {
            if ('Наявність локалізації' === $requirement->title && !empty($requirement->expectedValue)) {
                $query['query']['bool']['must'][] = ['term' => ['status' => 'active']];
                $nestedName = 'requirementResponses.classification';
                $mustQuery = [
                    ['match' => ['requirementResponses.classification.id' => DzoConstants::LOCAL_ORIGIN_LEVEL_CLASSIFICATION_ID]],
                ];
            } else {
                $nestedName = 'textRequirements';
                if (in_array($requirement->dataType, ['number', 'integer', 'int'])) {
                    $nestedName = 'numberRequirements';
                } elseif ('boolean' === $requirement->dataType) {
                    $nestedName = 'booleanRequirements';
                }

                $mustQuery = [
                    ['match' => [$nestedName.'.requirement' => $requirement->title]],
                ];

                if ($requirement->expectedValues) {
                    $mustQuery[] = ['terms' => [$nestedName.'.values' => $requirement->expectedValues]];
                }

                if ($requirement->expectedValue) {
                    $mustQuery[] = ['match' => [$nestedName.'.values' => $requirement->expectedValue]];
                }

                if ($requirement->minValue) {
                    $mustQuery[] = ['range' => [$nestedName.'.values' => ['gte' => $requirement->minValue]]];
                }

                if ($requirement->maxValue) {
                    $mustQuery[] = ['range' => [$nestedName.'.values' => ['lte' => $requirement->maxValue]]];
                }
            }

            $nestedQuery = [
                'nested' => [
                    'path' => $nestedName,
                    'query' => [
                        'bool' => [
                            'must' => $mustQuery,
                        ],
                    ],
                ],
            ];

            if (!empty($nestedQuery)) {
                $query['query']['bool']['must'][] = $nestedQuery;
            }
        }

        $products = $this->productsFinder->find($query);

        if ($isReturnProducts) {
            return $products;
        }

        if (count($products) > 0) {
            return true;
        }

        return false;
    }
}
