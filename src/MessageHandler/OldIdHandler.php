<?php

namespace App\MessageHandler;

use App\Document\Contract;
use App\Document\Plan;
use App\Document\Tender;
use App\Message\OldIdMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class OldIdHandler
{
    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger,
        private ObjectPersisterInterface $tenderPersister,
        private ObjectPersisterInterface $contractPersister,
        private ObjectPersisterInterface $planPersister,
    ) {
    }

    public function __invoke(OldIdMessage $message)
    {
        try {
            $documentClass = DocumentType::getDocumentClass($message->type);
            /** @var Plan|Tender|Contract $document */
            if ($document = $this->documentManager->getRepository($documentClass)->find($message->id)) {
                $document->setOldId($message->oldId);
                $this->documentManager->flush();

                if ($message->type === DocumentType::$TENDER) {
                    $this->tenderPersister->replaceOne($document);
                } elseif ($message->type === DocumentType::$CONTRACT) {
                    $this->contractPersister->replaceOne($document);
                } elseif ($message->type === DocumentType::$PLAN) {
                    $this->planPersister->replaceOne($document);
                } else {
                    throw new \Error('Unknown type: ' . $message->type);
                }
            }
        } catch (\Throwable $e) {
            $this->logger->critical('Unhandled Error received: ' . $e->getMessage(), [$this, $e]);
            throw $e;
        }
    }
}
