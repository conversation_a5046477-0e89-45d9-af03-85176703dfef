<?php

namespace App\Legacy\Dto;

class LegacyTenderDto
{
    private int $userId;

    private string $title;

    private string $description;

    private float $valueAmount = 0.0;

    private bool $valueValueAddedTaxIncluded = true;

    private float $minimalStepAmount = 0.0;

    private \DateTime $enquiryPeriodEndDate;

    private \DateTime $tenderPeriodEndDate;

    private ?string $tenderId1 = null;

    private ?string $tenderId2 = null;

    private int $status = 0;

    private string $fullData;

    private \DateTime $modify;

    private \DateTime $dateModified;

    private ?string $procuringEntityName = null;

    private ?string $cpvId = null;

    private ?string $valueCurrency = 'UAH';

    private \DateTime $auctionPeriodStartDate;

    private int $questionsCount = 0;

    private int $questionsAnswered = 0;

    private ?string $cpvDescription;

    private \DateTime $pubDate;

    private int $testMode = 1;

    private ?int $deliveryRegionId;

    private ?int $moneygetId;

    private string $type = 'simple';

    private ?string $post;

    private int $lots = 0;

    private int $features = 0;

    private ?string $titleEn = null;

    private ?string $descriptionEn = null;

    private ?string $procuringEntityNameEn = null;

    private ?string $procuringEntityNameRu = null;

    private ?string $titleRu = null;

    private ?string $descriptionRu = null;

    private ?int $procuringEntityIdentifierId;

    private int $complaintsCount = 0;

    private string $method = 'selective_priceQuotation';

    private int $signed = 0;

    private int $isPayFree = 1;

    private ?string $featureData;

    private \DateTime $awardPeriodStartDate;

    private \DateTime $completeDate;

    private float $exchange = 0.00000;

    private int $regionId;

    private \DateTime $qualificationPeriodStartDate;

    private int $relationId = 0;

    private int $verifierId = 0;

    private int $verification = 0;

    private int $creatorId;

    private ?string $verificationError = null;

    private \DateTime $verificationDate;

    private ?string $verificationBefore = null;

    private int $listHidden = 0;

    private int $verifierBossId = 0;

    private int $docs = 0;

    private int $creatorUserId = 0;

    private int $verificationType = 0;

    private int $CDBNumber = 0;

    private ?\DateTime $openDate = null;

    private \DateTime $sinhroDate;

    private int $langId = 2; // ukr

    private int $plande = 0;

    private \DateTime $planDate;

    private ?string $transfer = null;

    private int $monitoringsCount = 0;

    public function getArray(): array
    {
        return [
            'user_id' => $this->userId,
            'tender_title' => $this->title,
            'tender_description' => $this->description,
            'tender_value_amount' => $this->valueAmount,
            'tender_value_valueAddedTaxIncluded' => $this->valueValueAddedTaxIncluded,
            'tender_minimalStep_amount' => $this->minimalStepAmount,
            'status' => $this->status,
            'tender_fullData' => $this->fullData,
            'tender_procuringEntity_name' => $this->procuringEntityName,
            'tender_cpv_id' => $this->cpvId,
            'tender_value_currency' => $this->valueCurrency,
            'questionsCount' => $this->questionsCount,
            'questionsAnswered' => $this->questionsAnswered,
            'tender_cpv_description' => $this->cpvDescription,
            'test_mode' => $this->testMode,
            'tender_type' => $this->type,
            'tender_title_en' => $this->titleEn,
            'tender_description_en' => $this->descriptionEn,
            'tender_procuringEntity_name_en' => $this->procuringEntityNameEn,
            'tender_procuringEntity_name_ru' => $this->procuringEntityNameRu,
            'tender_title_ru' => $this->titleRu,
            'tender_description_ru' => $this->descriptionRu,
            'tender_procuringEntity_identifier_id' => $this->procuringEntityIdentifierId,
            'method' => $this->method,
            'featureData' => '',
            'region_id' => $this->regionId,
            'creator_id' => $this->creatorId,
            'creator_user_id' => $this->creatorUserId,
            'lang_id' => $this->langId,
            'post' => $this->post,
            'pubDate' => $this->pubDate->format('Y-m-d H:i:s'),
        ];
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): LegacyTenderDto
    {
        $this->userId = $userId;

        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): LegacyTenderDto
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): LegacyTenderDto
    {
        $this->description = $description;

        return $this;
    }

    public function getValueAmount(): float
    {
        return $this->valueAmount;
    }

    public function setValueAmount(float $valueAmount): LegacyTenderDto
    {
        $this->valueAmount = $valueAmount;

        return $this;
    }

    public function isValueValueAddedTaxIncluded(): bool
    {
        return $this->valueValueAddedTaxIncluded;
    }

    public function setValueValueAddedTaxIncluded(bool $valueValueAddedTaxIncluded): LegacyTenderDto
    {
        $this->valueValueAddedTaxIncluded = $valueValueAddedTaxIncluded;

        return $this;
    }

    public function getMinimalStepAmount(): float
    {
        return $this->minimalStepAmount;
    }

    public function setMinimalStepAmount(float $minimalStepAmount): LegacyTenderDto
    {
        $this->minimalStepAmount = $minimalStepAmount;

        return $this;
    }

    public function getEnquiryPeriodEndDate(): \DateTime
    {
        return $this->enquiryPeriodEndDate;
    }

    public function setEnquiryPeriodEndDate(\DateTime $enquiryPeriodEndDate): LegacyTenderDto
    {
        $this->enquiryPeriodEndDate = $enquiryPeriodEndDate;

        return $this;
    }

    public function getTenderPeriodEndDate(): \DateTime
    {
        return $this->tenderPeriodEndDate;
    }

    public function setTenderPeriodEndDate(\DateTime $tenderPeriodEndDate): LegacyTenderDto
    {
        $this->tenderPeriodEndDate = $tenderPeriodEndDate;

        return $this;
    }

    public function getTenderId1(): ?string
    {
        return $this->tenderId1;
    }

    public function setTenderId1(?string $tenderId1): LegacyTenderDto
    {
        $this->tenderId1 = $tenderId1;

        return $this;
    }

    public function getTenderId2(): ?string
    {
        return $this->tenderId2;
    }

    public function setTenderId2(?string $tenderId2): LegacyTenderDto
    {
        $this->tenderId2 = $tenderId2;

        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): LegacyTenderDto
    {
        $this->status = $status;

        return $this;
    }

    public function getFullData(): string
    {
        return $this->fullData;
    }

    public function setFullData(string $fullData): LegacyTenderDto
    {
        $this->fullData = $fullData;

        return $this;
    }

    public function getModify(): \DateTime
    {
        return $this->modify;
    }

    public function setModify(\DateTime $modify): LegacyTenderDto
    {
        $this->modify = $modify;

        return $this;
    }

    public function getDateModified(): \DateTime
    {
        return $this->dateModified;
    }

    public function setDateModified(\DateTime $dateModified): LegacyTenderDto
    {
        $this->dateModified = $dateModified;

        return $this;
    }

    public function getProcuringEntityName(): ?string
    {
        return $this->procuringEntityName;
    }

    public function setProcuringEntityName(?string $procuringEntityName): LegacyTenderDto
    {
        $this->procuringEntityName = $procuringEntityName;

        return $this;
    }

    public function getCpvId(): ?string
    {
        return $this->cpvId;
    }

    public function setCpvId(?string $cpvId): LegacyTenderDto
    {
        $this->cpvId = $cpvId;

        return $this;
    }

    public function getValueCurrency(): ?string
    {
        return $this->valueCurrency;
    }

    public function setValueCurrency(?string $valueCurrency): LegacyTenderDto
    {
        $this->valueCurrency = $valueCurrency;

        return $this;
    }

    public function getAuctionPeriodStartDate(): \DateTime
    {
        return $this->auctionPeriodStartDate;
    }

    public function setAuctionPeriodStartDate(\DateTime $auctionPeriodStartDate): LegacyTenderDto
    {
        $this->auctionPeriodStartDate = $auctionPeriodStartDate;

        return $this;
    }

    public function getQuestionsCount(): int
    {
        return $this->questionsCount;
    }

    public function setQuestionsCount(int $questionsCount): LegacyTenderDto
    {
        $this->questionsCount = $questionsCount;

        return $this;
    }

    public function getQuestionsAnswered(): int
    {
        return $this->questionsAnswered;
    }

    public function setQuestionsAnswered(int $questionsAnswered): LegacyTenderDto
    {
        $this->questionsAnswered = $questionsAnswered;

        return $this;
    }

    public function getCpvDescription(): ?string
    {
        return $this->cpvDescription;
    }

    public function setCpvDescription(?string $cpvDescription): LegacyTenderDto
    {
        $this->cpvDescription = $cpvDescription;

        return $this;
    }

    public function getPubDate(): \DateTime
    {
        return $this->pubDate;
    }

    public function setPubDate(\DateTime $pubDate): LegacyTenderDto
    {
        $this->pubDate = $pubDate;

        return $this;
    }

    public function getTestMode(): int
    {
        return $this->testMode;
    }

    public function setTestMode(int $testMode): LegacyTenderDto
    {
        $this->testMode = $testMode;

        return $this;
    }

    public function getDeliveryRegionId(): ?int
    {
        return $this->deliveryRegionId;
    }

    public function setDeliveryRegionId(?int $deliveryRegionId): LegacyTenderDto
    {
        $this->deliveryRegionId = $deliveryRegionId;

        return $this;
    }

    public function getMoneygetId(): ?int
    {
        return $this->moneygetId;
    }

    public function setMoneygetId(?int $moneygetId): LegacyTenderDto
    {
        $this->moneygetId = $moneygetId;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): LegacyTenderDto
    {
        $this->type = $type;

        return $this;
    }

    public function getPost(): ?string
    {
        return $this->post;
    }

    public function setPost(?string $post): LegacyTenderDto
    {
        $this->post = $post;

        return $this;
    }

    public function getLots(): int
    {
        return $this->lots;
    }

    public function setLots(int $lots): LegacyTenderDto
    {
        $this->lots = $lots;

        return $this;
    }

    public function getFeatures(): int
    {
        return $this->features;
    }

    public function setFeatures(int $features): LegacyTenderDto
    {
        $this->features = $features;

        return $this;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function setTitleEn(?string $titleEn): LegacyTenderDto
    {
        $this->titleEn = $titleEn;

        return $this;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function setDescriptionEn(?string $descriptionEn): LegacyTenderDto
    {
        $this->descriptionEn = $descriptionEn;

        return $this;
    }

    public function getProcuringEntityNameEn(): ?string
    {
        return $this->procuringEntityNameEn;
    }

    public function setProcuringEntityNameEn(?string $procuringEntityNameEn): LegacyTenderDto
    {
        $this->procuringEntityNameEn = $procuringEntityNameEn;

        return $this;
    }

    public function getProcuringEntityNameRu(): ?string
    {
        return $this->procuringEntityNameRu;
    }

    public function setProcuringEntityNameRu(?string $procuringEntityNameRu): LegacyTenderDto
    {
        $this->procuringEntityNameRu = $procuringEntityNameRu;

        return $this;
    }

    public function getTitleRu(): ?string
    {
        return $this->titleRu;
    }

    public function setTitleRu(?string $titleRu): LegacyTenderDto
    {
        $this->titleRu = $titleRu;

        return $this;
    }

    public function getDescriptionRu(): ?string
    {
        return $this->descriptionRu;
    }

    public function setDescriptionRu(?string $descriptionRu): LegacyTenderDto
    {
        $this->descriptionRu = $descriptionRu;

        return $this;
    }

    public function getProcuringEntityIdentifierId(): int
    {
        return $this->procuringEntityIdentifierId;
    }

    public function setProcuringEntityIdentifierId(?int $procuringEntityIdentifierId): LegacyTenderDto
    {
        $this->procuringEntityIdentifierId = $procuringEntityIdentifierId;

        return $this;
    }

    public function getComplaintsCount(): int
    {
        return $this->complaintsCount;
    }

    public function setComplaintsCount(int $complaintsCount): LegacyTenderDto
    {
        $this->complaintsCount = $complaintsCount;

        return $this;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function setMethod(string $method): LegacyTenderDto
    {
        $this->method = $method;

        return $this;
    }

    public function getSigned(): int
    {
        return $this->signed;
    }

    public function setSigned(int $signed): LegacyTenderDto
    {
        $this->signed = $signed;

        return $this;
    }

    public function getIsPayFree(): int
    {
        return $this->isPayFree;
    }

    public function setIsPayFree(int $isPayFree): LegacyTenderDto
    {
        $this->isPayFree = $isPayFree;

        return $this;
    }

    public function getFeatureData(): ?string
    {
        return $this->featureData;
    }

    public function setFeatureData(?string $featureData): LegacyTenderDto
    {
        $this->featureData = $featureData;

        return $this;
    }

    public function getAwardPeriodStartDate(): \DateTime
    {
        return $this->awardPeriodStartDate;
    }

    public function setAwardPeriodStartDate(\DateTime $awardPeriodStartDate): LegacyTenderDto
    {
        $this->awardPeriodStartDate = $awardPeriodStartDate;

        return $this;
    }

    public function getCompleteDate(): \DateTime
    {
        return $this->completeDate;
    }

    public function setCompleteDate(\DateTime $completeDate): LegacyTenderDto
    {
        $this->completeDate = $completeDate;

        return $this;
    }

    public function getExchange(): float
    {
        return $this->exchange;
    }

    public function setExchange(float $exchange): LegacyTenderDto
    {
        $this->exchange = $exchange;

        return $this;
    }

    public function getRegionId(): int
    {
        return $this->regionId;
    }

    public function setRegionId(int $regionId): LegacyTenderDto
    {
        $this->regionId = $regionId;

        return $this;
    }

    public function getQualificationPeriodStartDate(): \DateTime
    {
        return $this->qualificationPeriodStartDate;
    }

    public function setQualificationPeriodStartDate(\DateTime $qualificationPeriodStartDate): LegacyTenderDto
    {
        $this->qualificationPeriodStartDate = $qualificationPeriodStartDate;

        return $this;
    }

    public function getRelationId(): int
    {
        return $this->relationId;
    }

    public function setRelationId(int $relationId): LegacyTenderDto
    {
        $this->relationId = $relationId;

        return $this;
    }

    public function getVerifierId(): int
    {
        return $this->verifierId;
    }

    public function setVerifierId(int $verifierId): LegacyTenderDto
    {
        $this->verifierId = $verifierId;

        return $this;
    }

    public function getVerification(): int
    {
        return $this->verification;
    }

    public function setVerification(int $verification): LegacyTenderDto
    {
        $this->verification = $verification;

        return $this;
    }

    public function getCreatorId(): int
    {
        return $this->creatorId;
    }

    public function setCreatorId(int $creatorId): LegacyTenderDto
    {
        $this->creatorId = $creatorId;

        return $this;
    }

    public function getVerificationError(): ?string
    {
        return $this->verificationError;
    }

    public function setVerificationError(?string $verificationError): LegacyTenderDto
    {
        $this->verificationError = $verificationError;

        return $this;
    }

    public function getVerificationDate(): \DateTime
    {
        return $this->verificationDate;
    }

    public function setVerificationDate(\DateTime $verificationDate): LegacyTenderDto
    {
        $this->verificationDate = $verificationDate;

        return $this;
    }

    public function getVerificationBefore(): ?string
    {
        return $this->verificationBefore;
    }

    public function setVerificationBefore(?string $verificationBefore): LegacyTenderDto
    {
        $this->verificationBefore = $verificationBefore;

        return $this;
    }

    public function getListHidden(): int
    {
        return $this->listHidden;
    }

    public function setListHidden(int $listHidden): LegacyTenderDto
    {
        $this->listHidden = $listHidden;

        return $this;
    }

    public function getVerifierBossId(): int
    {
        return $this->verifierBossId;
    }

    public function setVerifierBossId(int $verifierBossId): LegacyTenderDto
    {
        $this->verifierBossId = $verifierBossId;

        return $this;
    }

    public function getDocs(): int
    {
        return $this->docs;
    }

    public function setDocs(int $docs): LegacyTenderDto
    {
        $this->docs = $docs;

        return $this;
    }

    public function getCreatorUserId(): int
    {
        return $this->creatorUserId;
    }

    public function setCreatorUserId(int $creatorUserId): LegacyTenderDto
    {
        $this->creatorUserId = $creatorUserId;

        return $this;
    }

    public function getVerificationType(): int
    {
        return $this->verificationType;
    }

    public function setVerificationType(int $verificationType): LegacyTenderDto
    {
        $this->verificationType = $verificationType;

        return $this;
    }

    public function getCDBNumber(): int
    {
        return $this->CDBNumber;
    }

    public function setCDBNumber(int $CDBNumber): LegacyTenderDto
    {
        $this->CDBNumber = $CDBNumber;

        return $this;
    }

    public function getOpenDate(): ?\DateTime
    {
        return $this->openDate;
    }

    public function setOpenDate(?\DateTime $openDate): LegacyTenderDto
    {
        $this->openDate = $openDate;

        return $this;
    }

    public function getSinhroDate(): \DateTime
    {
        return $this->sinhroDate;
    }

    public function setSinhroDate(\DateTime $sinhroDate): LegacyTenderDto
    {
        $this->sinhroDate = $sinhroDate;

        return $this;
    }

    public function getLangId(): int
    {
        return $this->langId;
    }

    public function setLangId(int $langId): LegacyTenderDto
    {
        $this->langId = $langId;

        return $this;
    }

    public function getPlande(): int
    {
        return $this->plande;
    }

    public function setPlande(int $plande): LegacyTenderDto
    {
        $this->plande = $plande;

        return $this;
    }

    public function getPlanDate(): \DateTime
    {
        return $this->planDate;
    }

    public function setPlanDate(\DateTime $planDate): LegacyTenderDto
    {
        $this->planDate = $planDate;

        return $this;
    }

    public function getTransfer(): ?string
    {
        return $this->transfer;
    }

    public function setTransfer(?string $transfer): LegacyTenderDto
    {
        $this->transfer = $transfer;

        return $this;
    }

    public function getMonitoringsCount(): int
    {
        return $this->monitoringsCount;
    }

    public function setMonitoringsCount(int $monitoringsCount): LegacyTenderDto
    {
        $this->monitoringsCount = $monitoringsCount;

        return $this;
    }
}
