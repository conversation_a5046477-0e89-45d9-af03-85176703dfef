<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\TenderManager;
use App\Resource\TenderView;
use App\Utils\CurrentUserService;

/**
 * Class TenderDataProvider.
 */
final class TenderDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly TenderManager $tenderManager,
        private readonly CurrentUserService $currentUserService
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->tenderManager->find($uriVariables['id']);
        if (!$document || $document->getIsMasked()) {
            return null;
        }
        if (in_array($document->getStatus(), ['draft', 'draft.stage2', 'draft.pending', 'draft.unsuccessful', 'draft.publishing'])) {
            return null;
        }

        return new TenderView($document, $this->currentUserService->userIdAsString());
    }
}
