<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Organization;
use App\Resource\BaseIdentifierView;

/**
 * Class OrganizationView.
 */
class OrganizationView
{
    private ?string $id = null;

    private ?string $name = null;

    private ?BaseIdentifierView $identifier = null;

    private $additionalIdentifiers;

    private $address;

    private $contactPoint;

    private $additionalContactPoint;

    private $scale;

    /**
     * OrganizationView constructor.
     */
    public function __construct(Organization $organization)
    {
        $this->id = $organization->getId();
        $this->name = $organization->getName();

        $identifier = $organization->getIdentifier();
        if ($identifier) {
            $this->identifier = new BaseIdentifierView($identifier);
        }

        $this->additionalIdentifiers = $organization->getAdditionalIdentifiers();
        $this->address = $organization->getAddress();
        $this->contactPoint = $organization->getContactPoint();
        $this->additionalContactPoint = $organization->getAdditionalContactPoint();
        $this->scale = $organization->getScale();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getIdentifier(): ?BaseIdentifierView
    {
        return $this->identifier;
    }

    public function getAdditionalIdentifiers()
    {
        return $this->additionalIdentifiers;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function getContactPoint()
    {
        return $this->contactPoint;
    }

    public function getAdditionalContactPoint()
    {
        return $this->additionalContactPoint;
    }

    public function getScale()
    {
        return $this->scale;
    }
}
