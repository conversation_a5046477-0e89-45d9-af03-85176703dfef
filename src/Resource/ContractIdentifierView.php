<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\ItemProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;

/**
 * Separate collection with IDs of Contract Organization Identifiers.
 */
#[ApiResource(
    shortName: 'contract_identifiers',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_contract_identifier')),
        new Get(provider: ItemProvider::class, stateOptions: new Options(index: 'dzo_contract_identifier')),
    ],
)]
#[ApiFilter(MatchFilter::class, properties: ['scheme' => 'exact', 'id' => 'exact'])]
#[ApiFilter(KeywordFilter::class, properties: ['id', 'legalName'])]
readonly class ContractIdentifierView extends BaseIdentifierView
{
}
