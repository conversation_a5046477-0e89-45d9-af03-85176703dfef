<?php

namespace App\MessageHandler;

use App\Document\Monitoring;
use App\Document\Tender;
use App\Message\MonitoringMessage;
use App\Service\MarketCacheService;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Client;
use League\Flysystem\FilesystemOperator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[AsMessageHandler]
class MonitoringHandler extends BaseHandler
{
    private string $prozorroMonitoringUrl;

    /**
     * TenderHandler constructor.
     */
    public function __construct(
        DocumentManager $documentManager,
        SerializerInterface $serializer,
        LoggerInterface $apiLogger,
        MarketCacheService $marketCacheService,
        string $prozorroPublicPointPassword,
        string $prozorroPublicPointUrl,
        string $prozorroMarketApiLink,
        string $prozorroMarketApiPassword,
        FilesystemOperator $marketStorage,
        MessageBusInterface $messageBus,
        string $prozorroMonitoringUrl,
        private readonly string $prozorroMonitoringAuthName,
        private readonly string $prozorroMonitoringAuthPass
    ) {
        parent::__construct($documentManager, $serializer, $apiLogger, $marketCacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);

        $this->prozorroMonitoringUrl = rtrim($prozorroMonitoringUrl, '/').'/';
    }

    /**
     * @throws MongoDBException
     * @throws \Throwable
     */
    public function __invoke(MonitoringMessage $monitoring)
    {
        try {
            $client = new Client();
            $response = $client->request(
                'GET',
                $this->prozorroMonitoringUrl.'monitorings/'.$monitoring->getId(),
                [
                    'auth' => [
                        $this->prozorroMonitoringAuthName,
                        $this->prozorroMonitoringAuthPass,
                    ],
                ]
            );

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            if (array_key_exists('config', $result)) {
                $data['monitoringConfig'] = $result['config'];
            }

            // skip not full data
            if (!array_key_exists('tender_id', $data) || !array_key_exists('dateModified', $data)) {
                return;
            }

            /** @var Monitoring $object */
            $object = $this->getDocumentManager()->find(Monitoring::class, $data['id']);

            $object = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data),
                    Monitoring::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
                );

            $this->save($object);

            if ($data['tender_id']) {
                $this->processTender($data['tender_id'], false);
            }

            /** @var Tender $tenderObject */
            $tenderObject = $this->getDocumentManager()->find(Tender::class, $data['tender_id']);
            if (null === $tenderObject) {
                return;
            }

            $tenderObject->setHasMonitoring(true);
            $tenderObject->setMonitoringDateModified(new \DateTime($data['dateModified']));

            if ('active' === $object->getStatus() || 'addressed' === $object->getStatus()) {
                $tenderObject->setHasActiveMonitoring(true);
            } else {
                $tenderObject->setHasActiveMonitoring(false);
                $monitorings = $this->getDocumentManager()->getRepository(Monitoring::class)->findBy(['tenderId' => $tenderObject->getId()]);
                foreach ($monitorings as $item) {
                    if ('active' === $item->getStatus() || 'addressed' === $item->getStatus()) {
                        $tenderObject->setHasActiveMonitoring(true);
                        break;
                    }
                }
            }

            $this->save($tenderObject);
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
            throw $exception;
        }
    }
}
