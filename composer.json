{"type": "project", "license": "proprietary", "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "api-platform/core": ">=3.1.29", "composer/package-versions-deprecated": "1.11.99.1", "doctrine/annotations": "^1.14", "doctrine/doctrine-bundle": "^2.12.0", "doctrine/mongodb-odm": "2.7.1", "doctrine/mongodb-odm-bundle": "^5.0.1", "doctrine/orm": "^2.19.6", "friendsofsymfony/elastica-bundle": "^v6.3.1", "guzzlehttp/guzzle": "^7.5", "league/flysystem-aws-s3-v3": "*", "league/flysystem-bundle": "^3.3.5", "lexik/jwt-authentication-bundle": "^v2.21.0", "nelmio/cors-bundle": "^2.5.0", "predis/predis": "^2.2", "sentry/sentry-symfony": "^5.1", "snc/redis-bundle": "^4.7.1", "symfony/amqp-messenger": "7.0.*", "symfony/asset": "7.0.*", "symfony/console": "7.0.*", "symfony/dotenv": "7.0.*", "symfony/expression-language": "7.*", "symfony/flex": "^2.2", "symfony/framework-bundle": "7.0.*", "symfony/messenger": "7.0.*", "symfony/monolog-bundle": "^v3.10.0", "symfony/security-bundle": "7.0.*", "symfony/serializer": "7.0.*", "symfony/validator": "7.0.*", "symfony/yaml": "7.0.*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.61.1", "phpunit/phpunit": "^11.4", "symfony/browser-kit": "7.*", "symfony/css-selector": "7.*", "symfony/maker-bundle": "^v1.60.0", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "7.0.*", "symfony/web-profiler-bundle": "7.0.*"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts", "bin/console messenger:stop-workers"], "post-update-cmd": ["@auto-scripts", "bin/console messenger:stop-workers"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.*"}}}