replicaCount: 1

fullnameOverride: "search"

ingress:
  enabled: true
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/client-body-buffer-size: "108M"
  hosts:
    - host: search.dzo.com.ua
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: dzo
      hosts:
        - search.dzo.com.ua

resources:
  limits:
    memory: 4096Mi
  requests:
    cpu: 800m
    memory: 512Mi

configMapName: "dzo-search-worker"

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dzo-market-eks-role
