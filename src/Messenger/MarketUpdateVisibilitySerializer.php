<?php

namespace App\Messenger;

use App\Message\MarketUpdateVisibilityMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class MarketUpdateVisibilitySerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $message = new MarketUpdateVisibilityMessage($data['id'], $data['type']);

        return new Envelope($message);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();
        if ($message instanceof MarketUpdateVisibilityMessage) {
            $data = ['id' => $message->id, 'type' => $message->type];
        } else {
            throw new \Exception('Unsupported message class '.get_class($message));
        }

        return [
            'body' => json_encode($data),
        ];
    }
}
