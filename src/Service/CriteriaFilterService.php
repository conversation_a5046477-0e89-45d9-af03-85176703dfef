<?php

namespace App\Service;

use App\Document\Product;
use App\Document\Category;
use App\Document\Profile;
use App\Constants\DzoConstants;
use Doctrine\Common\Collections\ArrayCollection;

class CriteriaFilterService
{

    private const MAX_PROFILE_ITERATIONS = 100;

    /**
     * remove requirements where isArchived: true.
     *
     * @param array|null $criteria
     * @return array|null
     */
    public function filterArchivedRequirements(?array $criteria): ?array
    {
        if (!is_array($criteria)) {
            return null;
        }

        $result = array_map(function ($criterion) {
            if (!isset($criterion['requirementGroups']) || !is_array($criterion['requirementGroups'])) {
                return $criterion;
            }
            foreach ($criterion['requirementGroups'] as &$group) {
                if (isset($group['requirements']) && is_array($group['requirements'])) {
                    $group['requirements'] = array_values(array_filter($group['requirements'], function ($req) {
                        return !isset($req['isArchived']) || $req['isArchived'] === false;
                    }));
                }
            }
            unset($group);
            return $criterion;
        }, $criteria);
        unset($criteria);
        return $result;
    }

    public function addLocalizationCriteria(?array $criterias): array
    {
        if (!is_array($criterias) || empty($criterias)) {
            return [];
        }
        $criterionId = DzoConstants::LOCAL_ORIGIN_LEVEL_CLASSIFICATION_ID;
        foreach ($criterias as &$criteria) {
            if (isset($criteria['classification']['id']) && 
                $criteria['classification']['id'] == $criterionId &&
                isset($criteria['requirementGroups'][0]['requirements']) &&
                count($criteria['requirementGroups'][0]['requirements']) > 0) {
                
                $criteria['requirementGroups'] = [];

                $newRequirementGroup = [
                    'requirements' => [
                        [
                            'title'    => 'Наявність локалізації',
                            'dataType' => "boolean",
                            'id'       => md5(uniqid(rand(), true)),
                            'expectedValue' => true,
                        ]
                    ],
                    'description'  => 'Наявність локалізації'
                ];
                $criteria['requirementGroups'][] = $newRequirementGroup;
            }
        }

        return $criterias;
    }

    public function validateRequirementTypes(array $criteria, array $requirementResponses): bool
    {

        foreach ($criteria as $criterion) {
            if (!isset($criterion['requirementGroups']) || !is_array($criterion['requirementGroups'])) {
                continue;
            }
            
            foreach ($criterion['requirementGroups'] as $group) {
                if (!isset($group['requirements']) || !is_array($group['requirements'])) {
                    continue;
                }
                
                foreach ($group['requirements'] as $requirement) {
                    if (!isset($requirement['title'], $requirement['dataType'])) {
                        continue;
                    }
                    
                    $title = $requirement['title'];
                    if (!isset($requirementResponses[$title])) {
                        continue; // Skip if can't find
                    }
                    
                    $expectedType = $requirement['dataType'];
                    $responseValue = $requirementResponses[$title]['value'] ?? null;
                    $responseValues = $requirementResponses[$title]['values'] ?? [];
                    
                    if (!$this->isTypeCompatible($expectedType, $responseValue, $responseValues)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    private function isTypeCompatible(string $expectedType, $value, array $values): bool
    {
        if ($value === null && empty($values)) {
            return true;
        }

        if ($value !== null) {
            switch ($expectedType) {
                case 'string':
                    if (!is_string($value)) {
                        return false;
                    }
                    break;
                case 'boolean':
                    if (!is_bool($value)) {
                        return false;
                    }
                    break;
                case 'number':
                    if (!is_numeric($value)) {
                        return false;
                    }
                    break;
            }
        }

        if (!empty($values)) {
            foreach ($values as $item) {
                switch ($expectedType) {
                    case 'string':
                        if (!is_string($item)) {
                            return false;
                        }
                        break;
                    case 'boolean':
                        if (!is_bool($item)) {
                            return false;
                        }
                        break;
                    case 'number':
                        if (!is_numeric($item)) {
                            return false;
                        }
                        break;
                }
            }
        }
        
        return true;
    }

    public function hasIncompatibleProductTypes(Product $product, ?Category $category = null, array $profiles = []): bool
    {
        if (!$category || !$product->getRequirementResponses()) {
            return false;
        }

        $productRequirements = [];
        foreach ($product->getRequirementResponses() as $response) {
            if (!method_exists($response, 'getRequirement') ||
                !method_exists($response, 'getValue') ||
                !method_exists($response, 'getValues')) {
                continue;
            }

            $requirement = $response->getRequirement();
            if (!is_string($requirement)) {
                continue;
            }

            $value = $response->getValue();
            $values = $response->getValues();

            if (!is_array($values) && $values !== null) {
                continue;
            }

            $productRequirements[$requirement] = [
                'value' => $value,
                'values' => $values ?? [],
            ];
        }

        if ($category->getCriteria()) {
            if (!$this->validateRequirementTypes($category->getCriteria(), $productRequirements)) {
                return true;
            }
        }

        $profileIterations = 0;
        foreach ($profiles as $profile) {
            if (!$profile instanceof Profile) {
                continue;
            }

            $profileIterations++;
            if ($profileIterations >= self::MAX_PROFILE_ITERATIONS) {
                return true;
            }

            if ($profile->getCriteria()) {
                if (!$this->validateRequirementTypes($profile->getCriteria(), $productRequirements)) {
                    return true;
                }
            }
        }

        return false;
    }
    
    public function filterArchivedRequirementsFromProduct(Product $product, Category $category): void
    {
        if (!$category->getCriteria() || !is_array($category->getCriteria())) {
            return;
        }
        
        $archivedRequirements = $this->getArchivedRequirementTitles($category->getCriteria());
        if (empty($archivedRequirements)) {
            return;
        }

        if ($product->getRequirementResponses() && $product->getRequirementResponses()->count() > 0) {
            $filteredResponses = $product->getRequirementResponses()->filter(function($response) use ($archivedRequirements) {
                return !in_array($response->getRequirement(), $archivedRequirements);
            });
            $responsesArray = array_values($filteredResponses->toArray());
            $product->setRequirementResponses(new ArrayCollection($responsesArray));
        }

        if ($product->getProductRequirements() && $product->getProductRequirements()->count() > 0) {
            $filteredRequirements = $product->getProductRequirements()->filter(function($requirement) use ($archivedRequirements) {
                return !in_array($requirement->getRequirement(), $archivedRequirements);
            });

            $reindexedArray = array_values($filteredRequirements->toArray());
            $product->setProductRequirements(new ArrayCollection($reindexedArray));
        }
    }

    public function filterArchivedRequirementsFromProfile(Profile $profile, Category $category): void
    {
        if (!$category->getCriteria() || !is_array($category->getCriteria()) || !$profile->getCriteria() || !is_array($profile->getCriteria())) {
            return;
        }
        
        $archivedRequirements = $this->getArchivedRequirementTitles($category->getCriteria());
        if (empty($archivedRequirements)) {
            return;
        }
        
        $filteredCriteria = $profile->getCriteria();
        foreach ($filteredCriteria as $criterionKey => $criterion) {
            if (!isset($criterion['requirementGroups']) || !is_array($criterion['requirementGroups'])) {
                continue;
            }
            
            foreach ($criterion['requirementGroups'] as $groupKey => $group) {
                if (!isset($group['requirements']) || !is_array($group['requirements'])) {
                    continue;
                }
                
                $filteredRequirements = array_filter($group['requirements'], function($requirement) use ($archivedRequirements) {
                    return !isset($requirement['title']) || !in_array($requirement['title'], $archivedRequirements);
                });
                
                $filteredCriteria[$criterionKey]['requirementGroups'][$groupKey]['requirements'] = array_values($filteredRequirements);
            }
        }
        
        $profile->setCriteria($filteredCriteria);
    }

    private function getArchivedRequirementTitles(array $criteria): array
    {
        $archivedRequirements = [];
        
        foreach ($criteria as $criterion) {
            if (!isset($criterion['requirementGroups']) || !is_array($criterion['requirementGroups'])) {
                continue;
            }
            
            foreach ($criterion['requirementGroups'] as $group) {
                if (!isset($group['requirements']) || !is_array($group['requirements'])) {
                    continue;
                }
                
                foreach ($group['requirements'] as $requirement) {
                    if (isset($requirement['isArchived']) && $requirement['isArchived'] === true && isset($requirement['title'])) {
                        $archivedRequirements[] = $requirement['title'];
                    }
                }
            }
        }
        
        return $archivedRequirements;
    }
}
