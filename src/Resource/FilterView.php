<?php

namespace App\Resource;

use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Post;
use App\DataProvider\CollectionFilterProvider;
use App\DataProvider\DeleteFilterProvider;
use App\Document\Filter;
use App\Dto\FilterDto;
use App\Filter\Odm\RequiredExactMatchFilter;
use App\State\FilterProcessor;

#[ApiResource(
    shortName: 'filter',
    operations: [
        new GetCollection(class: Filter::class, provider: CollectionFilterProvider::class),
        new Post(input: FilterDto::class, processor: FilterProcessor::class),
        new Delete(uriVariables: 'id', provider: DeleteFilterProvider::class),
    ],
    order: ['createDate' => 'DESC'],
    security: "is_granted('ROLE_USER')",
)]
#[ApiFilter(RequiredExactMatchFilter::class, properties: ['type'])]
readonly class FilterView
{
    public string $id;

    public string $name;

    public string $filter;

    public function __construct(Filter $document)
    {
        $this->id = $document->getId();
        $this->name = $document->getName();
        $this->filter = $document->getFilter();
    }
}
