<?php

namespace App\Command;

use App\Document\Product;
use App\Message\ProductMessage;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:fix:product-profiles',
    description: 'Fix profiles with empty prozorroProfile but with exists our profileId',
)]
class FixProductEmptyProfilesCommand extends Command
{
    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $pageSize = 10000;
        $queryBuilder = $this->documentManager->createQueryBuilder(Product::class)
            ->select('prozorroId')
            ->limit($pageSize);

        $queryBuilder->addAnd(
            $queryBuilder->expr()->field('relatedProfileIds')->notEqual([])
        );

        $queryBuilder->addAnd(
            $queryBuilder->expr()->field('relatedProfiles')->equals([])
        );

        $result = $queryBuilder->getQuery()->execute();
        if (0 === count($result)) {
            $output->writeln("Products are fine");

            return Command::SUCCESS;
        }

        /** @var Product $product */
        foreach ($result as $product) {
            $this->messageBus->dispatch(new ProductMessage($product->getProzorroId()));
        }

        $this->documentManager->clear();
        $output->writeln("All products send to queue to resync");

        return Command::SUCCESS;
    }
}