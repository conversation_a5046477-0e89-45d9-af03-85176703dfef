<?php

namespace App\Command;

use App\Document\MarketClassification;
use App\Repository\CategoryRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:market:generate-active-classifications',
    description: 'Generate active classifications by active categories',
)]
class MarketActiveClassificationsCommand extends Command
{
    protected static $defaultName = 'app:market:generate-cache';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly CategoryRepository $categoryRepository
    ) {
        parent::__construct(self::$defaultName);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $marketClassifications = $this->documentManager->getRepository(MarketClassification::class)->findAll();
        foreach ($marketClassifications as $marketClassification) {
            if ($countCategories = $this->categoryRepository->findActiveByClassificationId($marketClassification->getId())) {
                $marketClassification->setActiveCategories($countCategories);
                $this->documentManager->persist($marketClassification);
            }
        }
        $this->documentManager->flush();

        return Command::SUCCESS;
    }
}