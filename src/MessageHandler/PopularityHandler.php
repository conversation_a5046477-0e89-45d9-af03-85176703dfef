<?php

namespace App\MessageHandler;

use App\Document\Category;
use App\Document\Embedded\EmbeddedClassification;
use App\Document\Embedded\Item;
use App\Document\MarketClassification;
use App\Document\Product;
use App\Document\Profile;
use App\Document\Tender;
use App\Dto\CartItemConfigDto;
use App\Manager\ProductManager;
use App\Message\PopularityMessage;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ODM\MongoDB\DocumentManager;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class PopularityHandler
{
    public function __construct(
        private DocumentManager $documentManager,
        private ProductManager $productManager,
        private ObjectPersisterInterface $categoryPersister,
        private ObjectPersisterInterface $profilePersister,
        private ObjectPersisterInterface $productPersister,
        private ObjectPersisterInterface $classificationPersister
    ) {
    }

    public function __invoke(PopularityMessage $message)
    {
        $tenderRepository = $this->documentManager->getRepository(Tender::class);
        $profileRepository = $this->documentManager->getRepository(Profile::class);
        $productRepository = $this->documentManager->getRepository(Product::class);
        $categoryRepository = $this->documentManager->getRepository(Category::class);
        /** @var Tender $tender */
        $tender = $tenderRepository->find($message->getId());

        /** @var Item $item */
        foreach ($tender->getItems() as $item) {
            $isActiveProfile = false;
            if ($item->getClassification()) {
                $this->persistClassification($item->getClassification(), $message->isIncrease());
            }

            if ($item->getProfile()) {
                /** @var Profile $profile */
                if ($profile = $profileRepository->findOneBy(['prozorroId' => $item->getProfile()])) {
                    $this->persistProfile($profile, $message->isIncrease());
                    if ($profile->getStatus() === 'active') {
                        $isActiveProfile = true;
                        if ($products = $productRepository->findBy(['relatedProfiles' => $item->getProfile()])) {
                            $this->persistProducts($products, $message->isIncrease());
                        }
                    }
                }
            }

            if ($item->getCategory()) {
                if ($category = $categoryRepository->findOneBy(['prozorroId' => $item->getCategory()])) {
                    $this->persistCategory($category, $message->isIncrease());
                    if (!$isActiveProfile) {
                        if ($products = $this->findProducts($item->getId(), $tender->getCriteria(), $category)) {
                            $this->persistProducts($products, $message->isIncrease());
                        }
                    }
                }
            }
        }
    }

    function findProducts(string $itemId, array $criteria, Category $category)
    {
        $key = array_search($itemId, array_column($criteria, 'relatedItem'));
        if (isset($criteria[$key])) {
            $itemCriteria = $criteria[$key];
            $requirementCollection = new ArrayCollection();
            foreach ($itemCriteria['requirementGroups'][0]['requirements'] as $requirement) {
                $requirementCollection->add(new CartItemConfigDto($requirement));
            }

            return $this->productManager->findProductByRequirements(
                $requirementCollection,
                true,
                $category->getProzorroId()
            );
        }

        return [];
    }

    private function persistClassification(EmbeddedClassification $classification, bool $isIncrease): void
    {
        $marketClassificationRepository = $this->documentManager->getRepository(MarketClassification::class);
        if ($classification = $marketClassificationRepository->find($classification->getId())) {
            $popularity = $classification->getPopularity();
            if ($isIncrease) {
                $popularity += 1;
            } elseif ($popularity > 0) {
                $popularity -= 1;
            }
            if ($popularity != $classification->getPopularity()) {
                $classification->setPopularity($popularity);
                $this->documentManager->persist($classification);
                $this->documentManager->flush();

                $this->classificationPersister->replaceOne($classification);
            }
        }
    }

    private function persistProfile(Profile $profile, bool $isIncrease): void
    {
        if ($isIncrease) {
            $profile->setPopularity($profile->getPopularity() + 1);
        } elseif ($profile->getPopularity() > 0) {
            $profile->setPopularity($profile->getPopularity() - 1);
        }
        $this->documentManager->persist($profile);
        $this->documentManager->flush();

        $this->profilePersister->replaceOne($profile);
    }

    private function persistProducts(array $products, bool $isIncrease): void
    {
        /** @var Product $product */
        foreach ($products as $product) {
            if ($isIncrease) {
                $product->setPopularity($product->getPopularity() + 1);
            } elseif ($product->getPopularity() > 0) {
                $product->setPopularity($product->getPopularity() - 1);
            }
            $this->documentManager->persist($product);
        }
        $this->documentManager->flush();
        $this->productPersister->replaceMany($products);
    }

    private function persistCategory(Category $category, bool $isIncrease): void
    {
        if ($isIncrease) {
            $category->setPopularity($category->getPopularity() + 1);
        } elseif ($category->getPopularity() > 0) {
            $category->setPopularity($category->getPopularity() - 1);
        }
        $this->documentManager->persist($category);
        $this->documentManager->flush();
        $this->categoryPersister->replaceOne($category);
    }
}
