<?php

namespace App\Command;

use App\Document\MongoToEs;
use App\Document\Tender;
use App\Message\TenderMessage;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class FixTenderFundersCommand extends Command
{
    protected static $defaultName = 'app:fix:tender-funders';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct(self::$defaultName);
    }

    protected function configure()
    {
        $this->setDescription('Select tenders with funders and dispatch to resync them.
         Specific case, one time command for fix production database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $pageSize = 10000;
        while (true) {
            $pageDocument = $this->documentManager->getRepository(MongoToEs::class)->findOneBy(['type' => 'funders']);

            $queryBuilder = $this->documentManager->createQueryBuilder(Tender::class)
                ->select('id', 'funders', 'dateCreated')
                ->sort('dateCreated', 'ASC')
                ->limit($pageSize);

            if ($pageDocument) {
                $output->writeln('Migrating tender documents from '.$pageDocument->getDateCreated()->format('Y-m-d H:i:s'));
                $queryBuilder->addAnd(
                    $queryBuilder->expr()->field('dateCreated')->gt($pageDocument->getDateCreated())
                );
            } else {
                $output->writeln('Migrating tender documents from the beginning');
            }

            $result = $queryBuilder->getQuery()->execute();
            if (0 === count($result)) {
                break;
            }

            foreach ($result as $document) {
                $newDate = $document->getDateCreated();
                if (count($document->getFunders()) > 0) {
                    $this->messageBus->dispatch(new TenderMessage($document->getId()));
                }
            }

            $this->documentManager->clear();

            if (!$pageDocument) {
                $pageDocument = new MongoToEs();
                $pageDocument->setType('funders');
            }

            $pageDocument->setDateCreated($newDate);
            $this->documentManager->persist($pageDocument);

            $this->documentManager->flush();
        }

        $output->writeln('Fix funders in tender documents finished');

        return 0;
    }
}
