<?php

namespace App\Document;

use App\Repository\CartRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document(repositoryClass: CartRepository::class)]
#[MongoDB\HasLifecycleCallbacks]
class Cart
{
    #[MongoDB\Id]
    private string $id;

    #[MongoDB\Field(type: Type::STRING)]
    private string $userId;

    #[MongoDB\Field(type: Type::DATE)]
    private $createdDate;

    #[MongoDB\Field(type: Type::DATE)]
    private $updatedDate;

    #[MongoDB\ReferenceMany(targetDocument: CartItem::class, mappedBy: 'cart')]
    private Collection $cartItems;

    public function __construct()
    {
        $this->cartItems = new ArrayCollection();
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function setUserId(string $userId): void
    {
        $this->userId = $userId;
    }

    public function getCreateDate()
    {
        return $this->createDate;
    }

    public function setCreateDate($createDate): void
    {
        $this->createDate = $createDate;
    }

    public function getCartItems(): Collection
    {
        return $this->cartItems;
    }

    public function setCartItems(Collection $cartItems): void
    {
        $this->cartItems = $cartItems;
    }

    public function addCartItem(CartItem $cartItem): void
    {
        if (!$this->cartItems->contains($cartItem)) {
            $this->cartItems->add($cartItem);
        }
    }

    public function getCreatedDate()
    {
        return $this->createdDate;
    }

    public function setCreatedDate($createdDate): void
    {
        $this->createdDate = $createdDate;
    }

    public function getUpdatedDate()
    {
        return $this->updatedDate;
    }

    #[MongoDB\PrePersist]
    public function setUpdatedDate(): void
    {
        $this->updatedDate = new \DateTime();
    }
}
