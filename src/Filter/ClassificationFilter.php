<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

/**
 * Class ClassificationFilter.
 */
final class ClassificationFilter extends AbstractSearchFilter
{
    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operationName = null,
        array $context = [],
    ): array {
        $searches = [];

        foreach ($context['filters'] ?? [] as $property => $values) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }

            $searchProperty = $this->properties[$property];
            $searches[] = $this->getQuery($searchProperty, (array) $values, null);
        }

        if (!$searches) {
            return $clauseBody;
        }

        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);

        //        Uncomment to debug query:
        //        echo json_encode($query);exit;
        return $query;
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $termQuery = ['terms' => [$property => $values]];

        if (null !== $nestedPath) {
            $termQuery = ['nested' => ['path' => $nestedPath, 'query' => $termQuery]];
        }

        return $termQuery;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        foreach ($this->properties as $key => $related) {
            $description[$key] = [
                'property' => $key,
                'type' => 'string',
                'required' => false,
            ];
            $description[$key.'[]'] = [
                'property' => $key.'[]',
                'type' => 'array',
                'required' => false,
            ];
        }

        return $description;
    }
}
