<?php

namespace App\Utils;

use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class CurrentUserService
{
    private $cachedUserId;

    private $cacheUserOldId;

    private $cacheCompanyId;

    public function __construct(
        private readonly TokenStorageInterface $tokenStorage,
        private readonly JWTTokenManagerInterface $jwtManager,
    ) {
    }

    public function userId()
    {
        if (null === $this->cachedUserId) {
            try {
                $token = $this->tokenStorage->getToken();
                if (!$token) {
                    $this->cachedUserId = false;
                } else {
                    $decodedJwtToken = $this->jwtManager->decode($token);
                    $this->cachedUserId = $decodedJwtToken['id'];
                }
            } catch (\Exception $e) {
                $this->cachedUserId = false;
            }
        }

        return $this->cachedUserId;
    }

    public function userIdAsString(): string
    {
        $userId = $this->userId();
        if (!$userId) {
            return '';
        }

        return (string) $userId;
    }

    public function oldUderId()
    {
        if (null === $this->cacheUserOldId) {
            try {
                $token = $this->tokenStorage->getToken();
                if (!$token) {
                    $this->cacheUserOldId = false;
                } else {
                    $decodedJwtToken = $this->jwtManager->decode($token);
                    $this->cachedUserId = $decodedJwtToken['oldId'];
                }
            } catch (\Exception $e) {
                $this->cacheUserOldId = false;
            }
        }

        return $this->cacheUserOldId;
    }

    public function userOldIdAsString(): string
    {
        $oldId = $this->oldUderId();
        if (!$oldId) {
            return '';
        }

        return (string) $oldId;
    }

    public function companyId()
    {
        if (null === $this->cacheCompanyId) {
            try {
                $token = $this->tokenStorage->getToken();
                if (!$token) {
                    $this->cacheCompanyId = false;
                } else {
                    $decodedJwtToken = $this->jwtManager->decode($token);
                    $this->cacheCompanyId = $decodedJwtToken['companyId'];
                }
            } catch (\Exception $e) {
                $this->cacheCompanyId = false;
            }
        }

        return $this->cacheCompanyId;
    }

    public function companyIdAsString(): string
    {
        $companyId = $this->companyId();
        if (!$companyId) {
            return '';
        }

        return (string) $companyId;
    }
}
