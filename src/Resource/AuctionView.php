<?php

namespace App\Resource;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use App\DataProvider\AuctionDataProvider;

#[ApiResource(
    shortName: 'auction',
    operations: [
        new Get(provider: AuctionDataProvider::class),
    ]
)]
class AuctionView
{
    private string $id;

    private string $auctionType;

    private int $currentStage;

    private array $features;

    private array $item;

    private $minimalStep;

    private string $modified;

    private string $procurementMethodType;

    private $procurementMethod;

    private array $stages;

    private string $startedAt;

    private string $tenderID;

    private string $title;

    private $value;

    private array $initialBids;

    private array $results;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getAuctionType(): string
    {
        return $this->auctionType;
    }

    public function setAuctionType(string $auctionType): void
    {
        $this->auctionType = $auctionType;
    }

    public function getCurrentStage(): int
    {
        return $this->currentStage;
    }

    public function setCurrentStage(int $currentStage): void
    {
        $this->currentStage = $currentStage;
    }

    public function getFeatures(): array
    {
        return $this->features;
    }

    public function setFeatures(array $features): void
    {
        $this->features = $features;
    }

    public function getInitialBids(): array
    {
        return $this->initialBids;
    }

    public function setInitialBids(array $initialBids): void
    {
        $this->initialBids = $initialBids;
    }

    public function getItem(): array
    {
        return $this->item;
    }

    public function setItem(array $item): void
    {
        $this->item = $item;
    }

    public function getMinimalStep()
    {
        return $this->minimalStep;
    }

    public function setMinimalStep($minimalStep): void
    {
        $this->minimalStep = $minimalStep;
    }

    public function getModified(): string
    {
        return $this->modified;
    }

    public function setModified(string $modified): void
    {
        $this->modified = $modified;
    }

    public function getProcurementMethod()
    {
        return $this->procurementMethod;
    }

    public function setProcurementMethod($procurementMethod): void
    {
        $this->procurementMethod = $procurementMethod;
    }

    public function getProcurementMethodType(): string
    {
        return $this->procurementMethodType;
    }

    public function setProcurementMethodType(string $procurementMethodType): void
    {
        $this->procurementMethodType = $procurementMethodType;
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function setResults(array $results): void
    {
        $this->results = $results;
    }

    public function getStages(): array
    {
        return $this->stages;
    }

    public function setStages(array $stages): void
    {
        $this->stages = $stages;
    }

    public function getStartedAt(): string
    {
        return $this->startedAt;
    }

    public function setStartedAt(string $startedAt): void
    {
        $this->startedAt = $startedAt;
    }

    public function getTenderID(): string
    {
        return $this->tenderID;
    }

    public function setTenderID(string $tenderID): void
    {
        $this->tenderID = $tenderID;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value): void
    {
        $this->value = $value;
    }
}
