<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

/**
 * Class KeywordFilter.
 */
final class KeywordFilter extends AbstractSearchFilter
{
    public const KEY = 'keyword';

    public array $fields = [];

    public function getName(): string
    {
        return self::KEY;
    }

    public function setFields(array $fields): void
    {
        $this->fields = $fields;
    }

    /**
     * @todo
     */
    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $properties = array_keys($this->properties);
        $query =
          [
              'multi_match' => [
                  'query' => $values[0],
                  'fields' => $properties,
                  'type' => 'bool_prefix',
                  'operator' => 'and',
              ],
          ];

        return $query;
    }

    /**
     * @todo refactor
     * @todo move to base class
     */
    public function apply(array $clauseBody, string $resourceClass, ?Operation $operationName = null, array $context = []): array
    {
        $searches = [];

        foreach ($context['filters'] ?? [] as $property => $values) {
            if ($property !== $this->getName()) {
                continue;
            }

            $searches[] = $this->getQuery($property, (array) $values, '');
        }

        if (!$searches) {
            return $clauseBody;
        }

        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);

        //        Uncomment to debug query:
        //        echo json_encode($query);exit;
        return $query;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        $description[$this->getName()] = [
            'property' => $this->getName(),
            'type' => 'string',
            'required' => false,
        ];

        return $description;
    }
}
