App\Document\Product:
  attributes:
    id:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list', 'category:list', 'category:read' ]
    prozorroId:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    owner:
      groups: [ 'product:read', 'product:cdb_parse' ]
    classification:
      groups: [ 'product:read', 'product:cdb_parse' ]
    identifier:
      groups: [ 'product:read', 'product:cdb_parse' ]
    description:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    status:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    title:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list', 'category:list', 'category:read' ]
    images:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    dateModified:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    dateCreated:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    expirationDate:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    relatedProfiles:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    relatedCategory:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    relatedCategoryId:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    brand:
      groups: [ 'product:read', 'product:cdb_parse' ]
    requirementResponses:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    productRequirements:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    manufacturers:
      groups: [ 'product:read', 'product:cdb_parse' ]
    additionalProperties:
      groups: [ 'product:read', 'product:cdb_parse' ]
    additionalClassifications:
      groups: [ 'product:read', 'product:cdb_parse' ]
    alternativeIdentifiers:
      groups: [ 'product:read', 'product:cdb_parse' ]
    vendor:
      groups: [ 'product:read', 'product:cdb_parse' ]
    documents:
      groups: [ 'product:read', 'product:cdb_parse' ]
    product:
      groups: [ 'product:read', 'product:cdb_parse' ]