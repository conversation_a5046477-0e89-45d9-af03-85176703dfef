monolog:
    channels: ['api']
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks
        nested:
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: error
            type: rotating_file
            max_files: 10
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]
        api:
            type: rotating_file
            action_level: error
            max_files: 10
            path: "%kernel.logs_dir%/%kernel.environment%.api.log"
            channels: [ "api" ]
