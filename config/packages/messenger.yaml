framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed

        transports:
            tenders:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\TenderSerializer
                options:
                    exchange:
                        name: tenders
                        type: direct
                    queues:
                        tenders: ~
            contracts:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\ContractSerializer
                options:
                    exchange:
                        name: contracts
                        type: direct
                    queues:
                        contracts: ~
            plans:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\PlanSerializer
                options:
                    exchange:
                        name: plans
                        type: direct
                    queues:
                        plans: ~
            monitorings:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\MonitoringSerializer
                options:
                    exchange:
                        name: monitorings
                        type: direct
                    queues:
                        monitorings: ~
            inspections:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\InspectionSerializer
                options:
                    exchange:
                        name: inspections
                        type: direct
                    queues:
                        inspections: ~
            categories:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\CategorySerializer
                options:
                    exchange:
                        name: categories
                        type: direct
                    queues:
                        categories: ~
            profiles:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\ProfileSerializer
                options:
                    exchange:
                        name: profiles
                        type: direct
                    queues:
                        profiles: ~
            products:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\ProductSerializer
                options:
                    exchange:
                        name: products
                        type: direct
                    queues:
                        products: ~
            legacy_tender_action:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\LegacyTenderActionSerializer
                options:
                    exchange:
                        name: legacy_tender_action
                        type: direct
                    queues:
                        legacy_tender_action: ~
            search_mongo_to_es:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\MongoToEsSerializer
                options:
                    exchange:
                        name: search_mongo_to_es
                        type: direct
                    queues:
                        search_mongo_to_es: ~
            update_visibility_flags:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\MarketUpdateVisibilitySerializer
                options:
                    exchange:
                        name: update_visibility_flags
                        type: direct
                    queues:
                        update_visibility_flags: ~
            category_status_setter:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\CategoryStatusSetterSerializer
                options:
                    exchange:
                        name: category_status_setter
                        type: direct
                    queues:
                        category_status_setter: ~


            old_id_setter:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\OldIdSerializer
                options:
                    exchange:
                        name: old_id_setter
                        type: direct
                    queues:
                        old_id_setter: ~

            agreements:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\AgreementSerializer
                options:
                    exchange:
                        name: agreements
                        type: direct
                    queues:
                        agreements: ~


            popularity_setter:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\PopularitySerializer
                options:
                    exchange:
                        name: popularity_setter
                        type: direct
                    queues:
                        popularity_setter: ~


            bids:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\BidSerializer
                options:
                    exchange:
                        name: bids
                        type: direct
                    queues:
                        bids: ~

            bid_errors:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: App\Messenger\BidErrorSerializer
                options:
                    exchange:
                        name: bid_errors
                        type: direct
                    queues:
                        bid_errors: ~
        routing:
            # Route your messages to the transports
            'App\Message\TenderMessage': tenders
            'App\Message\ContractMessage': contracts
            'App\Message\PlanMessage': plans
            'App\Message\MonitoringMessage': monitorings
            'App\Message\CategoryMessage': categories
            'App\Message\ProfileMessage': profiles
            'App\Message\ProductMessage': products
            'App\Message\InspectionMessage': inspections
            'App\Message\MongoToEsMessage' : search_mongo_to_es
            'App\Message\CategoryStatusSetterMessage': category_status_setter
            'App\Message\OldIdMessage': old_id_setter
            'App\Legacy\Message\LegacyTenderActionMessage' : legacy_tender_action
            'App\Message\PopularityMessage': popularity_setter
            'App\Message\AgreementMessage': agreements
            'App\Message\BidMessage': bids
            'App\Message\BidErrorMessage': bid_errors
            'App\Message\MarketUpdateVisibilityMessage': update_visibility_flags
