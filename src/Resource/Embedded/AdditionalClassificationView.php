<?php

namespace App\Resource\Embedded;

use App\Document\MarketClassification;

class AdditionalClassificationView
{
    public ?string $id;

    public ?string $description;

    public ?string $scheme;

    public function __construct(MarketClassification $marketClassification)
    {
        $this->id = $marketClassification->getId();
        $this->description = $marketClassification->getDescription();
        $this->scheme = $marketClassification->getScheme();
    }
}
