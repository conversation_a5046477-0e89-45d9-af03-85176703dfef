<?php

namespace App\Resource;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Post;
use App\DataProvider\CartDataProvider;
use App\DataProvider\DeleteCartItemProvider;
use App\Document\Cart;
use App\Dto\CartItemDto;
use App\Dto\CartItemQuantityDto;
use App\Dto\CartItemsDto;
use App\State\CartCheckoutProcessor;
use App\State\CartItemProcessor;
use App\State\CartItemQuantityProcessor;

#[ApiResource(
    shortName: 'cart',
    operations: [
        new Get(uriTemplate: '/cart', class: Cart::class, provider: CartDataProvider::class),
        new Post(uriTemplate: '/cart/add-item', input: CartItemDto::class, processor: CartItemProcessor::class),
        new Post(uriTemplate: '/cart/checkout', input: CartItemsDto::class, processor: CartCheckoutProcessor::class),
        new Post(uriTemplate: '/cart/change-quantity', input: CartItemQuantityDto::class, processor: CartItemQuantityProcessor::class),
        new Delete(uriVariables: 'id', provider: DeleteCartItemProvider::class),
    ],
    security: "is_granted('ROLE_USER')",
)]
readonly class CartView
{
    public string $id;

    public array $cartItems;

    public function __construct(Cart $cart, array $items = [])
    {
        $this->id = $cart->getId();
        $this->cartItems = $items;
    }
}
