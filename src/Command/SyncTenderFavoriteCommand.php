<?php

namespace App\Command;

use App\Legacy\Repository\TenderFavoriteRepository;
use App\Repository\TenderRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:sync:tender-favorite',
    description: 'Sync tender favorite from legacy database',
)]
class SyncTenderFavoriteCommand extends Command
{
    private const TENDER_FAVORITE_CHUNK = 100;

    public function __construct(
        private readonly DocumentManager $dm,
        private readonly TenderFavoriteRepository $favoriteRepository,
        private readonly TenderRepository $tenderRepository,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $tenders = $this->favoriteRepository->tendersFavorite();

        $count = 0;
        foreach ($tenders as $item) {
            $tender = $this->tenderRepository->findOneBy(['id' => $item['tender_id']]);
            if (!$tender) {
                continue;
            }
            $tender->getDzoData()->addFavoriteUser($item['user_id']);
            if (self::TENDER_FAVORITE_CHUNK === $count) {
                $this->dm->flush();
                $this->dm->clear();
                $count = 0;
            }
            ++$count;
        }

        $this->dm->flush();
        $this->dm->clear();

        return Command::SUCCESS;
    }
}
