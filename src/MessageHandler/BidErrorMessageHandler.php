<?php

namespace App\MessageHandler;

use App\Message\BidErrorMessage;
use App\Service\SlackNotifier;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class BidErrorMessageHandler
{
    private SlackNotifier $slackNotifier;

    public function __construct(SlackNotifier $slackNotifier)
    {
        $this->slackNotifier = $slackNotifier;
    }

    public function __invoke(BidErrorMessage $message)
    {
        $errorMessage = $message->getMessage();

        $this->slackNotifier->send(
            "❌ Bid Error:\n" . $errorMessage
        );
    }
}
