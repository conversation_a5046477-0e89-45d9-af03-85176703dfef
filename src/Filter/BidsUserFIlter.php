<?php

namespace App\Filter;

use ApiPlatform\Api\IriConverterInterface;
use ApiPlatform\Api\ResourceClassResolverInterface;
use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\Metadata\Property\Factory\PropertyMetadataFactoryInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyNameCollectionFactoryInterface;
use App\Security\JwtCheckerTrait;
use App\Utils\CurrentUserService;
use Symfony\Component\Finder\Exception\AccessDeniedException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;

class BidsUserFIlter extends AbstractSearchFilter
{
    use JwtCheckerTrait;

    private ?string $userId;
    public function __construct(
        private CurrentUserService $currentUserService,
        PropertyNameCollectionFactoryInterface $propertyNameCollectionFactory,
        PropertyMetadataFactoryInterface $propertyMetadataFactory,
        ResourceClassResolverInterface $resourceClassResolver,
        IriConverterInterface $iriConverter,
        PropertyAccessorInterface $propertyAccessor,
        ?NameConverterInterface $nameConverter = null,
        ?array $properties = null
    ) {
        parent::__construct($propertyNameCollectionFactory, $propertyMetadataFactory, $resourceClassResolver, $iriConverter, $propertyAccessor, $nameConverter, $properties);
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $query = [];
        if ($property == 'isPublicated') {
            $query = [
                "nested" => [
                    "path" => "dzoData",
                    "query" => [
                        "nested" => [
                            "path" => "dzoData.bids",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["match" => ['dzoData.bids.userId' => $this->userId]],
                                        ["exists" => ["field" => "dzoData.bids.apiId"]],
                                    ],
                                    "must_not" => [
                                        ["term" => ["dzoData.bids.apiId" => ""]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        if ($property == 'isDraft') {
            $query = [
                "nested" => [
                    "path" => "dzoData",
                    "query" => [
                        "nested" => [
                            "path" => "dzoData.bids",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["match" => ['dzoData.bids.userId' => $this->userId]],
                                        ["match" => ['dzoData.bids.pay' => 0]],
                                        ["match" => ['dzoData.bids.signed' => 0]],
                                        ["match" => ['dzoData.bids.apiId' => ""]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        if ($property == 'isWaitSign') {
            $query = [
                "nested" => [
                    "path" => "dzoData",
                    "query" => [
                        "nested" => [
                            "path" => "dzoData.bids",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["match" => ['dzoData.bids.userId' => $this->userId]],
                                        ["range" => ['dzoData.bids.pay' => ['gt' => 0]]],
                                        ["match" => ['dzoData.bids.signed' => 0]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        return $query;
    }

    public function apply(array $clauseBody, string $resourceClass, ?Operation $operationName = null, array $context = []): array
    {
        if (!$this->userId = $this->checkJwt()) {
            return $clauseBody;
        }

        $searches = [];

        foreach ($context['filters'] ?? [] as $property => $value) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }
            if ('1' !== $value && 'true' !== $value) {
                continue;
            }

            $searchProperty = $this->properties[$property];
            $searches[] = $this->getQuery($searchProperty, [$value], null);
        }

        if (!$searches) {
            return $clauseBody;
        }
        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);

        return $query;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        foreach ($this->properties as $key => $related) {
            $description[$key] = [
                'property' => $key,
                'type' => 'string',
                'required' => false,
            ];
        }

        return $description;
    }
}
