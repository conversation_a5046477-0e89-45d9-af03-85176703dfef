<?php

namespace App\Messenger;

use App\Message\PopularityMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class PopularitySerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $message = new PopularityMessage($data['id'], $data['increase']);

        return new Envelope($message);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();
        if ($message instanceof PopularityMessage) {
            $data = ['id' => $message->getId(), 'increase' => $message->isIncrease()];
        } else {
            throw new \Exception('Unsupported message class '.get_class($message));
        }

        return [
            'body' => json_encode($data),
        ];
    }
}
