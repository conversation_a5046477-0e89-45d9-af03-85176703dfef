<?php

namespace App\Classification;

use App\Document\Classification;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class CcceParser.
 */
class CcceParser extends ClassificationParser
{
    protected string $scheme = 'ccce_ua';

    /**
     * @throws GuzzleException
     * @throws \Doctrine\ODM\MongoDB\LockException
     * @throws \Doctrine\ODM\MongoDB\Mapping\MappingException
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    public function parse(array $urls): void
    {
        $url = $urls[0];

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Starting with '.$this->scheme);
        }
        try {
            $response = $this->client->request('GET', $url);
        } catch (GuzzleException $exception) {
            $this->logger->error($exception->getMessage(), [$exception]);
            throw $exception;
        }

        $result = json_decode((string) $response->getBody(), true);
        $totalDocuments = count($result);

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment($totalDocuments.' items found...');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsCount = 0;
        $parentClassifications = [];

        foreach ($result as $id => $description) {
            $document = new Classification();
            $document->setScheme($this->scheme);
            $document->setId($id);
            $document->setDescription($description);
            $document->setParentId('');
            $document->setSelectable(true);
            $document->setTreeLevel(0);

            ++$documentsCount;

            $needFlush = (0 === $documentsCount % 50) || ($documentsCount === $totalDocuments);
            $this->manager->save($document, $needFlush);
            if (null !== $this->inputOutput) {
                $this->inputOutput->progressAdvance(1);
            }
        }
    }
}
