<?php

namespace App\Manager;

use App\Document\Budgetary;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

/**
 * Class BudgetaryManager.
 */
class BudgetaryManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    /**
     * BudgetaryManager constructor.
     */
    public function __construct(private readonly DocumentManager $documentManager)
    {
        $this->repository = $documentManager->getRepository(Budgetary::class);
    }
}
