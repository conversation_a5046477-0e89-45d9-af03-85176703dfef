<?php

namespace App\Repository;

use App\Document\Product;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class ProductRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Product::class);
    }

    public function save(Product $filter)
    {
        $this->dm->persist($filter);
        $this->dm->flush();
    }

    public function remove(Product $filter)
    {
        $this->dm->remove($filter);
        $this->dm->flush();
    }
}
