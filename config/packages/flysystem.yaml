# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        local.market: {adapter: 'local', options: { directory: '%kernel.project_dir%/%env(FLYSYSTEM_DIRECTORY_UPLOAD)%/market' } }
        s3.market: { adapter: 'aws', options: { client: Aws\S3\S3Client, bucket: '%env(AWS_BUCKET)%', streamReads: true, prefix: '%env(FLYSYSTEM_DIRECTORY_UPLOAD)%/market' } }
        market.storage: { adapter: 'lazy', options: { source: '%env(FLYSYSTEM_ADAPTER)%.market' } }

        local.funders: { adapter: 'local', options: { directory: '%kernel.project_dir%/%env(FLYSYSTEM_DIRECTORY_UPLOAD)%/funders' } }
        s3.funders: { adapter: 'aws', options: { client: Aws\S3\S3Client, bucket: '%env(AWS_BUCKET)%', streamReads: true, prefix: '%env(FLYSYSTEM_DIRECTORY_UPLOAD)%/funders' } }
        funders.storage: { adapter: 'lazy', options: { source: '%env(FLYSYSTEM_ADAPTER)%.funders' } }