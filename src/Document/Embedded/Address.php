<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Address
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $streetAddress = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $locality = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $region = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $postalCode = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $countryName = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $countryName_en = null;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): Period
    {
        $this->id = $id;

        return $this;
    }

    public function getStreetAddress(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->streetAddress;
    }

    public function setStreetAddress(?string $streetAddress): void
    {
        $this->streetAddress = $streetAddress;
    }

    public function getLocality(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->locality;
    }

    public function setLocality(?string $locality): void
    {
        $this->locality = $locality;
    }

    public function getRegion(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->region;
    }

    public function setRegion(?string $region): void
    {
        $this->region = $region;
    }

    public function getPostalCode(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getCountryName(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->countryName;
    }

    public function setCountryName(?string $countryName): void
    {
        $this->countryName = $countryName;
    }

    public function getCountryNameEn(): ?string
    {
        if ($this->restrictedMode) {
            return 'Hidden';
        }

        return $this->countryName_en;
    }

    public function setCountryNameEn(?string $countryName_en): void
    {
        $this->countryName_en = $countryName_en;
    }
}
