<?php

namespace App\MessageHandler;

use App\Message\MongoToEsMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class MongoToEsMessageHandler
{
    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger,
        private ObjectPersisterInterface $tenderPersister,
        private ObjectPersisterInterface $contractPersister,
        private ObjectPersisterInterface $planPersister,
        private ObjectPersisterInterface $inspectionPersister,
        private ObjectPersisterInterface $monitoringPersister,
        private ObjectPersisterInterface $categoryPersister,
        private ObjectPersisterInterface $profilePersister,
        private ObjectPersisterInterface $productPersister
    ) {
    }

    public function __invoke(MongoToEsMessage $message)
    {
        try {
            $documentClass = DocumentType::getDocumentClass($message->type);
            $document = $this->documentManager->getRepository($documentClass)->find($message->id);

            if ($message->type === DocumentType::$TENDER) {
                $this->tenderPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$CONTRACT) {
                $this->contractPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$PLAN) {
                $this->planPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$INSPECTION) {
                $this->inspectionPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$MONITORING) {
                $this->monitoringPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$PROFILE) {
                $this->profilePersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$PRODUCT) {
                $this->productPersister->replaceOne($document);
            } elseif ($message->type === DocumentType::$CATEGORY) {
                $this->categoryPersister->replaceOne($document);
            } else {
                throw new \Error('Unknown type: '.$message->type);
            }
        } catch (\Throwable $e) {
            $this->logger->critical('Unhandled Error received: '.$e->getMessage(), [$this, $e]);
            throw $e;
        }
    }
}
