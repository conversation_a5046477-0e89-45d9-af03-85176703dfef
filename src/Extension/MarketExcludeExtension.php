<?php

namespace App\Extension;

use ApiPlatform\Elasticsearch\Extension\RequestBodySearchCollectionExtensionInterface;
use ApiPlatform\Metadata\Operation;
use App\Resource\CategoryView;
use App\Resource\ProductView;
use App\Resource\ProfileView;
use App\Constants\DzoConstants;

/**
 * Class ElasticExcludeExtension.
 */
class MarketExcludeExtension implements RequestBodySearchCollectionExtensionInterface
{
    private const VIEWS = [
        CategoryView::class,
        ProductView::class,
        ProfileView::class,
    ];

    public function applyToCollection(
        array $requestBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): array {
        if (!in_array($resourceClass, self::VIEWS)) {
            return $requestBody;
        }

        $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
            'bool' => [
                'must' => [
                    'terms' => [
                        'status' => ['active'],
                    ],
                ],
            ],
        ];

        if (in_array($resourceClass, [ProductView::class, ProfileView::class])) {
            $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
                'bool' => [
                    'must_not' => [
                        'terms' => [
                            'relatedCategoryStatus' => ['hidden'],
                        ],
                    ],
                ],
            ];
            $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
                'bool' => [
                    'must_not' => [
                        [
                            'term' => [
                                'dzoCategoryDataTypesMatch' => false,
                            ],
                        ]
                    ],
                ],
            ];
        }


        if ($resourceClass === ProductView::class) {
            $criterionId = DzoConstants::LOCAL_ORIGIN_LEVEL_CLASSIFICATION_ID;
            $signatureFileName = DzoConstants::SIGNATURE_FILE_NAME;
            $hasLocalizationCriterion = [
                'nested' => [
                    'path' => 'requirementResponses.classification',
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'match' => [
                                        'requirementResponses.classification.id' => $criterionId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $hasSignature = [
                'nested' => [
                    'path' => 'documents',
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'match' => [
                                        'documents.title' => $signatureFileName
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
                'bool' => [
                    'should' => [
                        [
                            'bool' => [
                                'must_not' => [$hasLocalizationCriterion]
                            ]
                        ],
                        $hasSignature
                    ],
                    'minimum_should_match' => 1
                ]
            ];
        }

        $requestBody['query']['constant_score']['filter']['bool']['must_not'][] = [
            'term' => [
                'classification.id' => '99999999-9', // restricted classification to show
            ],
        ];

        return $requestBody;
    }
}
