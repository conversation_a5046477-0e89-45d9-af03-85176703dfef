<?php

namespace App\MessageHandler;

use App\Document\Plan;
use App\Document\PlanIdentifier;
use App\Message\PlanMessage;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;

#[AsMessageHandler]
class PlanHandler extends BaseHandler
{
    /**
     * @throws GuzzleException
     * @throws MongoDBException
     * @throws \Throwable
     */
    public function __invoke(PlanMessage $plan)
    {
        try {
            $response = $this->requestPublicApi('plans/'.$plan->getId());

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];

            // sometimes for Plan we have id field in procuringEntity which fails in doctrine, need to drop it
            if (array_key_exists('procuringEntity', $data)) {
                unset($data['procuringEntity']['id']);
            }

            /** @var Plan $object */
            $object = $this->getDocumentManager()->find(Plan::class, $data['id']);

            $object = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data, JSON_THROW_ON_ERROR),
                    Plan::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
                );

            //        /** uncomment to debug with raw data */
            //        //@todo remove or comment for prod
            //        $object->setData($data);

            if ($object->getTenderId()) {
                $this->processTender($object->getTenderId(), false);
            }

            if (array_key_exists('procuringEntity', $data) && array_key_exists('identifier', $data['procuringEntity'])) {
                $this->handleIdentifier($data['procuringEntity']['identifier'], PlanIdentifier::class, false);
            }

            $this->save($object);
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
            throw $exception;
        }
    }
}
