<?php

namespace App\Dto;

class CartItemConfigDto
{
    public string $dataType;

    public string $title;

    public mixed $expectedValue;

    public ?array $expectedValues;

    public ?int $minValue;

    public ?int $maxValue;

    public function __construct(array $config)
    {
        $this->dataType = $config['dataType'];
        $this->title = $config['title'];
        $this->expectedValue = $config['expectedValue'] ?? null;
        $this->expectedValues = $config['expectedValues'] ?? [];
        $this->minValue = $config['minValue'] ?? null;
        $this->maxValue = $config['maxValue'] ?? null;
    }
}
