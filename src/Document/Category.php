<?php

namespace App\Document;

use App\Document\Embedded\EmbeddedClassification;
use App\Document\Embedded\Image;
use App\Document\Embedded\ProcuringEntity;
use App\Document\Embedded\Unit;
use App\Repository\CategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document(repositoryClass: CategoryRepository::class)]
class Category
{
    #[MongoDB\Id]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $prozorroId = null;

    #[MongoDB\EmbedOne(targetDocument: ProcuringEntity::class)]
    protected ?ProcuringEntity $procuringEntity = null;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedClassification::class)]
    protected EmbeddedClassification $classification;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\EmbedMany(targetDocument: Image::class)]
    protected Collection|array $images;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::DESC)]
    private ?\DateTimeInterface $dateModified = null;

    // this field is just added to add more consistency
    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\EmbedOne(targetDocument: Unit::class)]
    private ?Unit $unit;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $agreementID;
    #[MongoDB\Field(type: Type::RAW)]
    private $criteria;

    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    private mixed $additionalClassifications = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private int $popularity = 0;

    public function __construct()
    {
        $this->images = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getProcuringEntity(): ?ProcuringEntity
    {
        return $this->procuringEntity;
    }

    public function setProcuringEntity(?ProcuringEntity $procuringEntity): void
    {
        $this->procuringEntity = $procuringEntity;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getImages(): Collection|array
    {
        return $this->images;
    }

    public function setImages(Collection|array $images): void
    {
        $this->images = $images;
    }

    public function addImage(Image $image): void
    {
        if (!$this->images->contains($image)) {
            $this->images->add($image);
        }
    }

    public function getProzorroId(): ?string
    {
        return $this->prozorroId;
    }

    public function setProzorroId(?string $prozorroId): void
    {
        $this->prozorroId = $prozorroId;
    }

    public function getClassification(): EmbeddedClassification
    {
        return $this->classification;
    }

    public function setClassification(EmbeddedClassification $classification): void
    {
        $this->classification = $classification;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    /**
     * @throws \Exception
     */
    public function setDateModified($dateModified): void
    {
        if (is_string($dateModified)) {
            $dateModified = new \DateTime($dateModified);
        }
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws \Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new \DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getUnit(): ?Unit
    {
        return $this->unit;
    }

    public function setUnit(?Unit $unit): void
    {
        $this->unit = $unit;
    }

    public function getAgreementID(): ?string
    {
        return $this->agreementID;
    }

    public function setAgreementID(?string $agreementID): void
    {
        $this->agreementID = $agreementID;
    }

    public function getCriteria()
    {
        return $this->criteria;
    }

    public function setCriteria($criteria): void
    {
        $this->criteria = $criteria;
    }

    public function getAdditionalClassifications(): mixed
    {
        return $this->additionalClassifications;
    }

    public function setAdditionalClassifications(mixed $additionalClassifications): Category
    {
        $this->additionalClassifications = $additionalClassifications;

        return $this;
    }

    public function getPopularity(): int
    {
        return $this->popularity;
    }

    public function setPopularity(int $popularity): Category
    {
        $this->popularity = $popularity;
        return $this;
    }
}
