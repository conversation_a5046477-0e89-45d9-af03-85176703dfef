<?php

namespace App\Command;

use App\Message\CategoryStatusSetterMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class MarketCategoryStatusSetterCommand extends Command
{
    protected static $defaultName = 'app:market-category-status-setter';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct(self::$defaultName);
    }

    protected function configure()
    {
        $this->setDescription('Set category status for products and profiles');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $documentClass = DocumentType::getDocumentClass(DocumentType::$CATEGORY);
        $categories = $this->documentManager->getRepository($documentClass)->findAll();

        if (!empty($categories)) {
            foreach ($categories as $category) {
                $this->messageBus->dispatch(new CategoryStatusSetterMessage($category->getId()));
            }
        }

        return 0;
    }
}
