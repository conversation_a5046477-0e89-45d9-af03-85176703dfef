App\Resource\ProfileView:
  attributes:
    id:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list', 'profile:links', 'product:list', 'product:read', 'category:read']
    prozorroId:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]
    status:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list', 'product:list', 'product:read', 'profile:links', 'category:read' ]
    title:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:links', 'product:list', 'product:read', 'category:read', 'profile:list' ]
    description:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    agreementID:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    agreement:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    owner:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    criteria:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    dateModified:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]
    dateCreated:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]
    classification:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    category:
      groups: [ 'profile:read', 'profile:cdb_parse']
    relatedCategory:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]
    relatedCategoryId:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]
    value:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    unit:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    additionalClassifications:
      groups: [ 'profile:read', 'profile:cdb_parse' ]
    images:
      groups: [ 'profile:read', 'profile:cdb_parse', 'profile:list' ]