<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\EmbeddedClassification;

class BudgetBreakdownClassificationView
{
    private ?string $id = null;
    
    private ?string $description = null;
    
    private ?string $scheme = null;
    
    private ?string $uri = null;

    public function __construct(EmbeddedClassification $classification)
    {
        $this->id = $classification->getId();
        $this->description = $classification->getDescription();
        $this->scheme = $classification->getScheme();
        $this->uri = $classification->getUri();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getScheme(): ?string
    {
        return $this->scheme;
    }

    public function getUri(): ?string
    {
        return $this->uri;
    }
}
