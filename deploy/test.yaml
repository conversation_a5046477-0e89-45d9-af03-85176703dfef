replicaCount: 1
fullnameOverride: "search"

#initContainers:
#  args:
#    - "doctrine:mongodb:schema:update"
#    - "--skip-search-indexes"

annotations:
  reloader.stakater.com/auto: "true"

configMapName: "dzo-search-worker"

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dzo-market-eks-role

ingress:
  enabled: true
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/client-body-buffer-size: "108M"
  hosts:
    - host: test-search.dzo.com.ua
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
   - secretName: dzo
     hosts:
       - test-search.dzo.com.ua

resources:
  limits:
    memory: 4096Mi
  requests:
    cpu: 100m
    memory: 315Mi
