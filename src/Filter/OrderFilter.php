<?php

namespace App\Filter;

use ApiPlatform\Api\ResourceClassResolverInterface;
use ApiPlatform\Elasticsearch\Filter\AbstractFilter;
use ApiPlatform\Elasticsearch\Filter\SortFilterInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\Metadata\Property\Factory\PropertyMetadataFactoryInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyNameCollectionFactoryInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;

/**
 * Class OrderFilter.
 */
final class OrderFilter extends AbstractFilter implements SortFilterInterface
{
    public function __construct(PropertyNameCollectionFactoryInterface $propertyNameCollectionFactory, PropertyMetadataFactoryInterface $propertyMetadataFactory, ResourceClassResolverInterface $resourceClassResolver, ?NameConverterInterface $nameConverter = null, private readonly string $orderParameterName = 'order', ?array $properties = null)
    {
        parent::__construct($propertyNameCollectionFactory, $propertyMetadataFactory, $resourceClassResolver, $nameConverter, $properties);
    }

    public function apply(array $clauseBody, string $resourceClass, ?Operation $operation = null, array $context = []): array
    {
        if (!\is_array($properties = $context['filters'][$this->orderParameterName] ?? [])) {
            return $clauseBody;
        }

        $orders = [];

        foreach ($properties as $property => $direction) {
            //            [$type] = $this->getMetadata($resourceClass, $property);
            //
            //            if (!$type) {
            //                continue;
            //            }

            if (empty($direction) && null !== $defaultDirection = $this->properties[$property] ?? null) {
                $direction = $defaultDirection;
            }

            if (!\in_array($direction = strtolower($direction), ['asc', 'desc'], true)) {
                continue;
            }

            $order = ['order' => $direction];

            if (null !== $nestedPath = $this->getNestedFieldPath($resourceClass, $property)) {
                $nestedPath = null === $this->nameConverter ? $nestedPath : $this->nameConverter->normalize($nestedPath, $resourceClass, null, $context);
                $order['nested'] = ['path' => $nestedPath];
            }

            $property = null === $this->nameConverter ? $property : $this->nameConverter->normalize($property, $resourceClass, null, $context);
            $orders[] = [$property => $order];
        }

        if (!$orders) {
            return $clauseBody;
        }

        return array_merge_recursive($clauseBody, $orders);
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        foreach (array_keys($this->properties) as $property) {
            $description["$this->orderParameterName[$property]"] = [
                'property' => $property,
                'type' => 'string',
                'required' => false,
            ];
        }

        return $description;
    }
}
