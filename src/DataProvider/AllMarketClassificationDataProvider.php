<?php

declare(strict_types=1);

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Service\MarketCacheService;

final readonly class AllMarketClassificationDataProvider implements ProviderInterface
{
    public function __construct(
        private MarketCacheService $marketCacheService
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        return $this->marketCacheService->getMarketClassificationsCache();
    }
}
