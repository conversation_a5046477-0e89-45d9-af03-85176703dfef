name: Run OldId Generate Command

on:
  workflow_dispatch:
    inputs:
      first_param:
        description: "First parameter (0 or 1)"
        required: true
        default: "0"
      max_id:
        description: "Max ID value"
        required: true

jobs:
  run-command:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-production --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Execute command inside search service pod
        run: |
          POD_NAME=$(kubectl get pods -n prod -l app.kubernetes.io/instance=search -o jsonpath="{.items[0].metadata.name}")
          echo "Executing command in pod: $POD_NAME"
          kubectl exec -n prod $POD_NAME -- bin/console app:old-id:generate ${{ github.event.inputs.first_param }} $(( ${{ github.event.inputs.max_id }} + 1 )) tender
