phpWorkers:
  enabled: true
  workers:
    - name: tenders
      args: [ "messenger:consume", "tenders" ]
      replicaCount: 10
    - name: contracts
      args: [ "messenger:consume", "contracts" ]
      replicaCount: 5
    - name: inspections
      args: [ "messenger:consume", "inspections" ]
      replicaCount: 5
    - name: monitorings
      args: [ "messenger:consume", "monitorings" ]
      replicaCount: 5
    - name: plans
      args: [ "messenger:consume", "plans" ]
      replicaCount: 5
    - name: categories
      args: [ "messenger:consume", "categories" ]
      replicaCount: 1
    - name: profiles
      args: [ "messenger:consume", "profiles" ]
      replicaCount: 2
    - name: products
      args: [ "messenger:consume", "products" ]
      replicaCount: 4
    - name: search-mongo-to-es
      args: [ "messenger:consume", "search_mongo_to_es" ]
    - name: category-status-setter
      args: [ "messenger:consume", "category_status_setter"]
    - name: agreements
      args: [ "messenger:consume", "agreements" ]
    - name: old-id-setter
      args: [ "messenger:consume", "old_id_setter"]
    - name: bids
      args: [ "messenger:consume", "bids"]
    - name: bid-errors
      args: [ "messenger:consume", "bid_errors"]
    - name: popularity-setter
      args: [ "messenger:consume", "popularity_setter", "--memory-limit=256M", "--time-limit=3600" ]
    - name: update-visibility-flags
      args: [ "messenger:consume", "update_visibility_flags", "--memory-limit=256M", "--time-limit=3600" ]



  configMapName: dzo-search-worker

  serviceAccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dzo-market-eks-role
