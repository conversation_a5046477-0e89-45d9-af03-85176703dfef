App\Document\Embedded\ProcuringEntity:
  attributes:
    id:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    name:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    identifier:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    additionalIdentifiers:
      groups: [
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    address:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    contactPoint:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
    kind:
      groups: [
        'tender:list',
        'tender:read',
        'tender:cdb_parse',
        'category:list',
        'category:read'
      ]
