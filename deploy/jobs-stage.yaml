defaultConfigFrom: "dzo-search-worker"

jobServiceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dzo-market-eks-role

jobs:
  - args: ["api:budgetary:parse", "sources/departments.json"]
  - args: ["api:classifications:parse", "gmdn"]
  - args: ["api:classifications:parse", "ccce_ua"]
  - args: ["api:classifications:parse", "cpv"]
  - args: ["api:classifications:parse", "inn"]
  - args: ["api:classifications:parse", "roads"]
  - args: ["app:sync:name-funders-list"]

cronjobs:
  - name: "market-cache"
    schedule: "0 0 * * *"
    args: ["app:market:generate-products-cache"]
  - name: "market-generate-cache"
    schedule: "10 0 * * *"
    args: [ "app:market:generate-cache" ]
  - name: "popularity-increase"
    schedule: "15 0 * * *"
    args: [ "app:popularity:queue", "increase" ]
  - name: "popularity-decrease"
    schedule: "30 0 * * *"
    args: [ "app:popularity:queue", "decrease" ]
