<?php

namespace App\Manager;

use App\Document\Profile;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use FOS\ElasticaBundle\Finder\FinderInterface;

class ProfileManager extends AbstractManager
{
    use ManagerTrait;

    private DocumentRepository $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly FinderInterface $profilesFinder,
    ) {
        $this->repository = $documentManager->getRepository(Profile::class);
    }

    public function find($id)
    {
        return $this->getRepository()->find($id);
    }

    public function findByCategory(string $categoryId, int $limit = 21): array
    {
        $query = [
            'query' => [
                'bool' => [
                    'must'     => [
                        'match' => [
                            'relatedCategoryId' => $categoryId,
                        ],
                    ],
                    'must_not' => [
                        'terms' => [
                            'status' => ['hidden', 'general']
                        ]
                    ]
                ],
            ],
        ];

        // somewhere inside the elastica finder we got limit 10, so we need set limit by ourselves
        return $this->profilesFinder->find($query, $limit);
    }

    public function findByIds(array $ids): array
    {
        return $this->getRepository()->createQueryBuilder()
            ->field('id')->in($ids)
            ->field('status')->equals('active')
            ->getQuery()->execute()->toArray();
    }
}
