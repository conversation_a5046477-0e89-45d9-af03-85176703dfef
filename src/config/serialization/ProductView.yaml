App\Resource\ProductView:
  attributes:
    id:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list', 'category:list', 'category:read' ]
    prozorroId:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    classification:
      groups: [ 'product:read', 'product:cdb_parse' ]
    category:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    categoryId:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    identifier:
      groups: [ 'product:read', 'product:cdb_parse' ]
    status:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    owner:
      groups: [ 'product:read', 'product:cdb_parse' ]
    title:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list', 'category:list', 'category:read' ]
    description:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    dateModified:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    dateCreated:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    expirationDate:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    images:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    profiles:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    categoryProfiles:
      groups: [ 'product:read', 'product:cdb_parse' ]
    profileIds:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    brand:
      groups: [ 'product:read', 'product:cdb_parse' ]
    requirementResponses:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    productRequirements:
      groups: [ 'product:read', 'product:cdb_parse', 'product:list' ]
    manufacturers:
      groups: [ 'product:read', 'product:cdb_parse' ]
    additionalProperties:
      groups: [ 'product:read', 'product:cdb_parse' ]
    additionalClassifications:
      groups: [ 'product:read', 'product:cdb_parse' ]
    vendor:
      groups: [ 'product:read', 'product:cdb_parse' ]
    documents:
      groups: [ 'product:read', 'product:cdb_parse' ]