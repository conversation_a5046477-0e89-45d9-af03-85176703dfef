<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\MonitoringManager;
use App\Resource\MonitoringView;

/**
 * Class MonitoringDataProvider.
 */
final class MonitoringDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly MonitoringManager $monitoringManager
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->monitoringManager->find($uriVariables['id']);
        if (!$document) {
            return null;
        }

        return new MonitoringView($document);
    }
}
