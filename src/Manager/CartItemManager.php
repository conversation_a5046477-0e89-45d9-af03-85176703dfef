<?php

namespace App\Manager;

use App\Document\CartItem;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

class CartItemManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    public function __construct(private readonly DocumentManager $documentManager)
    {
        $this->repository = $documentManager->getRepository(CartItem::class);
    }

    /**
     * @return CartItem|null
     *
     * @throws LockException
     * @throws MappingException
     */
    public function find($id)
    {
        return $this->getRepository()->find($id);
    }

    public function removeCollection($cartItems): void
    {
        foreach ($cartItems as $cartItem) {
            $this->remove($cartItem);
        }
    }

    public function remove(CartItem $cartItem): void
    {
        $this->getRepository()->remove($cartItem);
    }
}
