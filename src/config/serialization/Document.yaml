App\Document\Embedded\Document:
  attributes:
    id:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    hash:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    description:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    title:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    url:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    format:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    titleEn:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
      serialized_name: 'title_en'
    titleRu:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
      serialized_name: 'title_ru'
    documentOf:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    datePublished:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    author:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    dateModified :
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    documentType:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    index:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    language:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]
    relatedItem:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list',
          'product:read'
      ]