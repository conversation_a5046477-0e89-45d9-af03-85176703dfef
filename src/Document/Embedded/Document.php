<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class Document
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $hash = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $url = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $format = null;

    #[MongoDB\Field(name: 'title_en', type: Type::STRING, nullable: true)]
    private ?string $titleEn = null;

    #[MongoDB\Field(name: 'title_ru', type: Type::STRING, nullable: true)]
    private ?string $titleRu = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $documentOf = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $datePublished = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $author = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $dateModified = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $documentType;

    #[MongoDB\Field(type: Type::RAW)]
    private $index;

    #[MongoDB\Field(type: Type::RAW)]
    private $language;

    #[MongoDB\Field(type: Type::RAW)]
    private $relatedItem;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode, bool $onlySign): void
    {
        if ($onlySign) {
            if ('sign.p7s' === $this->title || 'notice' === $this->documentType) {
                $this->restrictedMode = $restrictedMode;
            }
        } else {
            $this->restrictedMode = $restrictedMode;
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(?string $hash): void
    {
        $this->hash = $hash;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getTitle(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getUrl(): ?string
    {
        if ($this->restrictedMode) {
            return '00000000000000000000000000000000';
        }

        return $this->url;
    }

    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }

    public function getFormat(): ?string
    {
        return $this->format;
    }

    public function setFormat(?string $format): void
    {
        $this->format = $format;
    }

    public function getTitleEn(): ?string
    {
        if ($this->restrictedMode) {
            return 'Hidden';
        }

        return $this->titleEn;
    }

    public function setTitleEn(?string $titleEn): void
    {
        $this->titleEn = $titleEn;
    }

    public function getTitleRu(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->titleRu;
    }

    public function setTitleRu(?string $titleRu): void
    {
        $this->titleRu = $titleRu;
    }

    public function getDocumentOf(): ?string
    {
        return $this->documentOf;
    }

    public function setDocumentOf(?string $documentOf): void
    {
        $this->documentOf = $documentOf;
    }

    public function getDatePublished(): ?string
    {
        return $this->datePublished;
    }

    public function setDatePublished(?string $datePublished): void
    {
        $this->datePublished = $datePublished;
    }

    public function getAuthor(): ?string
    {
        return $this->author;
    }

    public function setAuthor(?string $author): void
    {
        $this->author = $author;
    }

    public function getDateModified(): ?string
    {
        return $this->dateModified;
    }

    public function setDateModified(?string $dateModified): void
    {
        $this->dateModified = $dateModified;
    }

    public function getDocumentType()
    {
        return $this->documentType;
    }

    public function setDocumentType($documentType): void
    {
        $this->documentType = $documentType;
    }

    public function getIndex()
    {
        return $this->index;
    }

    public function setIndex($index): void
    {
        $this->index = $index;
    }

    public function getLanguage()
    {
        return $this->language;
    }

    public function setLanguage($language): void
    {
        $this->language = $language;
    }

    public function getRelatedItem()
    {
        return $this->relatedItem;
    }

    public function setRelatedItem($relatedItem): void
    {
        $this->relatedItem = $relatedItem;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
