<?php

namespace App\Helper;

trait RestrictedDataHelper
{
    private function getRestrictedAuthor(array $author): array
    {
        if (array_key_exists('identifier', $author)) {
            $author['identifier'] = array_merge($author['identifier'], [
                'id' => '00000000000000000000',
                'legalName' => 'Приховано',
                'legalName_ru' => 'Скрыто',
                'legalName_en' => 'Hidden',
            ]);
        }

        if (array_key_exists('address', $author)) {
            $author['address'] = $this->restrictAddressData($author['address']);
        }

        if (array_key_exists('contactPoint', $author)) {
            $author['contactPoint'] = $this->restrictContactPointData($author['contactPoint']);
        }

        return array_merge($author, [
            'name' => 'Приховано',
            'name_ru' => 'Скрыто',
            'name_en' => 'Hidden',
        ]);
    }

    private function restrictComplaints(array $complaints): array
    {
        foreach ($complaints as &$restrictedComplaint) {
            if (array_key_exists('author', $restrictedComplaint)) {
                $restrictedComplaint['author'] = $this->getRestrictedAuthor($restrictedComplaint['author']);
            }

            if (array_key_exists('documents', $restrictedComplaint)) {
                $restrictedComplaint['documents'] = array_map(function ($document) {
                    return $this->restrictDocumentData($document);
                }, $restrictedComplaint['documents']);
            }

            if (array_key_exists('posts', $restrictedComplaint)) {
                $restrictedComplaint['posts'] = array_map(function ($post) {
                    if (array_key_exists('documents', $post)) {
                        $post['documents'] = array_map(function ($document) {
                            return $this->restrictDocumentData($document);
                        }, $post['documents']);
                    }

                    return array_merge($post, [
                        'title' => 'Приховано',
                        'description' => 'Приховано',
                    ]);
                }, $restrictedComplaint['posts']);
            }

            if (array_key_exists('objections', $restrictedComplaint)) {
                $restrictedComplaint['objections'] = array_map(function ($objection) {
                    if (array_key_exists('classification', $objection)) {
                        $objection['classification'] = array_merge($objection['classification'], [
                            'description' => 'Приховано',
                        ]);
                    }

                    if (array_key_exists('requestedRemedies', $objection)) {
                        $objection['requestedRemedies'] = array_map(function ($requestedRemedy) {
                            return array_merge($requestedRemedy, [
                                'type' => 'Приховано',
                                'description' => 'Приховано',
                            ]);
                        }, $objection['requestedRemedies']);
                    }

                    if (array_key_exists('arguments', $objection)) {
                        $objection['arguments'] = array_map(function ($argument) {
                            if (array_key_exists('evidences', $argument)) {
                                $argument['evidences'] = array_map(function ($evidence) {
                                    return array_merge($evidence, [
                                        'title' => 'Приховано',
                                        'description' => 'Приховано',
                                    ]);
                                }, $argument['evidences']);
                            }

                            return array_merge($argument, [
                                'description' => 'Приховано',
                            ]);
                        }, $objection['arguments']);
                    }

                    return array_merge($objection, [
                        'title' => 'Приховано',
                        'description' => 'Приховано',
                    ]);
                }, $restrictedComplaint);
            }

            $restrictedComplaint = array_merge($restrictedComplaint, [
                'title' => 'Приховано',
                'description' => 'Приховано',
                'resolution' => 'Приховано',
                'tendererAuction' => 'Приховано',
                'decision' => 'Приховано',
                'rejectReasonDescription' => 'Приховано',
                'reviewPlace' => 'Приховано',
                'cancellationReason' => 'Приховано',
            ]);
        }

        return $complaints;
    }

    private function restrictDocumentData(array $document): array
    {
        return array_merge($document, [
            'title' => 'Приховано',
            'title_ru' => 'Скрыто',
            'title_en' => 'Hidden',
            'url' => '00000000000000000000000000000000',
        ]);
    }

    private function restrictCompanyData(array $company): array
    {
        if (array_key_exists('identifier', $company)) {
            $company['identifier'] = array_merge($company['identifier'], [
                'id' => '00000000000000000000',
                'legalName' => 'Приховано',
                'legalName_ru' => 'Скрыто',
                'legalName_en' => 'Hidden',
            ]);
        }

        if (array_key_exists('address', $company)) {
            $company['address'] = $this->restrictAddressData($company['address']);
        }

        if (array_key_exists('contactPoint', $company)) {
            $company['contactPoint'] = $this->restrictContactPointData($company['contactPoint']);
        }

        if (array_key_exists('signerInfo', $company)) {
            $company['signerInfo'] = $this->restrictSignerInfoData($company['signerInfo']);
        }

        return array_merge($company, [
            'name' => 'Приховано',
            'name_ru' => 'Скрыто',
            'name_en' => 'Hidden',
            'scale' => 'Приховано',
        ]);
    }

    private function restrictPeriodData(array $period): array
    {
        return array_merge($period, [
            'startDate' => '1970-01-01T00:00:00Z',
            'endDate' => '1970-01-01T00:00:00Z',
        ]);
    }

    private function restrictAddressData(array $address): array
    {
        return array_merge($address, [
            'streetAddress' => 'Приховано',
            'locality' => 'Приховано',
            'region' => 'Приховано',
            'postalCode' => 'Приховано',
            'countryName' => 'Приховано',
            'countryName_ru' => 'Скрыто',
            'countryName_en' => 'Hidden',
        ]);
    }

    private function restrictLocationData(array $location): array
    {
        return array_merge($location, [
            'latitude' => 'Приховано',
            'longitude' => 'Приховано',
        ]);
    }

    private function restrictContactPointData(array $contactPoint): array
    {
        return array_merge($contactPoint, [
            'name' => 'Приховано',
            'name_ru' => 'Скрыто',
            'name_en' => 'Hidden',
            'email' => 'Приховано',
            'telephone' => 'Приховано',
            'url' => 'Приховано',
        ]);
    }

    private function restrictSignerInfoData(array $signerInfo): array
    {
        return array_merge($signerInfo, [
            'name' => 'Приховано',
            'email' => 'Приховано',
            'telephone' => 'Приховано',
            'position' => 'Приховано',
            'authorizedBy' => 'Приховано',
            'iban' => '00000000000000000000000000000000',
        ]);
    }
}
