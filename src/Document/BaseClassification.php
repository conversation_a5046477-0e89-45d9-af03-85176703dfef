<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\MappedSuperclass]
abstract class BaseClassification
{
    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $scheme = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $uri = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getScheme(): ?string
    {
        return $this->scheme;
    }

    public function setScheme(?string $scheme): void
    {
        $this->scheme = $scheme;
    }

    public function getUri(): ?string
    {
        return $this->uri;
    }

    public function setUri(?string $uri): void
    {
        $this->uri = $uri;
    }
}
