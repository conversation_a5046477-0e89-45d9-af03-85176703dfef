<?php

namespace App\Legacy\Repository;

use App\Legacy\Helper\DatabaseHelper;

class LegacyPlanRepository extends LegacyRepository implements LegacyOldIdSetterInterface
{
    public function getForOldIdSetter(int $start, int $limit, bool $isArchive = false): false|array
    {
        $query = 'SELECT p.id, p.plan_id FROM ' . DatabaseHelper::getTableName('plan') . ' AS p ' .
            'WHERE p.id < :start AND p.plan_id IS NOT NULL AND p.plan_id != "" ORDER BY p.id DESC LIMIT ' . $limit;

        if (!$isArchive) {
            $query = $this->connection->prepare($query);
        } else {
            $query = $this->archiveConnection->prepare($query);
        }

        $query->bindValue('start', $start);


        return $query->executeQuery()->fetchAllAssociative();
    }
}