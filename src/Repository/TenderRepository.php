<?php

namespace App\Repository;

use App\Document\Tender;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class TenderRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Tender::class);
    }

    /**
     * @return void
     *
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    public function save(Tender $tender, bool $flush = true)
    {
        $this->dm->persist($tender);
        if ($flush) {
            $this->dm->flush();
        }
    }

    public function findPqTenders()
    {
        return $this->createQueryBuilder()
            ->field('noticePublicationDate')
            ->gte((new \DateTime())->modify('-30 days'))->lte(new \DateTime())
            ->field('procurementMethod')->equals('selective')
            ->field('procurementMethodType')->equals('priceQuotation')
            ->getQuery()
            ->execute();
    }

    public function findPq30dTenders()
    {
        return $this->createQueryBuilder()
            ->field('noticePublicationDate')
            ->gte((new \DateTime())->modify('-31 days'))->lte((new \DateTime())->modify('-30 days'))
            ->field('procurementMethod')->equals('selective')
            ->field('procurementMethodType')->equals('priceQuotation')
            ->getQuery()
            ->execute();
    }

    public function findPqYesterdayTenders()
    {
        return $this->createQueryBuilder()
            ->field('noticePublicationDate')
            ->gte((new \DateTime())->modify('-1 day'))->lte(new \DateTime())
            ->field('procurementMethod')->equals('selective')
            ->field('procurementMethodType')->equals('priceQuotation')
            ->getQuery()
            ->execute();
    }
}
