<?php

namespace App\Filter;

/**
 * Class ValueRangeFilter.
 */
final class ValueRangeFilter extends BaseRangeFilter
{
    public function getRangeFieldType(): string
    {
        return 'float';
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        // TODO: Implement Currency filter here???
        return parent::getQuery($property, $values, $nestedPath);
    }

    public function getRangeQuery(string $property, $from = null, $to = null): array
    {
        $query = [];
        if ($from) {
            $query['gte'] = (float) $from;
        }
        if ($to) {
            $query['lte'] = (float) $to;
        }

        return [
            'range' => [
                $property.'.amount' => $query,
            ],
        ];
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];
        foreach ($this->getProperties(self::class) as $property) {
            $description[$property.'.from'] = [
                'property' => $property.'.from',
                'type' => 'float',
                'required' => false,
            ];
            $description[$property.'.to'] = [
                'property' => $property.'.to',
                'type' => 'float',
                'required' => false,
            ];
            $description[$property.'.currency'] = [
                'property' => $property.'.currency',
                'type' => 'string',
                'required' => false,
            ];
        }

        return $description;
    }
}
