<?php

namespace App\Document\Embedded;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class DzoData
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::RAW)]
    private array $favoriteUsersMap = [];

    #[MongoDB\Field(type: Type::RAW)]
    private array $bidUserMap = [];

    #[MongoDB\EmbedMany(targetDocument: Bid::class)]
    protected Collection|array $bids;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $ownerId = null;

    public function __construct()
    {
        $this->bids = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function addFavoriteUser(string $userId): void
    {
        $this->favoriteUsersMap[$userId] = true;
    }

    public function removeFavoriteUser(string $userId): void
    {
        unset($this->favoriteUsersMap[$userId]);
    }

    public function getFavoriteUsersMap(): array
    {
        return $this->favoriteUsersMap;
    }

    public function isFavorite(string $userId): bool
    {
        return isset($this->favoriteUsersMap[$userId]);
    }

    public function getFavoriteUsers(): array
    {
        $result = [];
        foreach ($this->favoriteUsersMap as $userId => $value) {
            $obj = new \stdClass();
            $obj->id = $userId;
            $result[] = $obj;
        }

        return $result;
    }

    public function getBidUserMap(): array
    {
        return $this->bidUserMap;
    }

    public function addBidsUser(string $userId): void
    {
        $this->bidUserMap[$userId] = true;
    }

    public function isBids(string $userId): bool
    {
        return isset($this->bidUserMap[$userId]);
    }

    public function getBidUsers()
    {
        $result = [];
        foreach ($this->bidUserMap as $userId => $value) {
            $obj = new \stdClass();
            $obj->id = $userId;
            $result[] = $obj;
        }

        return $result;
    }

    public function getOwnerId(): ?string
    {
        return $this->ownerId;
    }

    public function addOwnerId(string $userId): void
    {
        $this->ownerId = $userId;
    }

    public function isOwner(string $userId): bool
    {
        return $this->ownerId === $userId;
    }

    public function getBids(): Collection|array
    {
        return $this->bids;
    }

    public function setBids(Collection|array $bids): DzoData
    {
        $this->bids = $bids;
        return $this;
    }
}
