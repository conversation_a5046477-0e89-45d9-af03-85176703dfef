<?php

namespace App\Service;

use App\Document\Profile;
use App\Dto\CartItemConfigDto;
use App\Dto\CartItemDto;
use App\Manager\ProductManager;
use Doctrine\Common\Collections\ArrayCollection;

readonly class CategoryCheckService
{
    public function __construct(
        private ProductManager $productManager
    ) {
    }

    public function checkGeneral(CartItemDto $cartItemDto): bool
    {
        if (!empty($cartItemDto->config)) {
            $requirementsCollection = new ArrayCollection();
            foreach ($cartItemDto->config as $requirement) {
                $requirementsCollection->add(new CartItemConfigDto($requirement));
            }
            if ($this->productManager->findProductByRequirements($requirementsCollection)) {
                return true;
            }
        }

        return false;
    }
}
