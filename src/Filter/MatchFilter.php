<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

/**
 * Class MatchFilter.
 */
final class MatchFilter extends AbstractSearchFilter
{
    private const PROPERTIES = [
        'relatedProfileIds',
        'relatedCategoryId',
        'milestonesCode',
        'monitoringIds',
        'procurementMethodTypeFilter',
    ];

    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): array {
        $searches = [];

        foreach ($context['filters'] ?? [] as $property => $values) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }

            if (!is_array($values)) {
                $values = [$values];
            }

            [$type, $hasAssociation, $nestedResourceClass, $nestedProperty] = $this->getMetadata(
                $resourceClass,
                $property
            );

            if (!$type) {
                if (in_array($property, self::PROPERTIES)) {
                    $searches[] = $this->getQuery($property, $values, null);
                    continue;
                }

                $propPath = explode('.', $property);
                array_pop($propPath);
                if (count($propPath) > 0) {
                    $searches[] = $this->getQuery($property, $values, join('.', $propPath));
                }
                continue;
            }

            if ($hasAssociation || $this->isIdentifier($nestedResourceClass, $nestedProperty)) {
                $values = array_map([$this, 'getIdentifierValue'],
                    $values,
                    array_fill(0, \count($values), $nestedProperty));
            }

            if (!$this->hasValidValues($values, $type)) {
                continue;
            }

            $property = null === $this->nameConverter ? $property : $this->nameConverter->normalize(
                $property,
                $resourceClass,
                null,
                $context
            );
            $nestedPath = $this->getNestedFieldPath($resourceClass, $property);
            $nestedPath = null === $nestedPath || null === $this->nameConverter ? $nestedPath : $this->nameConverter->normalize(
                $nestedPath,
                $resourceClass,
                null,
                $context
            );

            $searches[] = $this->getQuery($property, $values, $nestedPath);
        }

        if (!$searches) {
            return $clauseBody;
        }

        return array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $matches = [];

        foreach ($values as $value) {
            $matches[] = ['match' => [$property => $value]];
        }

        $matchQuery = isset($matches[1]) ? ['bool' => ['should' => $matches]] : $matches[0];

        if ('title' === $property) {
            foreach (['tenderId', 'tenderID', 'description', 'title', 'items.description'] as $condition) {
                $conditionPull[] = ['match_phrase' => [$condition => $values[0]]];
            }

            return ['bool' => ['should' => $conditionPull]];
        }

        if (null !== $nestedPath) {
            $matchQuery = ['nested' => ['path' => $nestedPath, 'query' => $matchQuery]];
        }

        return $matchQuery;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        foreach ($this->getProperties($resourceClass) as $property) {
            [$type, $hasAssociation] = $this->getMetadata($resourceClass, $property);

            $phpType = $type ? $this->getPhpType($type) : 'array';

            foreach ([$property, "{$property}[]"] as $filterParameterName) {
                $description[$filterParameterName] = [
                    'property' => $property,
                    'type' => $hasAssociation ? 'string' : $phpType,
                    'required' => false,
                ];
            }
        }

        return $description;
    }
}
