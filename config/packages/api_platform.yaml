parameters:
    env(ELASTICSEARCH_URL): 'http://elasticsearch:9200' #@todo remove

api_platform:
    title: DZO search engine
    version: 1.0.0
    defaults:
        pagination_client_items_per_page: true
        pagination_client_enabled: true
        enabled_parameter_name: page
        pagination_items_per_page: 10
    collection:
        pagination:
            items_per_page_parameter_name: perPage # Default value
    mapping:
        paths: ['%kernel.project_dir%/src/Resource']
    swagger:
        api_keys:
            JWT:
                name: Authorization
                type: header
    elasticsearch:
        hosts: [ '%env(ELASTICSEARCH_URL)%' ]
        mapping:
            App\Resource\ClassificationView:
                index: dzo_classification
                type: classification
            App\Resource\TenderView:
                index: dzo_tender
                type: tender
            App\Resource\ContractView:
                index: dzo_contract
                type: contract
            App\Resource\PlanView:
                index: dzo_plan
                type: plan
            App\Resource\BudgetaryView:
                index: dzo_budgetary
                type: budgetary
            App\Resource\ContractIdentifierView:
                index: dzo_contract_identifier
                type: identifier
            App\Resource\ContractSupplierView:
                index: dzo_contract_supplier
                type: identifier
            App\Resource\PlanIdentifierView:
                index: dzo_plan_identifier
                type: identifier
            App\Resource\TenderIdentifierView:
                index: dzo_tender_identifier
                type: identifier
            App\Resource\MonitoringView:
                index: dzo_monitoring
                type: monitoring
            App\Resource\InspectionView:
                index: dzo_inspection
                type: inspection
            App\Resource\CategoryView:
                index: dzo_category
                type: category
            App\Resource\MarketClassificationView:
                index: dzo_market_classification
                type: market_classification
