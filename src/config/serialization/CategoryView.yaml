App\Resource\CategoryView:
  attributes:
    id:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list', 'product:read', 'profile:read', 'product:list' ]
    prozorroId:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list', 'profile:read' ]
    procuringEntity:
      groups: [ 'category:read', 'category:cdb_parse' ]
    status:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list', 'profile:read', 'product:read', 'product:list' ]
    classification:
      groups: [ 'category:read', 'category:cdb_parse' ]
    title:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list', 'product:read', 'profile:read', 'product:list' ]
    description:
      groups: [ 'category:read', 'category:cdb_parse' ]
    dateModified:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list' ]
    dateCreated:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list' ]
    images:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list' ]
    criteria:
      groups: [ 'category:read', 'category:cdb_parse' ]
    products:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list' ]
    agreement:
      groups: [ 'category:read', 'product:read' ]
    profiles:
      groups: [ 'category:read', 'category:cdb_parse', 'category:list' ]
    additionalClassifications:
      groups: [ 'product:read', 'product:cdb_parse' ]
