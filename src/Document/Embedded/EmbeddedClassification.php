<?php

namespace App\Document\Embedded;

use App\Document\BaseClassification;
use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class EmbeddedClassification extends BaseClassification
{
    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
