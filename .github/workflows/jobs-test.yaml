name: Test jobs pipeline

on:
  push:
    branches:
      - 'master'

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.TEST_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.TEST_AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.12.0

      - name: kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-test --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Login to Amazon ECR
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          aws ecr get-login-password | helm registry login --username AWS --password-stdin 206424851631.dkr.ecr.eu-central-1.amazonaws.com

      - name: Delete previous search jobs
        run: |
          kubectl delete jobs -l app.kubernetes.io/instance=dzo-jobs --namespace test

      - name: Deploy jobs
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          helm upgrade --install dzo-jobs oci://206424851631.dkr.ecr.eu-central-1.amazonaws.com/helm-charts --version 2.0.0-jobs --namespace test \
          --set defaultImage.tag=${{ github.sha }} \
          -f deploy/jobs-test.yaml
