<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Project;

/**
 * Class ProjectView.
 */
class ProjectView
{
    private ?string $id = null;

    private ?string $title = null;

    private ?string $uri = null;

    /**
     * ProjectView constructor.
     */
    public function __construct(Project $project)
    {
        $this->id = $project->getId();
        $this->title = $project->getTitle();
        $this->uri = $project->getUri();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getUri(): ?string
    {
        return $this->uri;
    }
}
