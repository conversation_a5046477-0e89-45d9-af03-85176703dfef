<?php

namespace App\Extension;

use ApiPlatform\Elasticsearch\Extension\RequestBodySearchCollectionExtensionInterface;
use ApiPlatform\Metadata\Operation;
use App\Resource\ContractView;
use App\Resource\PlanView;
use App\Resource\TenderView;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class ElasticExcludeExtension.
 */
class ElasticExcludeExtension implements RequestBodySearchCollectionExtensionInterface
{
    private const VIEWS = [
        TenderView::class,
        ContractView::class,
        PlanView::class,
    ];

    public function applyToCollection(
        array $requestBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = [],
    ): array {
        if (!in_array($resourceClass, self::VIEWS, true)) {
            return $requestBody;
        }

        $mustNot = [
            ['term' => ['isMasked' => true]],
        ];


        if (!array_key_exists('filters', $context) || !array_key_exists('testMode', $context['filters'])) {
            $mustNot[] = ['term' => ['mode' => 'test']];
        }

        if (TenderView::class === $resourceClass) {
            $mustNot[] = [
                'terms' => [
                    'status' => [
                        'draft',
                        'draft.stage2',
                        'draft.pending',
                        'draft.unsuccessful',
                        'draft.publishing',
                    ],
                ],
            ];

            $this->excludeMedicalTendersForRoot($context['request'], function ($query) use (&$mustNot) {
                $mustNot[] = $query;
            });
        }

        $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
            'exists' => [
                'field' => "oldId"
            ]
        ];

        $requestBody['query']['constant_score']['filter']['bool']['must'][] = [
            'bool' => [
                'must_not' => $mustNot,
            ],
        ];

        //    die(json_encode($requestBody));

        return $requestBody;
    }

    private function excludeMedicalTendersForRoot(Request $request, callable $callback): void
    {
        if ($request->server->has('HTTP_REFERER') && '/' === parse_url($request->server->get('HTTP_REFERER'), PHP_URL_PATH)) {
            $callback([
                'terms' => [
                    'items.classification.id' => [
                        '33110000-4',
                        '33120000-7',
                        '33130000-0',
                        '33140000-3',
                        '33150000-6',
                        '33160000-9',
                        '33170000-2',
                        '33180000-5',
                        '33190000-8',
                        '33600000-6',
                        '33610000-9',
                        '33620000-2',
                        '33630000-5',
                        '33640000-8',
                        '33650000-1',
                        '33660000-4',
                        '33670000-7',
                        '33680000-0',
                        '33690000-3',
                        '33710000-0',
                        '33720000-3',
                        '33730000-6',
                        '33740000-9',
                        '33750000-2',
                        '33760000-5',
                        '33770000-8',
                        '33790000-4',
                        '33910000-2',
                        '33920000-5',
                        '33930000-8',
                        '33940000-1',
                        '33950000-4',
                        '33960000-7',
                        '33970000-0',
                    ],
                ],
            ]);
        }
    }
}
