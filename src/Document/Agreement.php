<?php

namespace App\Document;

use App\Document\Embedded\Document;
use App\Document\Embedded\Period;
use App\Document\Embedded\ProcuringEntity;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document]
class Agreement
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $prozorroId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $agreementID = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $status = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $owner = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $agreementNumber = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    protected ?Period $period = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    private ?\DateTimeInterface $dateSigned = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    private ?\DateTimeInterface $dateModified = null;

    /**
     * @var Collection|Document[]
     */
    #[MongoDB\EmbedMany(targetDocument: Document::class)]
    protected Collection|array $documents;

    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    protected $contracts;

    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    protected $items;

    #[MongoDB\EmbedOne(targetDocument: ProcuringEntity::class)]
    protected ?ProcuringEntity $procuringEntity = null;

    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    protected $agreementConfig;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getProzorroId(): ?string
    {
        return $this->prozorroId;
    }

    public function setProzorroId(?string $prozorroId): Agreement
    {
        $this->prozorroId = $prozorroId;
        return $this;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): Agreement
    {
        $this->owner = $owner;
        return $this;
    }

    public function getAgreementNumber(): ?string
    {
        return $this->agreementNumber;
    }

    public function setAgreementNumber(?string $agreementNumber): Agreement
    {
        $this->agreementNumber = $agreementNumber;
        return $this;
    }

    public function getPeriod(): ?Period
    {
        return $this->period;
    }

    public function setPeriod(?Period $period): Agreement
    {
        $this->period = $period;
        return $this;
    }

    public function getDateSigned(): ?\DateTimeInterface
    {
        return $this->dateSigned;
    }

    public function setDateSigned(?\DateTimeInterface $dateSigned): Agreement
    {
        $this->dateSigned = $dateSigned;
        return $this;
    }

    public function getDocuments(): Collection|array
    {
        return $this->documents;
    }

    public function setDocuments(Collection|array $documents): Agreement
    {
        $this->documents = $documents;
        return $this;
    }

    public function getContracts()
    {
        return $this->contracts;
    }

    public function setContracts($contracts)
    {
        $this->contracts = $contracts;
        return $this;
    }

    public function getItems()
    {
        return $this->items;
    }

    public function setItems($items)
    {
        $this->items = $items;
        return $this;
    }

    public function getProcuringEntity(): ?ProcuringEntity
    {
        return $this->procuringEntity;
    }

    public function setProcuringEntity(?ProcuringEntity $procuringEntity): Agreement
    {
        $this->procuringEntity = $procuringEntity;
        return $this;
    }

    public function getAgreementConfig()
    {
        return $this->agreementConfig;
    }

    public function setAgreementConfig($agreementConfig)
    {
        $this->agreementConfig = $agreementConfig;
        return $this;
    }

    public function getAgreementID(): ?string
    {
        return $this->agreementID;
    }

    public function setAgreementID(?string $agreementID): Agreement
    {
        $this->agreementID = $agreementID;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): Agreement
    {
        $this->status = $status;
        return $this;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    public function setDateModified(?\DateTimeInterface $dateModified): Agreement
    {
        $this->dateModified = $dateModified;
        return $this;
    }
}