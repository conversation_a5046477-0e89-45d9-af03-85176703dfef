<?php

namespace App\Filter\Odm;

use ApiPlatform\Doctrine\Odm\Filter\AbstractFilter;
use ApiPlatform\Metadata\Operation;
use Doctrine\ODM\MongoDB\Aggregation\Builder;

class RequiredExactMatchFilter extends AbstractFilter
{
    protected function filterProperty(string $property, $value, Builder $aggregationBuilder, string $resourceClass, ?Operation $operation = null, array &$context = []): void
    {
        if (!array_key_exists($property, $this->properties)) {
            return;
        }

        $aggregationBuilder
            ->match()
            ->field($property)
            ->equals($value);
    }

    public function getDescription(string $resourceClass): array
    {
        if (!$this->properties) {
            return [];
        }

        $description = [];
        foreach ($this->properties as $property => $strategy) {
            $description[(string) $property] = [
                'property' => $property,
                'type' => 'string',
                'required' => true,
            ];
        }

        return $description;
    }
}
