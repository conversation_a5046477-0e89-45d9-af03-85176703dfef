<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Manager\CartItemManager;
use App\Manager\CartManager;
use App\Resource\CartView;
use App\Security\JwtCheckerTrait;
use App\Service\CartItemService;

readonly class CartItemQuantityProcessor implements ProcessorInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private CartManager $cartManager,
        private CartItemManager $cartItemManager,
        private CartItemService $cartItemService
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        if (!$data) {
            return null;
        }

        if (!$userId = $this->checkJwt()) {
            return null;
        }

        if ($cart = $this->cartManager->getUserCart($userId)) {
            if ($cartItem = $this->cartItemManager->find($data->cartItemId)) {
                $cartItem->setQuantity($data->quantity);
                $this->cartItemManager->save($cartItem);
            }
        }

        return new CartView($cart, $this->cartItemService->getCartItems($cart));
    }
}
