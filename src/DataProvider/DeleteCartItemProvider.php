<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Repository\CartItemRepository;
use App\Resource\CartView;
use App\Security\JwtCheckerTrait;
use Symfony\Component\HttpFoundation\JsonResponse;

readonly class DeleteCartItemProvider implements ProviderInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private CartItemRepository $cartItemRepository,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $cartItem = $this->cartItemRepository->find($uriVariables['id']);

        if (!$userId = $this->checkJwt()) {
            return new JsonResponse('JWT Token not found', 401);
        }

        if (!$cartItem || $cartItem->getCart()->getUserId() !== $userId) {
            return new JsonResponse('Not found', 404);
        }

        $cart = $cartItem->getCart();
        $this->cartItemRepository->remove($cartItem);

        return new CartView($cart);
    }
}
