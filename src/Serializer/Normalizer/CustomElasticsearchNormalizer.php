<?php

namespace App\Serializer\Normalizer;

use ApiPlatform\Elasticsearch\Serializer\ItemNormalizer;
use App\Document\Budgetary;
use App\Document\Classification;
use App\Document\ContractIdentifier;
use App\Document\ContractSupplier;
use App\Document\PlanIdentifier;
use App\Document\TenderIdentifier;
use App\Manager\ContractManager;
use App\Manager\InspectionManager;
use App\Manager\MonitoringManager;
use App\Manager\ProductManager;
use App\Manager\PlanManager;
use App\Manager\ProfileManager;
use App\Manager\CategoryManager;
use App\Manager\TenderManager;
use App\Resource\BudgetaryView;
use App\Resource\ClassificationView;
use App\Resource\ContractIdentifierView;
use App\Resource\ContractSupplierView;
use App\Resource\ContractView;
use App\Resource\InspectionView;
use App\Resource\MonitoringView;
use App\Resource\PlanIdentifierView;
use App\Resource\PlanView;
use App\Resource\TenderIdentifierView;
use App\Resource\TenderView;
use App\Resource\ProductView;
use App\Service\CriteriaFilterService;
use App\Utils\CurrentUserService;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareInterface;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class CustomElasticsearchNormalizer.
 */
readonly class CustomElasticsearchNormalizer implements NormalizerInterface, DenormalizerInterface, SerializerAwareInterface
{
    /**
     * CustomElasticsearchNormalizer constructor.
     */
    public function __construct(
        private DocumentManager $documentManager,
        private NormalizerInterface $decorated,
        private TenderManager $tenderManager,
        private PlanManager $planManager,
        private ContractManager $contractManager,
        private MonitoringManager $monitoringManager,
        private InspectionManager $inspectionManager,
        private CurrentUserService $currentUserService,
        private ProductManager $productManager,
        private ProfileManager $profileManager,
        private CategoryManager $categoryManager,
        private CriteriaFilterService $criteriaFilterService,
        private string $awsCdnUrl,
    ) {
        if (!$decorated instanceof DenormalizerInterface) {
            throw new \InvalidArgumentException(sprintf('The decorated normalizer must implement the %s.', DenormalizerInterface::class));
        }
    }

    /**
     * @param array $context *
     */
    public function supportsNormalization($data, ?string $format = null, array $context = []): bool
    {
        return $this->decorated->supportsNormalization($data, $format);
    }

    /**
     * @param null $format
     *
     * @throws ExceptionInterface
     */
    public function normalize($object, ?string $format = null, array $context = []): float|int|bool|\ArrayObject|array|string|null
    {
        $data = $this->decorated->normalize($object, $format, $context);
        if (is_array($data)) {
            $data['date'] = date(\DateTime::RFC3339);
        }

        return $data;
    }

    /**
     * @param string $type
     * @param array  $context *
     */
    public function supportsDenormalization($data, $type, ?string $format = null, array $context = []): bool
    {
        return ItemNormalizer::FORMAT === $format;
    }

    /**
     * @param string $class
     *
     * @return mixed|null
     *
     * @throws ExceptionInterface
     * @throws LockException
     * @throws MappingException
     *
     * @todo try|catch, logs
     */
    public function denormalize($data, $class, ?string $format = null, array $context = []): mixed
    {
        switch ($class) {
            case ProductView::class:
                $document = $this->productManager->find($data['_source']['id']);
                $category = null;
                $profiles = [];
                if (!empty($document->getRelatedProfileIds())) {
                    $profileIds = array_slice($document->getRelatedProfileIds(), 0, 5);
                    $profiles = $this->profileManager->findByIds($profileIds);
                }

                if ($document->getRelatedCategoryId()) {
                    $category = $this->categoryManager->find($document->getRelatedCategoryId());
                }

                return new ProductView(
                    $document, 
                    $profiles, 
                    $this->criteriaFilterService, 
                    $this->awsCdnUrl,
                    $category,
                );
            case ContractView::class:
                $document = $this->contractManager->find($data['_source']['id']);

                return ($document) ? new ContractView($document) : null;
            case PlanView::class:
                $document = $this->planManager->find($data['_source']['id']);

                return ($document) ? new PlanView($document) : null;
            case TenderView::class:
                $document = $this->tenderManager->find($data['_source']['id']);

                return ($document) ? new TenderView($document, $this->currentUserService->userIdAsString()) : null;
            case MonitoringView::class:
                $document = $this->monitoringManager->find($data['_source']['id']);

                return ($document) ? new MonitoringView($document) : null;
            case InspectionView::class:
                $document = $this->inspectionManager->find($data['_source']['id']);

                return ($document) ? new InspectionView($document) : null;
            case ClassificationView::class:
                $document = $this->documentManager->getRepository(Classification::class)->find($data['_source']['id']);

                return ($document) ? new ClassificationView($document) : null;
            case TenderIdentifierView::class:
                $document = $this->documentManager->getRepository(TenderIdentifier::class)->find($data['_source']['id']);

                return ($document) ? new TenderIdentifierView($document) : null;
            case PlanIdentifierView::class:
                $document = $this->documentManager->getRepository(PlanIdentifier::class)->find($data['_source']['id']);

                return ($document) ? new PlanIdentifierView($document) : null;
            case ContractIdentifierView::class:
                $document = $this->documentManager->getRepository(ContractIdentifier::class)->find($data['_source']['id']);

                return ($document) ? new ContractIdentifierView($document) : null;
            case ContractSupplierView::class:
                $document = $this->documentManager->getRepository(ContractSupplier::class)->find($data['_source']['id']);

                return ($document) ? new ContractSupplierView($document) : null;
            case BudgetaryView::class:
                $document = $this->documentManager->getRepository(Budgetary::class)->find($data['_source']['id']);

                return ($document) ? new BudgetaryView($document) : null;
            default:
                return $this->decorated->denormalize($data['_source'], $class, $format, $context);
        }
    }

    public function setSerializer(SerializerInterface $serializer): void
    {
        if ($this->decorated instanceof SerializerAwareInterface) {
            $this->decorated->setSerializer($serializer);
        }
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            'object' => true,
        ];
    }
}
