App\Document\Tender:
  attributes:
    id:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    procurementMethod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    mode:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list'  ]
    numberOfBids:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    awardPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    mainProcurementCategory:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    auctionUrl:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    enquiryPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    submissionMethod:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    procuringEntity:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    owner:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    documents:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    title:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    titleEn:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    description:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    descriptionEn:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    plans:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    tenderID:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    procurementMethodRationale:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    guarantee:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    dateModified:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    dateCreated:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    noticePublicationDate:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    status:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    tenderPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    contracts:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    auctionPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    procurementMethodType:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    awards:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    date:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    milestones:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    minimalStep:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    items:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    bids:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    value:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    awardCriteria:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    features:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    questions:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    complaints:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    agreements:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    qualificationPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    complaintPeriod:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    lots:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    agreementDuration:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    maxAwardsCount:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    qualifications:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    cancellations:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    funders:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    buyers:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    revisions:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    cause:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    causeDescription:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    stage2TenderId:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    criteria:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    hasMonitoring:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    monitoringDateModified:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    is_masked:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
    hasActiveMonitoring:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    inspector:
      groups: [ 'tender:read', 'tender:cdb_parse' ]
    dzoData:
      groups: [ 'tender:read', 'tender:cdb_parse', 'tender:list' ]
