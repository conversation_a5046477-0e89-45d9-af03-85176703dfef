<?php

namespace App\Command;

use App\Message\MarketUpdateVisibilityMessage;
use App\Document\MarketUpdateVisibility;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:market:update-visibility-flags',
    description: 'Sync category status and data types consistency between categories and related documents (Products and Profiles)',
)]
class MarketUpdateVisibilityCommand extends Command
{
    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
    ) {
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addOption('type', 't', InputOption::VALUE_REQUIRED, 'Type of document: Products or Profiles')
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limit of documents to process. -1 for unlimited.', -1);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $executionType = $input->getOption('type');
        $limit = $input->getOption('limit');

        if (!$executionType) {
            $output->writeln("<error>Document type is required.</error>");
            return Command::FAILURE;
        } elseif (
            $executionType !== 'product' &&
            $executionType !== 'profile' &&
            $executionType !== 'productRelatedCategoryNull' &&
            $executionType !== 'profileRelatedCategoryNull'
        ) {
            $output->writeln("<error>Wrong document type: '$executionType'. Only --type='product' and --type='profile' are supported.</error>");
            return Command::FAILURE;
        }

        try {

            $realDocumentType = $executionType;
            if ($executionType === 'productRelatedCategoryNull') {
                $realDocumentType = 'product';
            } elseif ($executionType === 'profileRelatedCategoryNull') {
                $realDocumentType = 'profile';
            }

            $documentClass = DocumentType::getDocumentClass($realDocumentType);
            $pageSize = 10000;
            if (-1 !== $limit && $limit < $pageSize) {
                $pageSize = $limit;
            }
            
            $processedCount = 0;
            
            while (true) {
                $pageDocument = $this->documentManager->getRepository(MarketUpdateVisibility::class)->findOneBy(['type' => $realDocumentType]);

                $queryBuilder = $this->documentManager->createQueryBuilder($documentClass)
                    ->select('id', 'dateCreated')
                    ->sort('dateCreated', 'ASC')
                    ->limit($pageSize);
                if ($executionType === 'productRelatedCategoryNull' || $executionType === 'profileRelatedCategoryNull') {
                    $queryBuilder->field('relatedCategoryStatus')->equals(null);
                    if($pageDocument) {
                        $this->documentManager->remove($pageDocument);
                        $this->documentManager->flush();
                        $pageDocument = null;
                    }
                }

                if ($pageDocument) {
                    $output->writeln("Processing $realDocumentType documents from ".$pageDocument->getDateCreated()->format('Y-m-d H:i:s'));
                    $queryBuilder->addAnd(
                        $queryBuilder->expr()->field('dateCreated')->gt($pageDocument->getDateCreated())
                    );
                } else {
                    $output->writeln("Processing $realDocumentType documents from the beginning");
                }

                $result = $queryBuilder->getQuery()->execute();

                if (0 === count($result)) {
                    break;
                }

                $newDate = null;
                
                foreach ($result as $document) {
                    $newDate = $document->getDateCreated();
                    $this->messageBus->dispatch(new MarketUpdateVisibilityMessage($document->getId(), $realDocumentType));
                    $processedCount++;
                }

                $this->documentManager->clear();

                if (!$pageDocument) {
                    $pageDocument = new MarketUpdateVisibility();
                    $pageDocument->setType($realDocumentType);
                }

                $pageDocument->setDateCreated($newDate);
                $this->documentManager->persist($pageDocument);
                $this->documentManager->flush();

                if (-1 !== $limit) {
                    $limit -= $pageSize;
                    if ($limit <= 0) {
                        break;
                    }
                } else {
                    if (count($result) < $pageSize) {
                        break;
                    }
                }
            }

            $output->writeln("Total processed: $processedCount $realDocumentType documents");
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $output->writeln("<error>Error: " . $e->getMessage() . "</error>");
            return Command::FAILURE;
        }
    }
}
