<?php

namespace App\Resource\Embedded;

use App\Document\CartItem;

class CartItemView
{
    public string $id;

    public int $quantity;

    public ?string $profileId = null;

    public ?string $profileTitle = null;

    public ?string $categoryId = null;

    public ?string $classificationId = null;

    public ?array $config = null;

    public function __construct(CartItem $cartItem, ?string $profileTitle, ?string $classificationId)
    {
        $this->id = $cartItem->getId();
        $this->profileId = $cartItem->getProfileId();
        $this->categoryId = $cartItem->getCategoryId();
        $this->quantity = $cartItem->getQuantity();
        $this->config = $cartItem->getConfig();
        $this->classificationId = $classificationId;
        $this->profileTitle = $profileTitle;
    }
}
