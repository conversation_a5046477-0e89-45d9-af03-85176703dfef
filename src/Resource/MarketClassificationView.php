<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\AllMarketClassificationDataProvider;
use App\DataProvider\MarketClassificationDataProvider;
use App\Filter\KeywordFilter;
use App\Filter\OrderFilter;
use Doctrine\Common\Collections\Criteria;

#[ApiResource(
    shortName: 'market_classification',
    operations: [
        new GetCollection(
            uriTemplate: '/market_classification/all',
            paginationEnabled: false,
            provider: AllMarketClassificationDataProvider::class,
            stateOptions: new Options(index: 'dzo_market_classification', type: 'collection')
        ),
        new GetCollection(
            provider: CollectionProvider::class, stateOptions: new Options(
                index: 'dzo_market_classification', type: 'collection'
            )
        ),
        new Get(
            provider: MarketClassificationDataProvider::class, stateOptions: new Options(
                index: 'dzo_market_classification'
            )
        ),
    ],
    order: ['id' => Criteria::ASC],
    paginationItemsPerPage: 10,
    paginationType: 'page'
)]
#[ApiFilter(OrderFilter::class, properties: ['id', 'scheme', 'description', 'popularity'], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(KeywordFilter::class, properties: [
    'scheme', 'id', 'description.description',
])]
readonly class MarketClassificationView
{
    public ?string $id;

    public ?string $description;

    public ?string $scheme;
    /** @var array EmbeddedCategoryView[] */
    public array $categories;

    public string $image;

    /**
     * BaseClassificationView constructor.
     */
    public function __construct(mixed $marketClassification, array $categoryViews = [], string $awsCdnUrl = null)
    {
        $this->id = $marketClassification->getId();
        $this->description = $marketClassification->getDescription();
        $this->scheme = $marketClassification->getScheme();
        $this->categories = $categoryViews;
        $this->image = $awsCdnUrl . '/classifications/' . $marketClassification->getId() . '.jpeg';
    }
}
