<?php

namespace App\Classification;

use App\Document\Classification;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class UaRoadsParser.
 */
class RoadsParser extends ClassificationParser
{
    protected string $scheme = 'road';

    /**
     * @throws GuzzleException
     * @throws \Doctrine\ODM\MongoDB\LockException
     * @throws \Doctrine\ODM\MongoDB\Mapping\MappingException
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    public function parse(array $urls): void
    {
        $url = $urls[0];

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Starting with '.$this->scheme);
        }
        try {
            $response = $this->client->request('GET', $url);
        } catch (GuzzleException $exception) {
            $this->logger->error($exception->getMessage(), [$exception]);
            throw $exception;
        }

        $result = json_decode((string) $response->getBody(), true);
        $totalDocuments = count($result);

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment($totalDocuments.' items found...');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsCount = 0;
        $parentClassifications = [];

        foreach ($result as $id => $description) {
            $roadIndexArray = explode('-', $id);
            $parentDescription = $roadIndexArray[0];
            $parentId = $this->scheme.$parentDescription;
            $level = 1;
            $this->buildParentClassification($parentId, $parentDescription, '', $parentClassifications);

            // For T road we need to add 2nd level
            if ('T' == $parentDescription) {
                $level = 2;
                $tRoadParentId = $parentId;
                $parentDescription = implode('-', [$roadIndexArray[0], $roadIndexArray[1]]);
                $parentId = $this->scheme.$parentDescription;
                $this->buildParentClassification($parentId, $parentDescription, $tRoadParentId, $parentClassifications);
            }

            $document = new Classification();
            $document->setScheme($this->scheme);
            $document->setId($id);
            $document->setDescription($description);
            $document->setParentId($parentId);
            $document->setSelectable(true);
            $document->setTreeLevel($level);

            ++$documentsCount;

            $needFlush = (0 === $documentsCount % 50) || ($documentsCount === $totalDocuments);
            $this->manager->save($document, $needFlush);
            if (null !== $this->inputOutput) {
                $this->inputOutput->progressAdvance(1);
            }
        }
    }
}
