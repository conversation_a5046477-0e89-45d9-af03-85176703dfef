<?php

namespace App\Document;

use App\Helper\RestrictedDataHelper;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[MongoDB\Document]
class Inspection implements HasDateModifiedInterface
{
    use RestrictedDataHelper;

    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    #[SerializedName('inspection_id')]
    protected ?string $inspectionId = null;

    #[MongoDB\Field(type: Type::RAW)]
    #[SerializedName('monitoring_ids')]
    protected array $monitoringIds = [];

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $documents;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::DESC)]
    protected $dateModified;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected $dateCreated;

    #[MongoDB\Field(type: Type::RAW)]
    private $restricted;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
    }

    /**
     * @return string|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getInspectionId(): ?string
    {
        return $this->inspectionId;
    }

    public function setInspectionId(?string $inspectionId): void
    {
        $this->inspectionId = $inspectionId;
    }

    /**
     * @return string[]
     */
    public function getMonitoringIds(): array
    {
        return $this->monitoringIds;
    }

    /**
     * @param string[] $monitoringIds
     */
    public function setMonitoringIds(array $monitoringIds): void
    {
        $this->monitoringIds = $monitoringIds;
    }

    public function getDescription(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getDocuments()
    {
        if ($this->restrictedMode && is_array($this->documents)) {
            $restrictedDocuments = $this->documents;
            foreach ($restrictedDocuments as $idx => &$document) {
                $restrictedDocuments[$idx] = $this->restrictDocumentData($document);
            }

            return $restrictedDocuments;
        }

        return $this->documents;
    }

    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    /**
     * @throws \Exception
     */
    public function setDateModified($dateModified): void
    {
        if (is_string($dateModified)) {
            $dateModified = new \DateTime($dateModified);
        }
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws \Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new \DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getRestricted()
    {
        return $this->restricted;
    }

    public function setRestricted($restricted): void
    {
        $this->restricted = $restricted;
    }
}
