<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Repository\FilterRepository;
use App\Security\JwtCheckerTrait;
use Symfony\Component\HttpFoundation\JsonResponse;

readonly class DeleteFilterProvider implements ProviderInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private FilterRepository $filterRepository
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $filter = $this->filterRepository->find($uriVariables['id']);

        if (!$userId = $this->checkJwt()) {
            return new JsonResponse('Not found', 404);
        }

        if (!$filter || $filter->getUserId() !== $userId) {
            return new JsonResponse('Not found', 404);
        }

        $this->filterRepository->remove($filter);

        return new JsonResponse('Remove success', 200);
    }
}
