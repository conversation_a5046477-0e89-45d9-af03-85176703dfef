<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document]
#[MongoDB\InheritanceType('COLLECTION_PER_CLASS')]
class Budgetary
{
    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $code = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    protected ?int $type = 1;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $titleEn = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getType(): ?int
    {
        return $this->type;
    }

    public function setType(?int $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function setTitleEn(?string $titleEn): self
    {
        $this->titleEn = $titleEn;

        return $this;
    }
}
