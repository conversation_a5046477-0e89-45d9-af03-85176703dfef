<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Address;

/**
 * Class AddressView.
 */
class AddressView
{
    private ?string $id = null;

    private ?string $streetAddress = null;

    private ?string $locality = null;

    private ?string $region = null;

    private ?string $postalCode = null;

    private ?string $countryName = null;

    /**
     * AddressView constructor.
     */
    public function __construct(Address $address)
    {
        $this->id = $address->getId();
        $this->streetAddress = $address->getStreetAddress();
        $this->locality = $address->getLocality();
        $this->region = $address->getRegion();
        $this->postalCode = $address->getPostalCode();
        $this->countryName = $address->getCountryName();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getStreetAddress(): ?string
    {
        return $this->streetAddress;
    }

    public function getLocality(): ?string
    {
        return $this->locality;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function getCountryName(): ?string
    {
        return $this->countryName;
    }
}
