<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Unit;

/**
 * Class UnitView.
 */
class UnitView
{
    private ?string $id = null;

    private ?string $description = null;

    private ?string $code = null;

    private ?string $name = null;

    private ?string $symbol = null;

    /**
     * UnitView constructor.
     */
    public function __construct(Unit $unit)
    {
        $this->id = $unit->getId();
        $this->description = $unit->getDescription();
        $this->code = $unit->getCode();
        $this->name = $unit->getName();
        $this->symbol = $unit->getSymbol();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getSymbol(): ?string
    {
        return $this->symbol;
    }
}
