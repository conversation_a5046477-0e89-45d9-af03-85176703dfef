<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use ApiPlatform\Symfony\Security\Exception\AccessDeniedException;
use App\Manager\TenderManager;
use App\Resource\TenderView;
use App\Security\JwtCheckerTrait;

final readonly class TenderSendedBidsDataProvider implements ProviderInterface
{
    use JwtCheckerTrait;
    public function __construct(
        private TenderManager $tenderManager,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        if (!$userId = $this->checkJwt()) {
            throw  new AccessDeniedException('Access denied');
        }
        $documents = $this->tenderManager->findSendedBids($userId);
        $documents->setMaxPerPage($context['filters']['limit'] ?? 10);
        $documents->setCurrentPage($context['filters']['page'] ?? 1);

        if (empty($documents)) {
            return null;
        }

        $views = [];
        foreach ($documents as $tender) {
            $views[] = new TenderView($tender, $userId);
        }

        return  $views;
    }
}
