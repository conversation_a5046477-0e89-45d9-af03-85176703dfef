<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\BudgetBreakdown;

/**
 * Class BudgetBreakdownView.
 */
class BudgetBreakdownView
{
    private ?string $id = null;

    private ?string $title = null;

    private ?string $description = null;

    private ?ValueView $value = null;
    
    private $address = null;
    
    private ?BudgetBreakdownClassificationView $classification = null;

    /**
     * BudgetBreakdownView constructor.
     */
    public function __construct(BudgetBreakdown $budgetBreakdown)
    {
        $this->id = $budgetBreakdown->getId();
        $this->title = $budgetBreakdown->getTitle();
        $this->description = $budgetBreakdown->getDescription();

        $value = $budgetBreakdown->getValue();
        if ($value) {
            $this->value = new ValueView($value);
        }
        
        $this->address = $budgetBreakdown->getAddress();
        
        $classification = $budgetBreakdown->getClassification();
        if ($classification) {
            $this->classification = new BudgetBreakdownClassificationView($classification);
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getValue(): ?ValueView
    {
        return $this->value;
    }
    
    public function getAddress()
    {
        return $this->address;
    }
    
    public function getClassification(): ?BudgetBreakdownClassificationView
    {
        return $this->classification;
    }
}
