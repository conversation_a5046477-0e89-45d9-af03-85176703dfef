<?php

namespace App\Legacy\Repository;

use App\Legacy\Helper\DatabaseHelper;

class LegacyContractRepository extends LegacyRepository implements LegacyOldIdSetterInterface
{
    public function getForOldIdSetter(int $start, int $limit, bool $isArchive = false): false|array
    {
        $query = 'SELECT c.id, c.contract_id FROM ' . DatabaseHelper::getTableName('contract') . ' AS c ' .
            'WHERE c.id < :start AND c.contract_id IS NOT NULL AND c.contract_id != "" ORDER BY c.id DESC LIMIT ' . $limit;

        if (!$isArchive) {
            $query = $this->connection->prepare($query);
        } else {
            $query = $this->archiveConnection->prepare($query);
        }

        $query->bindValue('start', $start);


        return $query->executeQuery()->fetchAllAssociative();
    }

}