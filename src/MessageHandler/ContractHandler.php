<?php

namespace App\MessageHandler;

use App\Document\Contract;
use App\Document\ContractIdentifier;
use App\Document\ContractSupplier;
use App\Message\ContractMessage;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;

#[AsMessageHandler]
class ContractHandler extends BaseHandler
{
    /**
     * @throws MongoDBException
     * @throws GuzzleException
     * @throws \Throwable
     */
    public function __invoke(ContractMessage $contract): void
    {
        try {
            try {
                $response = $this->requestPublicApi('contracts/'.$contract->getId());
            } catch (GuzzleException $e) {
                echo $e->getMessage();

                return;
            }

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            if (array_key_exists('config', $result)) {
                $data['contractConfig'] = $result['config'];
            }
            if (array_key_exists('value', $data) && is_array($data['value']) && array_key_exists('amount', $data['value'])
                && !is_float($data['value']['amount'])) {
                $data['value']['amount'] = floatval($data['value']['amount']);
            }

            /** @var Contract $object */
            $object = $this->getDocumentManager()->find(Contract::class, $data['id']);

            $object = $this
              ->getSerializer()
              ->deserialize(
                  json_encode($data, JSON_THROW_ON_ERROR),
                  Contract::class,
                  'json',
                  [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
              );

            try {
                if ($object->getTenderId()) {
                    $this->processTender($object->getTenderId(), false);
                }
                $tenderData = $this->fetchTender($object->getTenderId());
                if (array_key_exists('procurementMethodRationale', $tenderData)) {
                    $object->setProcurementMethodRationale($tenderData['procurementMethodRationale']);
                }

                if (array_key_exists('procurementMethodType', $tenderData)) {
                    $object->setProcurementMethodType($tenderData['procurementMethodType']);
                }
            } catch (\Exception $e) {
                // do nothing here, just ignore if tender not exists
            }

            if (array_key_exists('suppliers', $data) && is_array($data['suppliers'])) {
                foreach ($data['suppliers'] as $idx => $supplierData) {
                    if (array_key_exists('identifier', $supplierData)) {
                        // ignore suppliers with to long id in identifiers
                        if (array_key_exists('id', $supplierData['identifier']) && strlen($supplierData['identifier']['id']) > 400) {
                            continue;
                        }
                        $this->handleIdentifier($supplierData['identifier'], ContractSupplier::class, false);
                    }
                }
            }

            if (array_key_exists('buyer', $data) && is_array($data['buyer'])) {
                $this->handleIdentifier($data['buyer']['identifier'], ContractIdentifier::class, false);
            }

            if (array_key_exists('procuringEntity', $data) && array_key_exists('identifier', $data['procuringEntity'])) {
                $this->handleIdentifier($data['procuringEntity']['identifier'], ContractIdentifier::class, false);
            }

            //        /** uncomment to debug with raw data */
            //        //@todo remove or comment for prod
            //        $object->setData($data);

            $this->save($object);
        } catch (\Throwable $throwable) {
            $this->getLogger()->critical('Unandled Error received: '.$throwable->getMessage(), [$this, $throwable]);
            throw $throwable;
        }
    }
}
