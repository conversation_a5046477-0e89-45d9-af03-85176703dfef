<?php

namespace App\Document\Embedded;

use App\Helper\RestrictedDataHelper;
use App\Uuid\Uuid;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class Item
{
    use RestrictedDataHelper;

    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $category = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $profile = null;

    #[MongoDB\Field(name: 'description_en', type: Type::STRING, nullable: true)]
    private ?string $descriptionEn = null;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedClassification::class)]
    private ?EmbeddedClassification $classification = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    private ?Period $deliveryDate = null;

    /**
     * @var Collection|EmbeddedClassification[]
     */
    #[MongoDB\EmbedMany(targetDocument: EmbeddedClassification::class)]
    private Collection|array $additionalClassifications;

    #[MongoDB\EmbedOne(targetDocument: Unit::class)]
    private $unit;

    #[MongoDB\Field(type: Type::FLOAT)]
    private ?float $quantity = null;

    #[MongoDB\EmbedOne(targetDocument: Address::class)]
    private ?Address $deliveryAddress = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $location;

    #[MongoDB\Field(type: Type::RAW)]
    private $deliveryLocation;

    #[MongoDB\Field(type: Type::RAW)]
    private $relatedLot;

    #[MongoDB\Field(type: Type::RAW)]
    private $award;

    #[MongoDB\Field(type: Type::RAW)]
    private $tender;

    private bool $restrictedMode = false;

    #[MongoDB\Field(type: Type::RAW)]
    private $attributes;

    /**
     * Item constructor.
     */
    public function __construct()
    {
        $this->additionalClassifications = new ArrayCollection();
    }

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
        if (null !== $this->deliveryAddress) {
            $this->deliveryAddress->setRestrictedMode($restrictedMode);
        }
        if (null !== $this->deliveryDate) {
            $this->deliveryDate->setRestrictedMode($restrictedMode);
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getClassification()
    {
        return $this->classification;
    }

    public function setClassification($classification): void
    {
        $this->classification = $classification;
    }

    public function getDeliveryDate()
    {
        return $this->deliveryDate;
    }

    public function setDeliveryDate($deliveryDate): void
    {
        $this->deliveryDate = $deliveryDate;
    }

    public function getAdditionalClassifications()
    {
        return $this->additionalClassifications;
    }

    public function setAdditionalClassifications($additionalClassifications): void
    {
        $this->additionalClassifications = $additionalClassifications;
    }

    public function getUnit()
    {
        return $this->unit;
    }

    public function setUnit($unit): void
    {
        $this->unit = $unit;
    }

    public function getQuantity(): ?float
    {
        return $this->quantity;
    }

    public function setQuantity(?float $quantity): void
    {
        $this->quantity = $quantity;
    }

    public function getDeliveryAddress()
    {
        return $this->deliveryAddress;
    }

    public function setDeliveryAddress($deliveryAddress): void
    {
        $this->deliveryAddress = $deliveryAddress;
    }

    public function getLocation()
    {
        if ($this->restrictedMode && is_array($this->location)) {
            return $this->restrictLocationData($this->location);
        }

        return $this->location;
    }

    public function setLocation($location): void
    {
        $this->location = $location;
    }

    public function getRelatedLot()
    {
        return $this->relatedLot;
    }

    public function setRelatedLot($relatedLot): void
    {
        $this->relatedLot = $relatedLot;
    }

    public function getAward()
    {
        return $this->award;
    }

    public function setAward($award): void
    {
        $this->award = $award;
    }

    public function getTender()
    {
        return $this->tender;
    }

    public function setTender($tender): void
    {
        $this->tender = $tender;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function setDescriptionEn(?string $descriptionEn): void
    {
        $this->descriptionEn = $descriptionEn;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }

    public function getDeliveryLocation()
    {
        return $this->deliveryLocation;
    }

    public function setDeliveryLocation($deliveryLocation)
    {
        $this->deliveryLocation = $deliveryLocation;

        return $this;
    }

    public function getProfile(): ?string
    {
        return $this->profile;
    }

    public function setProfile(?string $profile): Item
    {
        $this->profile = $profile;
        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): Item
    {
        $this->category = $category;
        return $this;
    }

    public function getAttributes()
    {
        return $this->attributes;
    }

    public function setAttributes($attributes)
    {
        $this->attributes = $attributes;
        return $this;
    }
}
