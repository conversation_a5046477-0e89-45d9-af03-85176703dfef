<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations\Document;
use Doctrine\ODM\MongoDB\Mapping\Annotations\Field;
use Doctrine\ODM\MongoDB\Mapping\Annotations\Id;
use Doctrine\ODM\MongoDB\Types\Type;

#[Document(collection: 'mongo_to_es')]
class MongoToEs
{
    #[Id(strategy: 'auto')]
    private ?string $id = null;

    #[Field(type: Type::STRING)]
    private string $type;

    #[Field(type: Type::DATE)]
    private \DateTimeInterface $dateCreated;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getDateCreated(): \DateTimeInterface
    {
        return $this->dateCreated;
    }

    public function setDateCreated(\DateTimeInterface $dateCreated): void
    {
        $this->dateCreated = $dateCreated;
    }
}
