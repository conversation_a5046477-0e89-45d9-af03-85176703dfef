<?php

namespace App\MessageHandler;

use App\Document\Inspection;
use App\Document\Monitoring;
use App\Message\InspectionMessage;
use App\Service\MarketCacheService;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\MongoDBException;
use GuzzleHttp\Client;
use League\Flysystem\FilesystemOperator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[AsMessageHandler]
class InspectionHandler extends BaseHandler
{
    private string $prozorroMonitoringUrl;

    /**
     * TenderHandler constructor.
     */
    public function __construct(
        DocumentManager $documentManager,
        SerializerInterface $serializer,
        LoggerInterface $apiLogger,
        MarketCacheService $marketCacheService,
        string $prozorroPublicPointPassword,
        string $prozorroPublicPointUrl,
        string $prozorroMarketApiLink,
        string $prozorroMarketApiPassword,
        FilesystemOperator $marketStorage,
        MessageBusInterface $messageBus,
        string $prozorroMonitoringUrl,
        private readonly string $prozorroMonitoringAuthName,
        private readonly string $prozorroMonitoringAuthPass
    ) {
        parent::__construct($documentManager, $serializer, $apiLogger, $marketCacheService, $prozorroPublicPointPassword, $prozorroPublicPointUrl, $prozorroMarketApiLink, $prozorroMarketApiPassword, $marketStorage, $messageBus);

        $this->prozorroMonitoringUrl = rtrim($prozorroMonitoringUrl, '/').'/';
    }

    /**
     * @throws MongoDBException
     * @throws \Throwable
     */
    public function __invoke(InspectionMessage $inspection)
    {
        try {
            $client = new Client();
            $response = $client->request(
                'GET',
                $this->prozorroMonitoringUrl.'inspections/'.$inspection->getId(),
                [
                    'auth' => [
                        $this->prozorroMonitoringAuthName,
                        $this->prozorroMonitoringAuthPass,
                    ],
                ]
            );

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            if (array_key_exists('config', $result)) {
                $data['inspectionConfig'] = $result['config'];
            }

            /** @var Inspection $object */
            $object = $this->getDocumentManager()->find(Inspection::class, $data['id']);

            $object = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data),
                    Inspection::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
                );

            $this->save($object);

            /* @var Monitoring $monitoring */
            if (is_array($data['monitoring_ids'])) {
                foreach ($data['monitoring_ids'] as $monitoringId) {
                    $monitoring = $this->getDocumentManager()->find(Monitoring::class, $monitoringId);
                    if (null === $monitoring) {
                        continue;
                    }

                    $monitoring->setHasInspection(true);

                    $this->save($monitoring);
                }
            }
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
            throw $exception;
        }
    }
}
