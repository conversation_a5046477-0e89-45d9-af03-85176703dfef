# Please change elasticsearch url to proper one before applying this migration (!)

curl -X POST "https://opensearch-stage.dzo.com.ua/dzo_product/_close?pretty"

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_product/_settings?pretty" -H 'Content-Type: application/json' -d'
{
  "analysis": {
    "normalizer": {
      "lowercase_normalizer": {
        "type": "custom",
        "filter": ["lowercase"]
      }
    }
  }
}
'

curl -X POST "https://opensearch-stage.dzo.com.ua/dzo_product/_open?pretty"

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_product/_mapping?pretty" -H 'Content-Type: application/json' -d'
{
  "properties": {
    "title": {
      "type": "icu_collation_keyword",
      "language": "uk",
      "country": "UK",
      "fields": {
        "title_keyword": {
          "type": "keyword",
          "normalizer": "lowercase_normalizer"
        }
      }
    }
  }
}
'
