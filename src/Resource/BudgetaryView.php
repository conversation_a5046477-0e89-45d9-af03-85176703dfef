<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\ItemProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Document\Budgetary;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;

#[ApiResource(
    shortName: 'budgetary',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_budgetary')),
        new Get(provider: ItemProvider::class, stateOptions: new Options(index: 'dzo_budgetary')),
    ],
    order: ['id' => 'DESC']
)]
#[ApiFilter(MatchFilter::class, properties: ['id' => 'exact'])]
#[ApiFilter(KeywordFilter::class, properties: ['code', 'title', 'titleEn', 'titleRu'])]
readonly class BudgetaryView
{
    public ?string $id;

    public ?string $code;

    public ?int $type;

    public ?string $title;

    public ?string $titleEn;

    /**
     * BudgetaryView constructor.
     */
    public function __construct(Budgetary $budgetary)
    {
        $this->id = $budgetary->getId();
        $this->code = $budgetary->getCode();
        $this->type = $budgetary->getType();
        $this->title = $budgetary->getTitle();
        $this->titleEn = $budgetary->getTitleEn();
    }
}
