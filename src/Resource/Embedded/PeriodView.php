<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Period;

/**
 * Class PeriodView.
 */
class PeriodView
{
    private ?string $id = null;

    private ?\DateTimeInterface $startDate = null;

    private ?\DateTimeInterface $endDate = null;

    private ?\DateTimeInterface $shouldStartAfter = null;

    /**
     * PeriodView constructor.
     */
    public function __construct(Period $period)
    {
        $this->id = $period->getId();
        $this->startDate = $period->getStartDate();
        $this->endDate = $period->getEndDate();
        $this->shouldStartAfter = $period->getShouldStartAfter();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function getShouldStartAfter(): ?\DateTimeInterface
    {
        return $this->shouldStartAfter;
    }
}
