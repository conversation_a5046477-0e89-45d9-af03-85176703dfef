name: '"fos:elastica:populate" job pipeline'

on:
  workflow_dispatch:
    inputs:
      env:
        description: "Environment to run a job in"
        required: true
        type: choice
        options:
          - prod
          - stage
          - test
      index:
        description: "Index"
        required: true
        type: choice
        options:
          - dzo_tender
          - dzo_plan
          - dzo_contract
          - dzo_classification
          - dzo_monitoring
          - dzo_inspection
          - dzo_budgetary
          - dzo_tender_identifier
          - dzo_plan_identifier
          - dzo_contract_identifier
          - dzo_contract_supplier
          - dzo_category
          - dzo_market_classification
          - dzo_profile
          - dzo_product

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      env: ${{ inputs.env }}
      index: ${{ inputs.index }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up prod AWS CLI
        if: env.env == 'prod'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up stage AWS CLI
        if: env.env == 'stage'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.STAGE_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.STAGE_AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up test AWS CLI
        if: env.env == 'test'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.TEST_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.TEST_AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.12.0

      - name: kubeconfig
        if: env.env == 'prod'
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-production --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: kubeconfig
        if: env.env != 'prod'
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-$env --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Login to Amazon ECR
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          aws ecr get-login-password | helm registry login --username AWS --password-stdin 206424851631.dkr.ecr.eu-central-1.amazonaws.com

      - name: Delete previous job if exists
        run: |
          formatted_index=$(echo $index | tr '_' '-')
          echo $formatted_index
          if kubectl get jobs -n $env | grep fos-elastica-populate-$formatted_index-helm-charts-job-0; then
            kubectl delete job fos-elastica-populate-$formatted_index-helm-charts-job-0 -n $env
          else
            echo "Job does not exist. Nothing to delete."
          fi

      - name: Deploy jobs
        run: |
          formatted_index=$(echo $index | tr '_' '-')
          export HELM_EXPERIMENTAL_OCI=1
          helm upgrade --install fos-elastica-populate-$formatted_index oci://206424851631.dkr.ecr.eu-central-1.amazonaws.com/helm-charts --version 0.3.3-jobs --namespace $env \
          --set image.tag=${{ github.sha }} \
          --set jobs[0].args[0]="fos:elastica:populate" \
          --set jobs[0].args[1]="--index" \
          --set jobs[0].args[2]="$index" \
          -f deploy/fos-elastica-populate.yaml
