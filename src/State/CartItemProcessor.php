<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Document\Cart;
use App\Document\CartItem;
use App\Document\Category;
use App\Document\Profile;
use App\Dto\CartItemDto;
use App\Manager\CartItemManager;
use App\Manager\CartManager;
use App\Manager\CategoryManager;
use App\Manager\ProductManager;
use App\Manager\ProfileManager;
use App\Resource\CartView;
use App\Security\JwtCheckerTrait;
use App\Service\CartItemService;
use App\Service\ProfileCheckService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

readonly class CartItemProcessor implements ProcessorInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private ProfileManager $profileManager,
        private CartManager $cartManager,
        private CartItemManager $cartItemManager,
        private ProductManager $productManager,
        private ProfileCheckService $profileCheckService,
        private CartItemService $cartItemService,
        private CategoryManager $categoryManager
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        if (!$data) {
            return null;
        }

        if (!$userId = $this->checkJwt()) {
            return null;
        }

        if ($cart = $this->cartManager->getUserCart($userId)) {
            $cart = $this->processData($cart, $data);
        }

        return new CartView($cart, $this->cartItemService->getCartItems($cart));
    }

    private function processData(Cart $cart, CartItemDto $cartItemDto): Cart
    {
        $profile = $category = null;
        $categoryId = false;
        /** @var Profile $profile */
        if ($cartItemDto->profileId && $profile = $this->profileManager->find($cartItemDto->profileId)) {
            if ($profile->getClassification()->getId() === '99999999-9') {
                throw new NotFoundHttpException('Restricted classification.');
            }

            $categoryId = $profile->getRelatedCategoryId();
        }

        if ($cartItemDto->categoryId) {
            $categoryId = $cartItemDto->categoryId;
        }

        /** @var Category $category */
        if ($categoryId && (!($category = $this->categoryManager->find($categoryId)) || $category->getStatus(
            ) == 'hidden')) {
            throw new NotFoundHttpException('Category hidden or does not exist');
        }

        if (!$profile && !$category) {
            throw new NotFoundHttpException('Category or profile does not exist');
        }

        if ($profile) {
            if ('active' === $profile->getStatus()) {
                if (!$this->profileCheckService->checkActive($profile)) {
                    throw new NotFoundHttpException('Product did not exist');
                }

                if (!$cartItem = $this->cartItemManager->getRepository()->findOneBy(
                    ['cart' => $cart, 'profileId' => $profile->getId()]
                )) {
                    $cartItem = new CartItem();
                    $cartItem->setCart($cart);
                    $cartItem->setProfileId($profile->getId());
                }
                if ($cartItemDto->quantity > 0) {
                    $cartItem->setQuantity($cartItem->getQuantity() + 1);
                    $this->cartItemManager->getDocumentManager()->persist($cartItem);
                    $cart->addCartItem($cartItem);
                } else {
                    $this->cartItemManager->getDocumentManager()->remove($cartItem);
                }
                $this->cartManager->save($cart);
            } elseif ('general' === $profile->getStatus()) {
                if ($this->profileCheckService->checkGeneral($cartItemDto)) {
                    if ($cartItem = $this->cartItemManager->getRepository()->findOneBy(
                        ['cart' => $cart, 'profileId' => $cartItemDto->profileId, 'config' => $cartItemDto->config]
                    )) {
                        $cartItem->setQuantity($cartItem->getQuantity() + 1);
                    } else {
                        $cartItem = new CartItem();
                        $cartItem->setCart($cart);
                        $cartItem->setProfileId($profile->getId());
                        $cartItem->setQuantity(1);
                        $cartItem->setConfig($this->clearConfig($cartItemDto->config));
                        $this->cartItemManager->getDocumentManager()->persist($cartItem);
                    }
                    $cart->addCartItem($cartItem);

                    $this->cartManager->save($cart);
                } else {
                    throw new NotFoundHttpException('Product with this config did not exist');
                }
            }
        }

        if ($cartItemDto->categoryId) {
            if ($this->profileCheckService->checkGeneral($cartItemDto)) {
                if ($cartItem = $this->cartItemManager->getRepository()->findOneBy(
                    ['cart' => $cart, 'categoryId' => $cartItemDto->categoryId, 'config' => $cartItemDto->config]
                )) {
                    $cartItem->setQuantity($cartItem->getQuantity() + 1);
                } else {
                    $cartItem = new CartItem();
                    $cartItem->setCart($cart);
                    $cartItem->setCategoryId($category->getId());
                    $cartItem->setQuantity(1);
                    $cartItem->setConfig($this->clearConfig($cartItemDto->config));
                    $this->cartItemManager->getDocumentManager()->persist($cartItem);
                }
                $cart->addCartItem($cartItem);

                $this->cartManager->save($cart);
            } else {
                throw new NotFoundHttpException('Product with this config did not exist');
            }
        }


        return $cart;
    }

    private function clearConfig(array $configs): array
    {
        $clearedConfig = [];
        $titles = [];
        foreach ($configs as $config) {
            if (!in_array($config['title'], $titles)) {
                $titles[] = $config['title'];
                $clearedConfig[] = $config;
            }
        }

        return $clearedConfig;
    }
}
