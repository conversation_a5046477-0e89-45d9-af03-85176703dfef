<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Image;

class ImageView
{
    private ?string $id = null;

    private ?string $sizes = null;

    private ?string $url = null;

    public function __construct(Image $image, ?string $awsCdnUrl = null)
    {
        $this->id = $image->getId();
        $this->sizes = $image->getSizes();
        if ($awsCdnUrl) {
            $this->url = $awsCdnUrl.$image->getUrl();
        } else {
            $this->url = $image->getUrl();
        }
    }

    public function __toString(): string
    {
        return $this->url;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getSizes(): ?string
    {
        return $this->sizes;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }
}
