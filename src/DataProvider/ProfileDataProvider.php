<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Document\Agreement;
use App\Manager\CategoryManager;
use App\Manager\ProfileManager;
use App\Resource\ProfileView;
use App\Service\CriteriaFilterService;
use Doctrine\ODM\MongoDB\DocumentManager;

final readonly class ProfileDataProvider implements ProviderInterface
{
    public function __construct(
        private ProfileManager $profileManager,
        private CategoryManager $categoryManager,
        private string $awsCdnUrl,
        private CriteriaFilterService $criteriaFilterService,
        private DocumentManager $documentManager,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->profileManager->find($uriVariables['id']);
        $agreement = null;

        if (!$document || $document->getStatus() !== 'active' || $document->getRelatedCategoryStatus() === 'hidden') {
            return null;
        }

        $category = null;
        if ($document->getRelatedCategoryId()) {
            if ($category = $this->categoryManager->find($document->getRelatedCategoryId())) {
                $this->criteriaFilterService->filterArchivedRequirementsFromProfile($document, $category);
            }
        }

        if ($document->getAgreementID()) {
            $agreement = $this->documentManager->getRepository(Agreement::class)->findOneBy(['prozorroId' => $document->getAgreementID()]);
        }

        return new ProfileView(
            $document,
            $this->criteriaFilterService,
            $category,
            $this->awsCdnUrl,
            $agreement,
        );
    }
}
