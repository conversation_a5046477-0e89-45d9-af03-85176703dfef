<?php

namespace App\Legacy\MessageHandler;

use App\Legacy\Message\LegacyTenderActionMessage;
use App\Legacy\Repository\LegacyTenderRepository;
use App\Repository\TenderRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class LegacyTenderActionHandler
{
    public function __construct(
        private TenderRepository $tenderRepository,
        private LegacyTenderRepository $legacyTenderRepository
    ) {
    }

    public function __invoke(LegacyTenderActionMessage $message): void
    {
        if ('bid' === $message->action) {
            $tender = $this->tenderRepository->find($message->tenderId);
            if (!$tender) {
                return;
            }

            $tender->getDzoData()->addBidsUser($message->userId);

            $this->tenderRepository->save($tender);
        }

        if ('announced' === $message->action) {
            $tender = $this->tenderRepository->find($message->tenderId);
            if (!$tender) {
                return;
            }

            $ownerId = $this->legacyTenderRepository->tenderOwnerByTenderId($message->tenderId);
            if (0 !== $ownerId) {
                $tender->getDzoData()->addOwnerId($ownerId);
                $this->tenderRepository->save($tender);
            }
        }
    }
}
