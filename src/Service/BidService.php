<?php

namespace App\Service;

use App\Document\Embedded\Bid;
use App\Document\Tender;
use App\Repository\TenderRepository;

class BidService
{
    public function __construct(
        private readonly TenderRepository $tenderRepository
    ) {
    }

    public function addBidToTender(Tender $tender, array $bid): void
    {
        $isBidFound = false;
        $tenderBids = $tender->getDzoData()->getBids();

        /** @var Bid $tenderBid */
        foreach ($tenderBids as $tenderBid) {
            if ($tenderBid->getId() == $bid['id']) {
                $tenderBid->setApiId($bid['api_id']);
                $tenderBid->setPay($bid['pay']);
                $tenderBid->setSigned($bid['signed']);
                $tenderBid->setValue($bid['amount']);
                $tenderBid->setCurrency($bid['value_currency']);
                $tenderBid->setValueAddedTax($bid['value_valueAddedTaxIncluded'] ?? false);
                $isBidFound = true;
                break;
            }
        }
        if (!$isBidFound) {
            $tenderBids[$bid['id']] = new Bid(
                $bid['id'],
                $bid['api_id'],
                $bid['pay'],
                $bid['signed'],
                $bid['new_user_id'],
                $bid['amount'],
                $bid['value_currency'],
                $bid['value_valueAddedTaxIncluded'] ?? false
            );
        }

        $tender->getDzoData()->setBids($tenderBids);
        $this->tenderRepository->save($tender);
    }
}