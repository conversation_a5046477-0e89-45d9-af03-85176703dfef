doctrine_mongodb:
  auto_generate_proxy_classes: false
  auto_generate_hydrator_classes: false
# TODO: figure out how to use cache pools here
#  document_managers:
#    default:
#      metadata_cache_driver:
#        type: service
#        id: doctrine_mongodb.system_cache_provider

#services:
#  doctrine_mongodb.system_cache_provider:
#    class: Symfony\Component\Cache\DoctrineProvider
#    public: false
#    arguments:
#      - '@doctrine_mongodb.system_cache_pool'

#framework:
#  cache:
#    pools:
#      doctrine_mongodb.system_cache_pool:
#        adapter: cache.system
