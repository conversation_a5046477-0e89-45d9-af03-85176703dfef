# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    env(MONGODB_URL): ''
    env(MONGODB_DATABASE): ''
    app.api.prozorro_public_point_url: '%env(string:PROZORRO_API_LINK)%'
    app.api.prozorro_public_point_password: '%env(string:PROZORRO_API_PASSWORD)%'
    app.api.prozorro_monitoring_url: '%env(string:PROZORRO_MONITORING_LINK)%'
    app.api.prozorro_monitoring_auth_name: '%env(string:PROZORRO_MONITORING_AUTH_NAME)%'
    app.api.prozorro_monitoring_auth_pass: '%env(string:PROZORRO_MONITORING_AUTH_PASS)%'
    app.api.prozorro_public_auction_point_url: '%env(string:PROZORRO_AUCTION_API_LINK)%'
    app.api.prozorro_market_api_link: '%env(string:PROZORRO_MARKET_API_LINK)%'
    app.api.prozorro_market_api_password: '%env(string:PROZORRO_MARKET_API_PASSWORD)%'
    classification_sources:
        cpv_en: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/cpv_en.json'
        cpv_ru: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/cpv_ru.json'
        cpv_uk: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/dk021_uk.json'
        roads: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/ua_road.json'
        gmdn: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/gmdn.json'
        ccce_ua: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/classifiers/ccce_ua.json'
        inn: 'https://medicines-registry.prozorro.gov.ua/api/1.0/registry/inn2atc.json'
    app.api.prozorro_funder_list_link: '%env(string:PROZORRO_FUNDERS_LIST_LINK)%'
    aws.cdn.url: '%env(string:FLYSYSTEM_CDN_UPLOAD)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $awsCdnUrl: '%aws.cdn.url%'
            $productsFinder: '@fos_elastica.finder.dzo_product'
            $profilesFinder: '@fos_elastica.finder.dzo_profile'
            $tendersFinder: '@fos_elastica.finder.dzo_tender'
            Predis\ClientInterface: '@snc_redis.default'
            $marketClassificationFinder: '@fos_elastica.finder.dzo_market_classification'
            $prozorroPublicPointUrl: '%app.api.prozorro_public_point_url%'
            $prozorroPublicPointPassword: '%app.api.prozorro_public_point_password%'
            $prozorroMonitoringUrl: '%app.api.prozorro_monitoring_url%'
            $prozorroMonitoringAuthName: '%app.api.prozorro_monitoring_auth_name%'
            $prozorroMonitoringAuthPass: '%app.api.prozorro_monitoring_auth_pass%'
            $classificationSources: '%classification_sources%'
            $prozorroPublicAuctionPointUrl: '%app.api.prozorro_public_auction_point_url%'
            $prozorroMarketApiLink: '%app.api.prozorro_market_api_link%'
            $prozorroMarketApiPassword: '%app.api.prozorro_market_api_password%'
            $prozorroFundersListLink: '%app.api.prozorro_funder_list_link%'
            $listsDir: "%kernel.project_dir%/public/lists"
            $cdbFilesData:
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/criteria/article_16.json'
                    name: 'article_16.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/criteria/article_17.json'
                    name: 'article_17.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/criteria/other.json'
                    name: 'other.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/AMCU/violation_amcu.json'
                    name: 'violation_amcu.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/AMCU/requested_remedies_type.json'
                    name: 'requested_remedies_type.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/AMCU/amcu.json'
                    name: 'amcu.json'
                -   url: 'https://raw.githubusercontent.com/ProzorroUKR/standards/master/AMCU/amcu_24.json'
                    name: 'amcu_24.json'
                -   url: 'https://prozorroukr.github.io/standards/codelists/tender/tender_funder.json'
                    name: 'tender_funder.json'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Tests/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller/'
        tags: [ 'controller.service_arguments' ]

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones


    ########################################################################################################################
    #####    SERIALIZATION    ##############################################################################################
    ########################################################################################################################

    datetime_normalizer:
        class: Symfony\Component\Serializer\Normalizer\DateTimeNormalizer
        public: false
        tags: [ serializer.normalizer ]

    App\Serializer\Normalizer\NullableDateTimeNormalizer:
        arguments:
            $denormalizer: '@serializer.normalizer.datetime'
        tags: [ { name: 'serializer.normalizer', priority: 9 } ]

    App\Serializer\Normalizer\CustomElasticsearchNormalizer:
        # By default .inner is passed as argument
        decorates: 'api_platform.elasticsearch.normalizer.item'

    ########################################################################################################################
    #####    Providers        ##############################################################################################
    ########################################################################################################################
    App\DataProvider\CollectionFilterProvider:
        bind:
            $collectionProvider: '@api_platform.doctrine_mongodb.odm.state.collection_provider'

    ########################################################################################################################
    #####    Services         ##############################################################################################
    ########################################################################################################################

    App\MessageHandler\MongoToEsMessageHandler:
        bind:
            $tenderPersister: '@fos_elastica.object_persister.dzo_tender'
            $planPersister: '@fos_elastica.object_persister.dzo_plan'
            $contractPersister: '@fos_elastica.object_persister.dzo_contract'
            $monitoringPersister: '@fos_elastica.object_persister.dzo_monitoring'
            $inspectionPersister: '@fos_elastica.object_persister.dzo_inspection'
            $categoryPersister: '@fos_elastica.object_persister.dzo_category'
            $profilePersister: '@fos_elastica.object_persister.dzo_profile'
            $productPersister: '@fos_elastica.object_persister.dzo_product'

    App\MessageHandler\PopularityHandler:
        bind:
            $categoryPersister: '@fos_elastica.object_persister.dzo_category'
            $profilePersister: '@fos_elastica.object_persister.dzo_profile'
            $productPersister: '@fos_elastica.object_persister.dzo_product'
            $classificationPersister: '@fos_elastica.object_persister.dzo_market_classification'

    App\MessageHandler\CategoryStatusSetterHandler:
        bind:
            $profilePersister: '@fos_elastica.object_persister.dzo_profile'
            $productPersister: '@fos_elastica.object_persister.dzo_product'

    App\MessageHandler\OldIdHandler:
        bind:
            $tenderPersister: '@fos_elastica.object_persister.dzo_tender'
            $planPersister: '@fos_elastica.object_persister.dzo_plan'
            $contractPersister: '@fos_elastica.object_persister.dzo_contract'

    App\MessageHandler\MarketUpdateVisibilityHandler:
        bind:
            $productPersister: '@fos_elastica.object_persister.dzo_product'
            $profilePersister: '@fos_elastica.object_persister.dzo_profile'

    App\Security\JwtDecorator:
        decorates: 'api_platform.openapi.factory'
        arguments: [ '@.inner' ]

    Aws\S3\S3Client:
        public: true
        arguments: [ { region: '%env(AWS_REGION)%', version: 'latest' } ]

    App\Service\SlackNotifier:
        arguments:
            $webhookUrl: '%env(SLACK_WEBHOOK_URL)%'
