<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;
use App\Constants\DzoConstants;

/**
 * Class MatchFilter.
 */
final class RequirementsProductFilter extends AbstractSearchFilter
{
    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): array {
        $searches = [];
        foreach ($context['filters'] ?? [] as $property => $values) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }

            foreach ($values as $filter) {
                $query = $this->getQuery($property, $filter, null);
                if (!empty($query)) {
                    $searches[] = $query;
                }
            }
        }

        if (!$searches) {
            return $clauseBody;
        }

        return array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $booleanQuery = $mustQuery = [];
        $criterionId = DzoConstants::LOCAL_ORIGIN_LEVEL_CLASSIFICATION_ID;
        if ($values['requirement'] === 'Наявність локалізації') {
            $boolVal = filter_var($values['value'], FILTER_VALIDATE_BOOLEAN);
            if ($boolVal === true) {
                $property = 'requirementResponses.classification';
                $mustQuery = [
                    ['match' => [$property . '.id' => $criterionId]],
                ];
            }
        } else {
            $mustQuery = [
                ['match' => [$property . '.requirement' => $values['requirement']]],
            ];
            if (!empty($values['values'])) {
                if ('numberRequirements' === $property) {
                    $values['values'] = array_map('intval', $values['values']);
                }
                $mustQuery[] = ['terms' => [$property . '.values' => $values['values']]];
            }

            if (isset($values['minValue'])) {
                $mustQuery[] = ['range' => [$property . '.values' => ['gte' => $values['minValue']]]];
            }

            if (isset($values['maxValue'])) {
                $mustQuery[] = ['range' => [$property . '.values' => ['lte' => $values['maxValue']]]];
            }

            if (!empty($values['values'])) {
                $booleanQuery['filter'] = [
                    'script' => [
                        'script' => [
                            'id'     => $property,
                            'params' => ['values' => $values['values']],
                        ],
                    ],
                ];
            }
        }
        if (!empty($mustQuery)) {
            $booleanQuery['must'] = $mustQuery;

            return [
                'nested' => [
                    'path'  => $property,
                    'query' => [
                        'bool' => $booleanQuery,
                    ],
                ],
            ];
        }

        return [];
    }
}
