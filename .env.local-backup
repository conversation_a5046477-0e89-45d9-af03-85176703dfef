# ============ STAGE ==============
# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration
APP_DEBUG=true
###> symfony/framework-bundle ###
APP_ENV=dev
COMPOSE_PROJECT_NAME=some.name
APP_SECRET=50a237dbdd8d9321622dc7016cc106e7

PROZORRO_API_LINK='https://public-api-staging.prozorro.gov.ua/api/2.5'
PROZORRO_API_PASSWORD='d868313085eb4dc2a3150a0b832b7683:'
PROZORRO_AUCTION_API_LINK='https://auction-staging.prozorro.gov.ua/api/auctions/'
PROZORRO_MONITORING_LINK='https://audit-api-staging.prozorro.gov.ua/api/2.5/'
PROZORRO_MONITORING_AUTH_NAME='netcast.com.ua'
PROZORRO_MONITORING_AUTH_PASS='945eeb507a2f47dbb82b5a05c2754878'
PROZORRO_MARKET_API_LINK=https://catalog-api-staging.prozorro.gov.ua/api/
PROZORRO_MARKET_API_PASSWORD=kV24KaN8TBm1mVdCkq0qyMnv
PROZORRO_FUNDERS_LIST_LINK=https://prozorroukr.github.io/standards/codelists/tender/tender_funder.json

#PROZORRO_API_LINK='https://public-api-sandbox-2.prozorro.gov.ua/api/2.5/'
#PROZORRO_API_PASSWORD='cdcd668c9e714d6cb9227a733a04ff8e:'
#PROZORRO_AUCTION_API_LINK='https://auction-sandbox-2.prozorro.gov.ua/api/auctions/'
#PROZORRO_MONITORING_LINK='https://audit-api-sandbox-2.prozorro.gov.ua/api/2.5/'
#PROZORRO_MONITORING_AUTH_NAME='netcast.com.ua'
#PROZORRO_MONITORING_AUTH_PASS='LFsU4uELQVhDOmvIM0lsatSYWRmFdChQ'
#PROZORRO_MARKET_API_LINK=https://catalog-api-sandbox-2.prozorro.gov.ua/api/
#PROZORRO_MARKET_API_PASSWORD=testpassword
#PROZORRO_FUNDERS_LIST_LINK=https://prozorroukr.github.io/standards/codelists/tender/tender_funder.json

#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
###< symfony/framework-bundle ###
###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(.*\.dzo\.com\.ua|.*\.dzo\.lh|dzo\.lh|dzo\.com\.ua|localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> doctrine/mongodb-odm-bundle ###
MONGO_ROOT_USERNAME=root
MONGO_ROOT_PASSWORD=UltraPassw0rD
MONGO_USER_USERNAME=root
MONGO_USER_PASSWORD=UltraPassw0rD
MONGO_DB_NAME=registry
MONGODB_URL=mongodb://root:<EMAIL>:27017/?authSource=admin&readPreference=primary&appname=MongoDB%20Compass&ssl=false&socketTimeoutMS=300000&connectTimeoutMS=5000
###< doctrine/mongodb-odm-bundle ###

# Telegram error chanel data
TELEGRAM_ALERT_TOKEN=qwerty12345
TELEGRAM_BOT_KEY=qwerty12345
TELEGRAM_LOG_CHANNEL=-123456
TELEGRAM_ALERT_CHANNEL=-123345
IS_TELEGRAM_LOGS_ENABLED=false
###< telegram ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=doctrine://default
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=amqp://guest:guest@rabbit:5672/%2f/tenders
###< symfony/messenger ###

###> friendsofsymfony/elastica-bundle ###
ELASTICSEARCH_URL=https://opensearch-stage.dzo.com.ua:443
###< friendsofsymfony/elastica-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://db_user:db_password@127.0.0.1:3306/db_name?serverVersion=5.7"

#====================STAGE=====================#
DATABASE_URL="postgresql://userdzo:<EMAIL>:5432/users?serverVersion=5.7&charset=utf8"
LEGACY_DATABASE_URL="mysql://sandbox:b5E3KkUpKrvsL4KpwWrs@**************:3306/sandboxdb?serverVersion=5.7&charset=utf8mb4"
LEGACY_ARCHIVE_DATABASE_URL="mysql://sandbox:b5E3KkUpKrvsL4KpwWrs@**************:3306/sandboxarchive?serverVersion=5.7&charset=utf8mb4"
#====================STAGE=====================#
###< doctrine/doctrine-bundle ###

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=55070525fa656cff1505acd0154a545f81f66365ad5d480e1b66a0bdfc85648c
###< lexik/jwt-authentication-bundle ###

###FLYSYSTEM###
FLYSYSTEM_ADAPTER=local
FLYSYSTEM_CDN_UPLOAD=https://dzo-market-321462487661.s3.eu-central-1.amazonaws.com/public/upload/market
FLYSYSTEM_DIRECTORY_UPLOAD=public/upload
###FLYSYSTEM###

###AWS###
AWS_BUCKET=AWS_BUCKET
AWS_KEY=AWS_KEY
AWS_SECRET=AWS_SECRET
AWS_REGION=eu-central-1
###AWS###

###> snc/redis-bundle ###
# passwords that contain special characters (@, %, :, +) must be urlencoded
REDIS_URL=redis://redis:6379
###< snc/redis-bundle ###

###> sentry/sentry-symfony ###
SENTRY_ENV=local
SENTRY_DSN="https://<EMAIL>/4508409049317456"
###< sentry/sentry-symfony ###

SLACK_WEBHOOK_URL="*********************************************************************************" #yastin
#SLACK_WEBHOOK_URL="*********************************************************************************" #DZO
