<?php

namespace App\Document;

use App\Document\Embedded\Document;
use App\Document\Embedded\DzoData;
use App\Document\Embedded\Item;
use App\Document\Embedded\Lot;
use App\Document\Embedded\Organization;
use App\Document\Embedded\Period;
use App\Document\Embedded\ProcuringEntity;
use App\Document\Embedded\Value;
use App\Helper\RestrictedDataHelper;
use App\Repository\TenderRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[MongoDB\Document(repositoryClass: TenderRepository::class)]
class Tender implements HasDateModifiedInterface
{
    use RestrictedDataHelper;

    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $tenderID = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $data;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $procurementMethod = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $mode = null;

    #[MongoDB\Field(type: Type::BOOL, nullable: true)]
    #[SerializedName('is_masked')]
    protected ?bool $isMasked = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    protected ?int $numberOfBids = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    protected ?Period $awardPeriod = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $mainProcurementCategory = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $auctionUrl = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    protected $enquiryPeriod;

    #[MongoDB\Field(type: Type::RAW)]
    protected ?string $submissionMethod = null;

    #[MongoDB\EmbedOne(targetDocument: ProcuringEntity::class)]
    protected ?ProcuringEntity $procuringEntity = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $owner = null;

    /**
     * @var Collection|Document[]
     */
    #[MongoDB\EmbedMany(targetDocument: Document::class)]
    protected Collection|array $documents;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $title = null;

    #[MongoDB\Field(name: 'title_en', type: Type::STRING, nullable: true)]
    protected ?string $titleEn = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\Field(name: 'description_en', type: Type::STRING, nullable: true)]
    protected ?string $descriptionEn = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $plans;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $procurementMethodRationale = null;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    protected ?Value $guarantee = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::DESC)]
    protected $dateModified;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $noticePublicationDate = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    protected $tenderPeriod;

    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    protected $contracts;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    protected $auctionPeriod;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $procurementMethodType = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $awards;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    protected $date;

    #[MongoDB\Field(type: Type::RAW)]
    protected $milestones;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    protected $minimalStep;

    /**
     * @var Collection|Item[]
     */
    #[MongoDB\EmbedMany(targetDocument: Item::class)]
    protected Collection|array $items;

    #[MongoDB\Field(type: Type::RAW)]
    protected $bids;

    #[MongoDB\Field(type: Type::RAW)]
    protected $reviewRequests;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    protected $value;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected $awardCriteria;

    #[MongoDB\Field(type: Type::RAW)]
    private $features;

    #[MongoDB\Field(type: Type::RAW)]
    private $questions;

    #[MongoDB\Field(type: Type::RAW)]
    private $complaints;

    #[MongoDB\Field(type: Type::RAW)]
    private $agreements;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    private $qualificationPeriod;

    #[MongoDB\EmbedOne(targetDocument: Period::class)]
    private $complaintPeriod;

    /**
     * @var Collection|Lot[]
     */
    #[MongoDB\EmbedMany(targetDocument: Lot::class)]
    private Collection|array $lots;

    #[MongoDB\Field(type: Type::RAW)]
    private $agreementDuration;

    #[MongoDB\Field(type: Type::RAW)]
    private $maxAwardsCount;

    #[MongoDB\Field(type: Type::RAW)]
    private $qualifications;

    #[MongoDB\Field(type: Type::RAW)]
    private $cancellations;

    #[MongoDB\EmbedMany(targetDocument: Organization::class)]
    private Collection|array $funders;

    #[MongoDB\Field(type: Type::RAW)]
    private $buyers;

    #[MongoDB\Field(type: Type::RAW)]
    private $revisions;

    #[MongoDB\Field(type: Type::RAW)]
    private $cause;

    #[MongoDB\Field(type: Type::RAW)]
    private $causeDescription;

    #[MongoDB\Field(type: Type::RAW)]
    private $criteria;

    #[MongoDB\Field(name: 'stage2TenderId', type: Type::STRING, nullable: true)]
    private ?string $stage2TenderId = null;

    #[MongoDB\Field(type: Type::BOOL)]
    private bool $hasMonitoring = false;

    #[MongoDB\Field(type: Type::BOOL)]
    private bool $hasActiveMonitoring = false;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: 'desc')]
    private ?\DateTimeInterface $monitoringDateModified = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $tenderConfig;

    #[MongoDB\EmbedOne(targetDocument: DzoData::class)]
    private ?DzoData $dzoData = null;

    private bool $restrictedMode = false;

    #[MongoDB\EmbedOne(targetDocument: Organization::class)]
    private ?Organization $inspector = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private ?int $oldId = null;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->items = new ArrayCollection();
        $this->lots = new ArrayCollection();
        $this->funders = new ArrayCollection();
    }

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
        foreach ($this->items as $item) {
            $item->setRestrictedMode($restrictedMode);
        }
        // restrict only sign document
        foreach ($this->documents as $document) {
            $document->setRestrictedMode($restrictedMode, true);
        }

        foreach ($this->funders as $document) {
            $document->setRestrictedMode($restrictedMode);
        }
    }

    /**
     * @return string|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getIsMasked(): ?bool
    {
        return $this->isMasked;
    }

    public function setIsMasked(?bool $isMasked): void
    {
        $this->isMasked = $isMasked;
    }

    public function getMode(): ?string
    {
        return $this->mode;
    }

    public function setMode(?string $mode): void
    {
        $this->mode = $mode;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setData($data): void
    {
        $this->data = $data;
    }

    public function getProcurementMethod(): ?string
    {
        return $this->procurementMethod;
    }

    public function setProcurementMethod(?string $procurementMethod): void
    {
        $this->procurementMethod = $procurementMethod;
    }

    public function getNumberOfBids(): ?int
    {
        return $this->numberOfBids;
    }

    public function setNumberOfBids(?int $numberOfBids): void
    {
        $this->numberOfBids = $numberOfBids;
    }

    public function getAwardPeriod()
    {
        return $this->awardPeriod;
    }

    public function setAwardPeriod($awardPeriod): void
    {
        $this->awardPeriod = $awardPeriod;
    }

    public function getMainProcurementCategory(): ?string
    {
        return $this->mainProcurementCategory;
    }

    public function setMainProcurementCategory(?string $mainProcurementCategory): void
    {
        $this->mainProcurementCategory = $mainProcurementCategory;
    }

    public function getAuctionUrl(): ?string
    {
        return $this->auctionUrl;
    }

    public function setAuctionUrl(?string $auctionUrl): void
    {
        $this->auctionUrl = $auctionUrl;
    }

    public function getEnquiryPeriod()
    {
        return $this->enquiryPeriod;
    }

    public function setEnquiryPeriod($enquiryPeriod): void
    {
        $this->enquiryPeriod = $enquiryPeriod;
    }

    public function getSubmissionMethod(): ?string
    {
        return $this->submissionMethod;
    }

    public function setSubmissionMethod(?string $submissionMethod): void
    {
        $this->submissionMethod = $submissionMethod;
    }

    public function getProcuringEntity()
    {
        return $this->procuringEntity;
    }

    public function setProcuringEntity($procuringEntity): void
    {
        $this->procuringEntity = $procuringEntity;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): void
    {
        $this->owner = $owner;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function setDescription($description): void
    {
        $this->description = $description;
    }

    public function getDocuments()
    {
        return $this->documents;
    }

    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getPlans()
    {
        return $this->plans;
    }

    public function setPlans($plans): void
    {
        $this->plans = $plans;
    }

    public function getTenderID(): ?string
    {
        return $this->tenderID;
    }

    public function setTenderID(?string $tenderID): void
    {
        $this->tenderID = $tenderID;
    }

    public function getProcurementMethodRationale()
    {
        return $this->procurementMethodRationale;
    }

    public function setProcurementMethodRationale($procurementMethodRationale): void
    {
        $this->procurementMethodRationale = $procurementMethodRationale;
    }

    public function getGuarantee()
    {
        return $this->guarantee;
    }

    public function setGuarantee($guarantee): void
    {
        $this->guarantee = $guarantee;
    }

    /**
     * @return mixed
     */
    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    /**
     * @throws \Exception
     */
    public function setDateModified($dateModified): void
    {
        if (is_string($dateModified)) {
            $dateModified = new \DateTime($dateModified);
        }
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws \Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new \DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getNoticePublicationDate(): ?\DateTimeInterface
    {
        return $this->noticePublicationDate;
    }

    /**
     * @throws \Exception
     */
    public function setNoticePublicationDate($noticePublicationDate): void
    {
        if (is_string($noticePublicationDate)) {
            $noticePublicationDate = new \DateTime($noticePublicationDate);
        }
        $this->noticePublicationDate = $noticePublicationDate;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getTenderPeriod()
    {
        return $this->tenderPeriod;
    }

    public function setTenderPeriod($tenderPeriod): void
    {
        $this->tenderPeriod = $tenderPeriod;
    }

    public function getContracts()
    {
        if ($this->restrictedMode && is_array($this->contracts)) {
            $restrictedContracts = $this->contracts;

            foreach ($restrictedContracts as &$restrictedContract) {
                if (array_key_exists('documents', $restrictedContract)) {
                    $restrictedContract['documents'] = array_map(function ($document) {
                        return $this->restrictDocumentData($document);
                    }, $restrictedContract['documents']);
                }

                if (array_key_exists('items', $restrictedContract)) {
                    $restrictedContract['items'] = array_map(function ($item) {
                        if (array_key_exists('deliveryDate', $item)) {
                            $item['deliveryDate'] = $this->restrictPeriodData($item['deliveryDate']);
                        }

                        if (array_key_exists('deliveryAddress', $item)) {
                            $item['deliveryAddress'] = $this->restrictAddressData($item['deliveryAddress']);
                        }

                        if (array_key_exists('devlieryLocation', $item)) {
                            $item['devlieryLocation'] = $this->restrictLocationData($item['devlieryLocation']);
                        }

                        return $item;
                    }, $restrictedContract['items']);
                }

                if (array_key_exists('suppliers', $restrictedContract)) {
                    $restrictedContract['suppliers'] = array_map(function ($supplier) {
                        return $this->restrictCompanyData($supplier);
                    }, $restrictedContract['suppliers']);
                }
            }

            return $restrictedContracts;
        }

        return $this->contracts;
    }

    public function setContracts($contracts): void
    {
        $this->contracts = $contracts;
    }

    public function getAuctionPeriod()
    {
        return $this->auctionPeriod;
    }

    public function setAuctionPeriod($auctionPeriod): void
    {
        $this->auctionPeriod = $auctionPeriod;
    }

    public function getProcurementMethodType()
    {
        return $this->procurementMethodType;
    }

    public function setProcurementMethodType($procurementMethodType): void
    {
        $this->procurementMethodType = $procurementMethodType;
    }

    public function getAwards()
    {
        if ($this->restrictedMode && is_array($this->awards)) {
            $restrictedAwards = $this->awards;
            foreach ($restrictedAwards as &$restrictedAward) {
                if (array_key_exists('complaints', $restrictedAward)) {
                    $restrictedAward['complaints'] = $this->restrictComplaints($restrictedAward['complaints']);
                }

                if (array_key_exists('suppliers', $restrictedAward)) {
                    $restrictedAward['suppliers'] = array_map(function ($supplier) {
                        return $this->restrictCompanyData($supplier);
                    }, $restrictedAward['suppliers']);
                }

                if (array_key_exists('documents', $restrictedAward)) {
                    $restrictedAward['documents'] = array_map(function ($document) {
                        return $this->restrictDocumentData($document);
                    }, $restrictedAward['documents']);
                }
            }

            return $restrictedAwards;
        }

        return $this->awards;
    }

    public function setAwards($awards): void
    {
        $this->awards = $awards;
    }

    public function getDate()
    {
        return $this->date;
    }

    /**
     * @throws \Exception
     */
    public function setDate($date): void
    {
        if (is_string($date)) {
            $date = new \DateTime($date);
        }
        $this->date = $date;
    }

    public function getMilestones()
    {
        return $this->milestones;
    }

    public function setMilestones($milestones): void
    {
        $this->milestones = $milestones;
    }

    public function getMinimalStep()
    {
        return $this->minimalStep;
    }

    public function setMinimalStep($minimalStep): void
    {
        $this->minimalStep = $minimalStep;
    }

    public function getItems()
    {
        return $this->items;
    }

    public function setItems($items): void
    {
        $this->items = $items;
    }

    public function getBids()
    {
        if ($this->restrictedMode && is_array($this->bids)) {
            $restrictedBids = $this->bids;
            foreach ($restrictedBids as &$restrictedBid) {
                if (array_key_exists('participationUrl', $restrictedBid)) {
                    $restrictedBid['participationUrl'] = 'Приховано';
                }
                if (array_key_exists('lotValues', $restrictedBid)) {
                    $restrictedBid['lotValues'] = array_map(function ($lotValue) {
                        return array_merge($lotValue, [
                            'participationUrl' => 'Приховано',
                            'subcontractingDetails' => 'Приховано',
                        ]);
                    }, $restrictedBid['lotValues']);
                }
                if (array_key_exists('tenderers', $restrictedBid)) {
                    $restrictedBid['tenderers'] = array_map(function ($tenderer) {
                        return $this->restrictCompanyData($tenderer);
                    }, $restrictedBid['tenderers']);
                }
                if (array_key_exists('documents', $restrictedBid)) {
                    $restrictedBid['documents'] = array_map(function ($document) {
                        return array_merge($this->restrictDocumentData($document), [
                            'confidentialityRationale' => 'Приховано',
                        ]);
                    }, $restrictedBid['documents']);
                }
            }

            return $restrictedBids;
        }

        return $this->bids;
    }

    public function setBids($bids): void
    {
        $this->bids = $bids;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value): void
    {
        $this->value = $value;
    }

    public function getAwardCriteria()
    {
        return $this->awardCriteria;
    }

    public function setAwardCriteria($awardCriteria): void
    {
        $this->awardCriteria = $awardCriteria;
    }

    public function getFeatures()
    {
        return $this->features;
    }

    public function setFeatures($features): void
    {
        $this->features = $features;
    }

    public function getQuestions()
    {
        if ($this->restrictedMode && is_array($this->questions)) {
            $restrictedQuestions = $this->questions;
            foreach ($restrictedQuestions as $key => &$restrictedQuestion) {
                if (array_key_exists('author', $restrictedQuestion)) {
                    $restrictedQuestion['author'] = $this->getRestrictedAuthor($restrictedQuestion['author']);
                }

                $restrictedQuestions[$key] = array_merge($restrictedQuestion, [
                    'title' => 'Приховано',
                    'description' => 'Приховано',
                    'answer' => 'Приховано',
                ]);
            }

            return $restrictedQuestions;
        }

        return $this->questions;
    }

    public function setQuestions($questions): void
    {
        $this->questions = $questions;
    }

    public function getComplaints()
    {
        if ($this->restrictedMode && is_array($this->complaints)) {
            return $this->restrictComplaints($this->complaints);
        }

        return $this->complaints;
    }

    public function setComplaints($complaints): void
    {
        $this->complaints = $complaints;
    }

    public function getAgreements()
    {
        if ($this->restrictedMode && is_array($this->agreements)) {
            $restrictedAgreements = $this->agreements;
            foreach ($restrictedAgreements as &$restrictedAgreement) {
                if (array_key_exists('tenderers', $restrictedAgreement)) {
                    $restrictedAgreement['tenderers'] = array_map(function ($tenderer) {
                        if (array_key_exists('address', $tenderer)) {
                            $tenderer['address'] = $this->restrictAddressData($tenderer['address']);
                        }

                        if (array_key_exists('contactPoint', $tenderer)) {
                            $tenderer['contactPoint'] = $this->restrictContactPointData($tenderer['contactPoint']);
                        }
                    }, $restrictedAgreement['tenderers']);
                }
            }

            return $restrictedAgreements;
        }

        return $this->agreements;
    }

    public function setAgreements($agreements): void
    {
        $this->agreements = $agreements;
    }

    public function getQualificationPeriod()
    {
        return $this->qualificationPeriod;
    }

    public function setQualificationPeriod($qualificationPeriod): void
    {
        $this->qualificationPeriod = $qualificationPeriod;
    }

    public function getLots()
    {
        return $this->lots;
    }

    public function setLots($lots): void
    {
        $this->lots = $lots;
    }

    public function getAgreementDuration()
    {
        return $this->agreementDuration;
    }

    public function setAgreementDuration($agreementDuration): void
    {
        $this->agreementDuration = $agreementDuration;
    }

    public function getMaxAwardsCount()
    {
        return $this->maxAwardsCount;
    }

    public function setMaxAwardsCount($maxAwardsCount): void
    {
        $this->maxAwardsCount = $maxAwardsCount;
    }

    public function getQualifications()
    {
        return $this->qualifications;
    }

    public function setQualifications($qualifications): void
    {
        $this->qualifications = $qualifications;
    }

    public function getCancellations()
    {
        return $this->cancellations;
    }

    public function setCancellations($cancellations): void
    {
        $this->cancellations = $cancellations;
    }

    public function getBuyers()
    {
        return $this->buyers;
    }

    public function setBuyers($buyers): void
    {
        $this->buyers = $buyers;
    }

    public function getRevisions()
    {
        return $this->revisions;
    }

    public function setRevisions($revisions): void
    {
        $this->revisions = $revisions;
    }

    public function getCause()
    {
        return $this->cause;
    }

    public function setCause($cause): void
    {
        $this->cause = $cause;
    }

    public function getCauseDescription()
    {
        return $this->causeDescription;
    }

    public function setCauseDescription($causeDescription): void
    {
        $this->causeDescription = $causeDescription;
    }

    public function getStage2TenderId()
    {
        return $this->stage2TenderId;
    }

    public function setStage2TenderId($stage2TenderId): void
    {
        $this->stage2TenderId = $stage2TenderId;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function setTitleEn(?string $titleEn): void
    {
        $this->titleEn = $titleEn;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function setDescriptionEn(?string $descriptionEn): void
    {
        $this->descriptionEn = $descriptionEn;
    }

    public function getComplaintPeriod()
    {
        return $this->complaintPeriod;
    }

    public function setComplaintPeriod($complaintPeriod): void
    {
        $this->complaintPeriod = $complaintPeriod;
    }

    public function getCriteria()
    {
        return $this->criteria;
    }

    public function setCriteria($criteria): void
    {
        $this->criteria = $criteria;
    }

    /**
     * @return bool|null
     */
    public function getHasMonitoring()
    {
        return $this->hasMonitoring;
    }

    public function setHasMonitoring(?bool $hasMonitoring): void
    {
        $this->hasMonitoring = $hasMonitoring;
    }

    public function getMonitoringDateModified(): ?\DateTimeInterface
    {
        return $this->monitoringDateModified;
    }

    public function setMonitoringDateModified(?\DateTimeInterface $monitoringDateModified): void
    {
        $this->monitoringDateModified = $monitoringDateModified;
    }

    public function getMilestonesCode()
    {
        if (!is_array($this->milestones)) {
            return [];
        }
        $items = [];
        foreach ($this->milestones as $milestone) {
            if (!is_array($milestone) || !array_key_exists('code', $milestone)) {
                continue;
            }
            $items[] = $milestone['code'];
        }

        return $items;
    }

    public function getProcurementMethodTypeFilter()
    {
        $filter = $this->procurementMethodType ?? '';
        $rationale = $this->procurementMethodRationale ?? '';
        if ('reporting' === $filter && 'catalogue' === substr($rationale, 0, 9)) {
            $rationale = 'catalogue';
        }
        if ('' !== $rationale) {
            $rationale = '.'.$rationale;
        }

        return $filter.$rationale;
    }

    /**
     * @return string|null
     */
    public function getTenderIDText()
    {
        return $this->tenderID;
    }

    /**
     * @return bool
     */
    public function getHasActiveMonitoring()
    {
        return $this->hasActiveMonitoring;
    }

    public function setHasActiveMonitoring(bool $hasActiveMonitoring): void
    {
        $this->hasActiveMonitoring = $hasActiveMonitoring;
    }

    public function getTenderConfig()
    {
        return $this->tenderConfig;
    }

    public function setTenderConfig($tenderConfig): void
    {
        $this->tenderConfig = $tenderConfig;
    }

    public function getDzoData(): DzoData
    {
        if (!$this->dzoData) {
            $this->dzoData = new DzoData();
        }

        return $this->dzoData;
    }

    public function setDzoData(DzoData $dzoData): void
    {
        $this->dzoData = $dzoData;
    }

    public function getFunders(): Collection|array
    {
        return $this->funders;
    }

    public function setFunders(Collection|array $funders): void
    {
        $this->funders = $funders;
    }

    public function addFunder(Organization $funder): void
    {
        if (!$this->funders->contains($funder)) {
            $this->funders->add($funder);
        }
    }

    public function getInspector(): ?Organization
    {
        return $this->inspector;
    }

    public function setInspector(?Organization $inspector): void
    {
        $this->inspector = $inspector;
    }

    public function getReviewRequests()
    {
        return $this->reviewRequests;
    }

    public function setReviewRequests($reviewRequests): void
    {
        $this->reviewRequests = $reviewRequests;
    }

    public function getOldId(): ?int
    {
        return $this->oldId;
    }

    public function setOldId(?int $oldId): Tender
    {
        $this->oldId = $oldId;
        return $this;
    }
}
