<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\ProfileManager;
use App\Resource\ProfileView;
use App\Service\CriteriaFilterService;

final readonly class CategoryProfilesDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly ProfileManager $profileManager,
        private CriteriaFilterService $criteriaFilterService,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $documents = $this->profileManager->findByCategory($uriVariables['id']);

        if (empty($documents)) {
            return null;
        }

        $views = [];
        foreach ($documents as $profile) {
            $views[] = new ProfileView(
                $profile,
                $this->criteriaFilterService,
            );
        }

        return $views;
    }
}
