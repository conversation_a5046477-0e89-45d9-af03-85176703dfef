<?php

namespace App\Manager;

use App\Document\Cart;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\LockException;
use Doctrine\ODM\MongoDB\Mapping\MappingException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

class CartManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    public function __construct(private readonly DocumentManager $documentManager)
    {
        $this->repository = $documentManager->getRepository(Cart::class);
    }

    /**
     * @return Cart|null
     *
     * @throws LockException
     * @throws MappingException
     */
    public function find($id)
    {
        return $this->getRepository()->find($id);
    }

    public function getUserCart(string $userId)
    {
        if (!$cart = $this->repository->findOneBy(['userId' => $userId])) {
            $cart = $this->createCart($userId);
        }

        return $cart;
    }

    public function createCart(string $userId): Cart
    {
        $cart = new Cart();
        $cart->setUserId($userId);
        $cart->setCreatedDate(new \DateTime());
        $this->repository->save($cart);

        return $cart;
    }

    public function save($document, $andFlush = true)
    {
        $this->getDocumentManager()->persist($document);
        if ($andFlush) {
            $this->getDocumentManager()->flush();
        }

        return $document;
    }
}
