<?php

namespace App\Document;

use App\Helper\RestrictedDataHelper;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Mapping\Annotations\Index;
use Doctrine\ODM\MongoDB\Types\Type;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[MongoDB\Document]
class Monitoring implements HasDateModifiedInterface
{
    use RestrictedDataHelper;

    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    #[Index]
    #[SerializedName('tender_id')]
    protected ?string $tenderId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    #[SerializedName('monitoring_id')]
    protected ?string $monitoringId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $reasons;

    #[MongoDB\Field(type: Type::RAW)]
    protected $procuringStages;

    #[MongoDB\Field(type: Type::RAW)]
    protected $monitoringPeriod;

    #[MongoDB\Field(type: Type::RAW)]
    protected $eliminationPeriod;

    #[MongoDB\Field(type: Type::RAW)]
    protected $eliminationReport;

    #[MongoDB\Field(type: Type::RAW)]
    protected $posts;

    #[MongoDB\Field(type: Type::RAW)]
    protected $parties;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[Index(order: 'desc')]
    protected $dateModified;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    protected $endDate;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[Index(order: Criteria::ASC)]
    protected $dateCreated;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    #[SerializedName('tender_owner')]
    protected ?string $tenderOwner = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $documents;

    #[MongoDB\Field(type: Type::RAW)]
    protected $decision;

    #[MongoDB\Field(type: Type::RAW)]
    protected $conclusion;

    #[MongoDB\Field(type: Type::RAW)]
    protected $cancellation;

    #[MongoDB\Field(type: Type::RAW)]
    protected $appeal;

    #[MongoDB\Field(type: Type::BOOL)]
    private ?bool $hasInspection = false;

    #[MongoDB\Field(type: Type::RAW)]
    private $restricted;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
    }

    /**
     * @return string|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getTenderId(): ?string
    {
        return $this->tenderId;
    }

    public function setTenderId(?string $tenderId): void
    {
        $this->tenderId = $tenderId;
    }

    public function getMonitoringId(): ?string
    {
        return $this->monitoringId;
    }

    public function setMonitoringId(?string $monitoringId): void
    {
        $this->monitoringId = $monitoringId;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getReasons()
    {
        return $this->reasons;
    }

    public function setReasons($reasons): void
    {
        $this->reasons = $reasons;
    }

    public function getProcuringStages()
    {
        return $this->procuringStages;
    }

    public function setProcuringStages($procuringStages): void
    {
        $this->procuringStages = $procuringStages;
    }

    public function getMonitoringPeriod()
    {
        return $this->monitoringPeriod;
    }

    public function setMonitoringPeriod($monitoringPeriod): void
    {
        $this->monitoringPeriod = $monitoringPeriod;
    }

    public function getEliminationPeriod()
    {
        return $this->eliminationPeriod;
    }

    public function setEliminationPeriod($eliminationPeriod): void
    {
        $this->eliminationPeriod = $eliminationPeriod;
    }

    public function getEliminationReport()
    {
        return $this->eliminationReport;
    }

    public function setEliminationReport($eliminationReport): void
    {
        $this->eliminationReport = $eliminationReport;
    }

    public function getPosts()
    {
        if ($this->restrictedMode && is_array($this->posts)) {
            $restrictedPosts = $this->posts;
            foreach ($restrictedPosts as $idx => &$post) {
                if (array_key_exists('documents', $post)) {
                    $post['documents'] = array_map(function ($document) {
                        return $this->restrictDocumentData($document);
                    }, $post['documents']);
                }

                $restrictedPosts[$idx] = array_merge($post, [
                    'title' => 'Приховано',
                    'description' => 'Приховано',
                ]);
            }

            return $restrictedPosts;
        }

        return $this->posts;
    }

    public function setPosts($posts): void
    {
        $this->posts = $posts;
    }

    public function getParties()
    {
        return $this->parties;
    }

    public function setParties($parties): void
    {
        $this->parties = $parties;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    /**
     * @throws \Exception
     */
    public function setDateModified($dateModified): void
    {
        if (is_string($dateModified)) {
            $dateModified = new \DateTime($dateModified);
        }
        $this->dateModified = $dateModified;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    /**
     * @throws \Exception
     */
    public function setEndDate($endDate): void
    {
        if (is_string($endDate)) {
            $endDate = new \DateTime($endDate);
        }
        $this->endDate = $endDate;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws \Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new \DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getTenderOwner(): ?string
    {
        return $this->tenderOwner;
    }

    public function setTenderOwner(?string $tenderOwner): void
    {
        $this->tenderOwner = $tenderOwner;
    }

    public function getDecision()
    {
        if ($this->restrictedMode && is_array($this->decision)) {
            $restrictedDecision = $this->decision;

            if (array_key_exists('documents', $restrictedDecision)) {
                $restrictedDecision['documents'] = array_map(function ($document) {
                    return $this->restrictDocumentData($document);
                }, $restrictedDecision['documents']);
            }

            return array_merge($restrictedDecision, [
                'description' => 'Приховано',
            ]);
        }

        return $this->decision;
    }

    public function setDecision($decision): void
    {
        $this->decision = $decision;
    }

    public function getConclusion()
    {
        if ($this->restrictedMode && is_array($this->conclusion)) {
            $restrictedConclusion = $this->conclusion;

            if (array_key_exists('documents', $restrictedConclusion)) {
                $restrictedConclusion['documents'] = array_map(function ($document) {
                    return $this->restrictDocumentData($document);
                }, $restrictedConclusion['documents']);
            }

            return array_merge($restrictedConclusion, [
                'description' => 'Приховано',
                'auditFinding' => 'Приховано',
                'stringsAttached' => 'Приховано',
            ]);
        }

        return $this->conclusion;
    }

    public function setConclusion($conclusion): void
    {
        $this->conclusion = $conclusion;
    }

    public function getAppeal()
    {
        return $this->appeal;
    }

    public function setAppeal($appeal): void
    {
        $this->appeal = $appeal;
    }

    /**
     * @return bool|null
     */
    public function getHasInspection()
    {
        return $this->hasInspection;
    }

    public function setHasInspection(?bool $hasInspection): void
    {
        $this->hasInspection = $hasInspection;
    }

    public function getDocuments()
    {
        if ($this->restrictedMode && is_array($this->documents)) {
            return array_map(function ($document) {
                return $this->restrictDocumentData($document);
            }, $this->documents);
        }

        return $this->documents;
    }

    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function getCancellation()
    {
        if ($this->restrictedMode && is_array($this->cancellation)) {
            return array_merge($this->cancellation, [
                'description' => 'Приховано',
            ]);
        }

        return $this->cancellation;
    }

    public function setCancellation($cancellation): void
    {
        $this->cancellation = $cancellation;
    }

    public function getRestricted()
    {
        return $this->restricted;
    }

    public function setRestricted($restricted): void
    {
        $this->restricted = $restricted;
    }
}
