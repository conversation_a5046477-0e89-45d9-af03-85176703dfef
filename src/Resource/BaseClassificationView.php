<?php

namespace App\Resource;

use App\Document\BaseClassification;

/**
 * Class BaseClassificationView.
 */
readonly class BaseClassificationView
{
    public ?string $id;

    public ?string $description;

    public ?string $scheme;

    public ?string $uri;

    /**
     * BaseClassificationView constructor.
     */
    public function __construct(BaseClassification $baseClassification)
    {
        $this->id = $baseClassification->getId();
        $this->description = $baseClassification->getDescription();
        $this->scheme = $baseClassification->getScheme();
        $this->uri = $baseClassification->getUri();
    }
}
