<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\ItemProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Document\Classification;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;

#[ApiResource(
    shortName: 'classification',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_classification')),
        new Get(provider: ItemProvider::class, stateOptions: new Options(index: 'dzo_classification')),
    ],
    order: ['id' => 'DESC'],
    paginationItemsPerPage: 10,
    paginationType: 'page'
)]
#[ApiFilter(MatchFilter::class, properties: ['scheme' => 'exact', 'searchId' => 'exact', 'parentId' => 'exact'])]
#[ApiFilter(KeywordFilter::class, properties: ['id', 'description', 'descriptionEn', 'descriptionRu', 'name', 'nameEn', 'nameRu'])]
readonly class ClassificationView extends BaseClassificationView
{
    /**
     * We need this special property, because by default API Platform generates get url using $id property
     * And here $id property can contain wrong characters. Also we don't need single item get for classification.
     * So instead we should provide $iri variable.
     */
    #[ApiProperty(identifier: true)]
    public string $iri;

    #[ApiProperty(identifier: false)]
    public ?string $id;

    public ?string $descriptionEn;

    public ?string $name;

    public ?string $nameEn;

    public string $parentId;

    public string $searchId;

    public int $treeLevel;

    public bool $selectable;

    public bool $children;

    public function __construct(Classification $classification)
    {
        parent::__construct($classification);

        $this->iri = $classification->getIri();
        $this->nameEn = $classification->getNameEn();
        $this->parentId = $classification->getParentId();
        $this->searchId = $classification->getSearchId();
        $this->treeLevel = $classification->getTreeLevel();
        $this->selectable = $classification->getSelectable();
        $this->children = $classification->getChildren();
    }
}
