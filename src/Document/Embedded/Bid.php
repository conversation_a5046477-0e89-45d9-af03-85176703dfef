<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class Bid
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $apiId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?int $pay = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?int $signed = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $userId = null;

    #[MongoDB\Field(type: Type::FLOAT, nullable: true)]
    private ?float $value = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $currency = null;

    #[MongoD<PERSON>\Field(type: Type::BOOL)]
    private bool $valueAddedTax = false;

    /**
     * @param string|null $id
     * @param string|null $apiId
     * @param int|null $pay
     * @param int|null $signed
     * @param string|null $userId
     */
    public function __construct(?string $id, ?string $apiId, ?int $pay, ?int $signed, ?string $userId, ?float $value, ?string $currency, bool $valueAddedTax = false)
    {
        $this->id = $id;
        $this->apiId = $apiId;
        $this->pay = $pay;
        $this->signed = $signed;
        $this->userId = $userId;
        $this->value = $value;
        $this->currency = $currency;
        $this->valueAddedTax = $valueAddedTax;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): Bid
    {
        $this->id = $id;
        return $this;
    }

    public function getApiId(): ?string
    {
        return $this->apiId;
    }

    public function setApiId(?string $apiId): Bid
    {
        $this->apiId = $apiId;
        return $this;
    }

    public function getPay(): ?int
    {
        return $this->pay;
    }

    public function setPay(?int $pay): Bid
    {
        $this->pay = $pay;
        return $this;
    }

    public function getSigned(): ?int
    {
        return $this->signed;
    }

    public function setSigned(?int $signed): Bid
    {
        $this->signed = $signed;
        return $this;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function setUserId(?string $userId): Bid
    {
        $this->userId = $userId;
        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): Bid
    {
        $this->value = $value;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): Bid
    {
        $this->currency = $currency;
        return $this;
    }

    public function getValueAddedTax(): ?bool
    {
        return $this->valueAddedTax;
    }

    public function setValueAddedTax(?bool $valueAddedTax): Bid
    {
        $this->valueAddedTax = $valueAddedTax;
        return $this;
    }
}
