<?php

namespace App\Command;

use League\Flysystem\FilesystemOperator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:sync:name-funders-list',
    description: 'Sync list of funders',
)]
class SyncTenderFundersCommand extends Command
{
    public function __construct(
        private readonly string $prozorroFundersListLink,
        protected readonly FilesystemOperator $fundersStorage,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $fundersList = file_get_contents($this->prozorroFundersListLink);

        $this->fundersStorage->write('funders.json', $fundersList);

        return Command::SUCCESS;
    }
}
