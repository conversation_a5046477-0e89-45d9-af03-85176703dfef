<?php

namespace App\MessageHandler;

use App\Document\Product;
use App\Document\Profile;
use App\Document\Category;
use App\Manager\ProfileManager;
use App\Message\MarketUpdateVisibilityMessage;
use Doctrine\ODM\MongoDB\DocumentManager;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use App\Service\CriteriaFilterService;

#[AsMessageHandler]
final class MarketUpdateVisibilityHandler
{
    public function __construct(
        private DocumentManager $documentManager,
        private ProfileManager $profileManager,
        private LoggerInterface $logger,
        private ObjectPersisterInterface $productPersister,
        private ObjectPersisterInterface $profilePersister,
        private CriteriaFilterService $criteriaFilterService,
    ) {
    }

    public function __invoke(MarketUpdateVisibilityMessage $message): void
    {
        try {
            if (trim($message->type) === 'product') {
                $this->processProduct($message->id);
            } elseif (trim($message->type) === 'profile') {
                $this->processProfile($message->id);
            } else {
                $this->logger->error('Unknown document type: ' . $message->type);
            }
        } catch (\Throwable $e) {
            $this->logger->error('Error processing document: ' . $e->getMessage(), [
                'document_id' => $message->id,
                'document_type' => $message->type,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    private function processProduct(string $productId): void
    {
        $product = $this->documentManager->getRepository(Product::class)->find($productId);
        
        if (!$product) {
            $this->logger->warning('Product not found: ' . $productId);
            return;
        }
        
        $categoryId = $product->getRelatedCategoryId();
        if (!$categoryId) {
            $this->logger->info('Product has no related category: ' . $productId);
            return;
        }
        
        $category = $this->documentManager->getRepository(Category::class)->find($categoryId);
        if (!$category) {
            $this->logger->warning('Related category not found for product: ' . $productId);
            return;
        }
        
        $product->setRelatedCategoryStatus($category->getStatus());

        $profiles = [];
        if (!empty($product->getRelatedProfileIds())) {
            $profiles = $this->profileManager->findByIds($product->getRelatedProfileIds());
        }
        $product->setRelatedCategoryStatus($category->getStatus());

        $isTypesValid = !$this->criteriaFilterService->hasIncompatibleProductTypes($product, $category, $profiles);
        
        $product->setDzoCategoryDataTypesMatch($isTypesValid);
        $this->documentManager->persist($product);
        $this->documentManager->flush();
        
        try {
            $this->productPersister->replaceOne($product);
        } catch (\Throwable $e) {
            $this->logger->error('Failed to update product in Elasticsearch: ' . $e->getMessage(), [
                'product_id' => $productId,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        $this->documentManager->clear();
    }
    
    private function processProfile(string $profileId): void
    {
        $profile = $this->documentManager->getRepository(Profile::class)->find($profileId);
        
        if (!$profile) {
            $this->logger->warning('Profile not found: ' . $profileId);
            return;
        }
        
        $categoryId = $profile->getRelatedCategoryId();
        if (!$categoryId) {
            $this->logger->info('Profile has no related category: ' . $profileId);
            return;
        }
        
        $category = $this->documentManager->getRepository(Category::class)->find($categoryId);
        if (!$category) {
            $this->logger->warning('Related category not found for profile: ' . $profileId);
            return;
        }
        
        $profile->setRelatedCategoryStatus($category->getStatus());
        $this->documentManager->persist($profile);
        $this->documentManager->flush();
        
        try {
            $this->profilePersister->replaceOne($profile);
        } catch (\Throwable $e) {
            $this->logger->error('Failed to update profile in Elasticsearch: ' . $e->getMessage(), [
                'profile_id' => $profileId,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        $this->documentManager->clear();
    }
}
