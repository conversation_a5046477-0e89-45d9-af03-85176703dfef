<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\ProcuringEntity;
use App\Document\Embedded\ProcuringEntityPlan;
use App\Resource\BaseIdentifierView;

/**
 * Class ProcuringEntityView.
 */
class ProcuringEntityView
{
    private ?string $id = null;

    private ?string $name = null;

    private ?BaseIdentifierView $identifier = null;

    private $additionalIdentifiers;

    private $address;

    private $contactPoint;

    private ?string $kind = null;

    /**
     * ProcuringEntityPlanView constructor.
     *
     * @param ProcuringEntityPlan|ProcuringEntity $mongoDocument
     */
    public function __construct($mongoDocument, bool $toArray = false)
    {
        $this->id = $mongoDocument->getId();
        $this->name = $mongoDocument->getName();

        $identifier = $mongoDocument->getIdentifier();
        if ($identifier && !$toArray) {
            $this->identifier = new BaseIdentifierView($identifier);
        } elseif ($identifier && $toArray) {
            $this->identifier = (array) new BaseIdentifierView($identifier);
        }

        $this->additionalIdentifiers = $mongoDocument->getAdditionalIdentifiers();
        $this->address = $mongoDocument->getAddress();
        $this->contactPoint = $mongoDocument->getContactPoint();
        $this->kind = $mongoDocument->getKind();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getIdentifier(): ?BaseIdentifierView
    {
        return $this->identifier;
    }

    public function getAdditionalIdentifiers()
    {
        return $this->additionalIdentifiers;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function getContactPoint()
    {
        return $this->contactPoint;
    }

    public function getKind(): ?string
    {
        return $this->kind;
    }
}
