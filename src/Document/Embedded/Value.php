<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Value
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $currency = null;

    #[MongoDB\Field(type: Type::FLOAT, nullable: true)]
    private ?float $amount = null;

    #[MongoDB\Field(type: Type::BOOL)]
    private ?bool $valueAddedTaxIncluded = true;

    public function __toString(): string
    {
        return (string) $this->amount.' '.$this->currency;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): void
    {
        $this->currency = $currency;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    /**
     * @param float|null $amount
     */
    public function setAmount($amount = null): void
    {
        $this->amount = $amount ? floatval($amount) : $amount;
    }

    public function getValueAddedTaxIncluded(): ?bool
    {
        return $this->valueAddedTaxIncluded;
    }

    public function setValueAddedTaxIncluded(?bool $valueAddedTaxIncluded): void
    {
        $this->valueAddedTaxIncluded = $valueAddedTaxIncluded;
    }
}
