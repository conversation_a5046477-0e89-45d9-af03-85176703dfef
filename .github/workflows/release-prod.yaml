name: Prod release pipeline

on:
  release:
    types: [released]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          ref: ${{ github.ref }}

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push Docker image
        uses: docker/build-push-action@v2
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: 206424851631.dkr.ecr.eu-central-1.amazonaws.com/dzo-search:${{ github.sha }}

  deploy:
    needs: [build]
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          ref: ${{ github.ref }}

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.12.0

      - name: kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-production --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Login to Amazon ECR
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          aws ecr get-login-password | helm registry login --username AWS --password-stdin 206424851631.dkr.ecr.eu-central-1.amazonaws.com

      - name: Deploy chart
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          helm upgrade --install search oci://206424851631.dkr.ecr.eu-central-1.amazonaws.com/helm-charts --version 1.2.0-dzo --namespace prod \
          --set image.repository=206424851631.dkr.ecr.eu-central-1.amazonaws.com/dzo-search \
          --set image.tag=${{ github.sha }} \
          -f deploy/prod.yaml

  workers:
    needs: [build]
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          ref: ${{ github.ref }}

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.12.0

      - name: kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          aws eks update-kubeconfig --name dzo-production --region eu-central-1 --kubeconfig $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Login to Amazon ECR
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          aws ecr get-login-password | helm registry login --username AWS --password-stdin 206424851631.dkr.ecr.eu-central-1.amazonaws.com

      - name: Deploy chart
        run: |
          export HELM_EXPERIMENTAL_OCI=1
          helm upgrade --install dzo-workers oci://206424851631.dkr.ecr.eu-central-1.amazonaws.com/helm-charts --version 1.4.4-dzo-workers --namespace prod \
          --set phpWorkers.image.tag=${{ github.sha }} \
          -f deploy/workers-prod.yaml

