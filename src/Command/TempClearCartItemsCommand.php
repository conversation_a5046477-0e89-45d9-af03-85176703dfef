<?php

namespace App\Command;

use App\Document\CartItem;
use App\Document\Profile;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:cart-items:clear',
    description: 'Remove cartItems with general or hidden profiles',
)]
class TempClearCartItemsCommand extends Command
{
    public function __construct(
        private readonly DocumentManager $documentManager,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $profileRepository = $this->documentManager->getRepository(Profile::class);
        $cartItems = $this->documentManager->createQueryBuilder(CartItem::class)
            ->field('profileId')->notEqual(null)->getQuery()->execute();
        if (count($cartItems) > 0) {
            foreach ($cartItems as $cartItem) {
                $profile = $profileRepository->find($cartItem->getProfileId());
                if ($profile->getStatus() != 'active') {
                    $this->documentManager->remove($cartItem);
                }
            }
            $this->documentManager->flush();
        }

        $output->writeln("All not active cartItems have been removed.");

        return Command::SUCCESS;
    }
}