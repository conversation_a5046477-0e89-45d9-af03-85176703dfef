<?php

namespace App\Repository;

use App\Document\Filter;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class FilterRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Filter::class);
    }

    public function save(Filter $filter)
    {
        $this->dm->persist($filter);
        $this->dm->flush();
    }

    public function remove(Filter $filter)
    {
        $this->dm->remove($filter);
        $this->dm->flush();
    }
}
