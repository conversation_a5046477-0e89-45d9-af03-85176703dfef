<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\InspectionDataProvider;
use App\Document\Inspection;
use App\Filter\MatchFilter;
use App\Filter\OrderFilter;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ApiResource(
    shortName: 'inspection',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_inspection')),
        new Get(provider: InspectionDataProvider::class),
    ],
)]
#[ApiFilter(OrderFilter::class, properties: ['dateModified'], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(MatchFilter::class, properties: ['monitoringIds' => 'exact'])]
readonly class InspectionView
{
    public ?string $id;

    /**
     * @SerializedName("inspection_id")
     */
    public ?string $inspectionId;

    /**
     * @var string[]
     *
     * @SerializedName("monitoring_ids")
     */
    public array $monitoringIds;

    public ?string $description;

    public mixed $documents;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    public function __construct(Inspection $inspection)
    {
        if ($inspection->getRestricted()) {
            $inspection->setRestrictedMode(true);
        }

        $this->id = $inspection->getId();
        $this->inspectionId = $inspection->getInspectionId();
        $this->monitoringIds = $inspection->getMonitoringIds();
        $this->description = $inspection->getDescription();
        $this->documents = $inspection->getDocuments();
        $this->dateCreated = $inspection->getDateCreated();
        $this->dateModified = $inspection->getDateModified();
    }
}
