<?php

namespace App\Uuid;

class Uuid
{
    /**
     * Generates UUID v4.Code is taken from vendor/doctrine/mongodb-odm/lib/Doctrine/ODM/MongoDB/Id/UuidGenerator.php
     * method generateV4().
     *
     * @return string
     *
     * @throws \Exception
     */
    public static function v4()
    {
        return sprintf(
            '%04x%04x%04x%04x%04x%04x%04x%04x',
            // 32 bits for "time_low"
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
            // 16 bits for "time_mid"
            random_int(0, 0xFFFF),
            // 16 bits for "time_hi_and_version",
            // four most significant bits holds version number 4
            random_int(0, 0x0FFF) | 0x4000,
            // 16 bits, 8 bits for "clk_seq_hi_res",
            // 8 bits for "clk_seq_low",
            // two most significant bits holds zero and one for variant DCE1.1
            random_int(0, 0x3FFF) | 0x8000,
            // 48 bits for "node"
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
        );
    }
}
