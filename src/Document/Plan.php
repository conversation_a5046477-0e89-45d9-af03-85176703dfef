<?php

namespace App\Document;

use App\Document\Embedded\Budget;
use App\Document\Embedded\Document;
use App\Document\Embedded\EmbeddedClassification;
use App\Document\Embedded\Item;
use App\Document\Embedded\ProcuringEntity;
use App\Document\Embedded\Project;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[MongoDB\Document]
class Plan implements HasDateModifiedInterface
{
    #[MongoDB\Id(strategy: 'NONE')]
    protected $id;

    #[MongoDB\Field(type: Type::BOOL, nullable: true)]
    #[SerializedName('is_masked')]
    protected ?bool $isMasked = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $data;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $mode = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    /**
     * @var Collection|Item[]
     */
    #[MongoDB\EmbedMany(targetDocument: Item::class)]
    protected Collection|array $items;

    #[MongoDB\Field(type: Type::RAW)]
    protected $cancellation;

    #[MongoDB\Field(type: Type::RAW)]
    protected $revisions;

    #[MongoDB\Field(type: Type::RAW)]
    protected $buyers;

    #[MongoDB\Field(type: Type::RAW)]
    protected $milestones;

    /**
     * @var Collection|Document[]
     */
    #[MongoDB\EmbedMany(targetDocument: Document::class)]
    protected Collection|array $documents;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedClassification::class)]
    protected $classification;

    #[MongoDB\Field(name: 'planID', type: Type::STRING, nullable: true)]
    #[SerializedName('planID')]
    protected ?string $planId = null;

    #[MongoDB\EmbedOne(targetDocument: Budget::class)]
    protected ?Budget $budget = null;

    #[MongoDB\EmbedOne(targetDocument: Project::class)]
    protected ?Project $project = null;

    #[MongoDB\Field(type: Type::DATE)]
    protected $datePublished;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $owner = null;

    /**
     * @var Collection|EmbeddedClassification[]
     */
    #[MongoDB\EmbedMany(targetDocument: EmbeddedClassification::class)]
    protected Collection|array $additionalClassifications;

    #[MongoDB\EmbedOne(targetDocument: ProcuringEntity::class)]
    protected ?ProcuringEntity $procuringEntity = null;

    #[MongoDB\Field(type: Type::RAW)]
    protected $tender;

    #[MongoDB\Field(type: Type::DATE)]
    #[MongoDB\Index(name: 'dateModifiedIdx', order: Criteria::DESC)]
    protected ?\DateTimeInterface $dateModified = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\Field(name: 'tender_id', type: Type::STRING, nullable: true)]
    #[SerializedName('tender_id')]
    protected ?string $tenderId = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private ?int $oldId = null;

    /**
     * Plan constructor.
     */
    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->items = new ArrayCollection();
        $this->additionalClassifications = new ArrayCollection();
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getIsMasked(): ?bool
    {
        return $this->isMasked;
    }

    public function setIsMasked(?bool $isMasked): void
    {
        $this->isMasked = $isMasked;
    }

    public function getMode(): ?string
    {
        return $this->mode;
    }

    public function setMode(?string $mode): void
    {
        $this->mode = $mode;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setData($data): void
    {
        $this->data = $data;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getBuyers()
    {
        return $this->buyers;
    }

    public function setBuyers($buyers): void
    {
        $this->buyers = $buyers;
    }

    /**
     * @return Document[]|Collection
     */
    public function getDocuments()
    {
        return $this->documents;
    }

    /**
     * @param Document[]|Collection $documents
     */
    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function getClassification()
    {
        return $this->classification;
    }

    public function setClassification($classification): void
    {
        $this->classification = $classification;
    }

    public function getPlanId(): ?string
    {
        return $this->planId;
    }

    public function setPlanId(?string $planId): void
    {
        $this->planId = $planId;
    }

    public function getBudget(): ?Budget
    {
        return $this->budget;
    }

    public function setBudget(?Budget $budget = null): self
    {
        $this->budget = $budget;

        return $this;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project = null): self
    {
        $this->project = $project;

        return $this;
    }

    public function getDatePublished()
    {
        return $this->datePublished;
    }

    /**
     * @throws \Exception
     */
    public function setDatePublished($datePublished): void
    {
        if (is_string($datePublished)) {
            $datePublished = new \DateTime($datePublished);
        }
        $this->datePublished = $datePublished;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): void
    {
        $this->owner = $owner;
    }

    public function getAdditionalClassifications()
    {
        return $this->additionalClassifications;
    }

    public function setAdditionalClassifications($additionalClassifications): void
    {
        $this->additionalClassifications = $additionalClassifications;
    }

    public function getProcuringEntity()
    {
        return $this->procuringEntity;
    }

    public function setProcuringEntity($procuringEntity): void
    {
        $this->procuringEntity = $procuringEntity;
    }

    public function getTender()
    {
        return $this->tender;
    }

    public function setTender($tender): void
    {
        $this->tender = $tender;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    /**
     * @throws \Exception
     */
    public function setDateModified($dateModified): void
    {
        if (is_string($dateModified)) {
            $dateModified = new \DateTime($dateModified);
        }
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    /**
     * @throws \Exception
     */
    public function setDateCreated($dateCreated): void
    {
        if (is_string($dateCreated)) {
            $dateCreated = new \DateTime($dateCreated);
        }
        $this->dateCreated = $dateCreated;
    }

    public function getTenderId(): ?string
    {
        return $this->tenderId;
    }

    public function setTenderId(?string $tenderId): void
    {
        $this->tenderId = $tenderId;
    }

    public function getItems()
    {
        return $this->items;
    }

    public function setItems($items): void
    {
        $this->items = $items;
    }

    public function getCancellation()
    {
        return $this->cancellation;
    }

    public function setCancellation($cancellation): void
    {
        $this->cancellation = $cancellation;
    }

    public function getRevisions()
    {
        return $this->revisions;
    }

    public function setRevisions($revisions): void
    {
        $this->revisions = $revisions;
    }

    public function getMilestones()
    {
        return $this->milestones;
    }

    public function setMilestones($milestones): void
    {
        $this->milestones = $milestones;
    }

    public function getTenderPeriodDate()
    {
        if (array_key_exists('tenderPeriod', $this->tender) && is_array($this->tender['tenderPeriod'])) {
            $tenderPeriod = $this->tender['tenderPeriod'];
            if (array_key_exists('startDate', $tenderPeriod) && (false !== strtotime($tenderPeriod['startDate']))) {
                return $tenderPeriod['startDate'];
            }
        }

        return null;
    }

    /**
     * @return string|null
     */
    public function getPlanIdText()
    {
        return $this->planId;
    }

    public function getOldId(): ?int
    {
        return $this->oldId;
    }

    public function setOldId(?int $oldId): Plan
    {
        $this->oldId = $oldId;
        return $this;
    }
}
