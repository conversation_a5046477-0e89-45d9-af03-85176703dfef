<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\CategoryDataProvider;
use App\DataProvider\CategoryProfilesDataProvider;
use App\Document\Agreement;
use App\Document\Category;
use App\Filter\ClassificationFilter;
use App\Filter\KeywordFilter;
use App\Filter\OrderFilter;
use App\Resource\Embedded\AgreementView;
use App\Resource\Embedded\ImageView;
use App\Resource\Embedded\ProcuringEntityView;
use Doctrine\Common\Collections\Criteria;
use App\Service\CriteriaFilterService;

#[ApiResource(
    shortName: 'category',
    operations: [
        new GetCollection(normalizationContext: ['groups' => 'category:list'],
            provider: CollectionProvider::class,
            stateOptions: new Options(index: 'dzo_category')),
        new Get(normalizationContext: ['groups' => 'category:read'],
            provider: CategoryDataProvider::class,
            stateOptions: new Options(index: 'dzo_category')),
        new Get(
            uriTemplate: '/categories/{id}/profiles',
            paginationEnabled: false,
            normalizationContext: ['groups' => 'profile:links'],
            provider: CategoryProfilesDataProvider::class,
        ),
    ],
    order: ['classification.id' => Criteria::ASC],
    paginationItemsPerPage: 10,
    paginationType: 'page'
)]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification' => 'classification.id',
    'additionalClassification' => 'additionalClassifications.id',
])]
#[ApiFilter(OrderFilter::class, properties: [
    'id',
    'prozorroId',
    'dateCreated',
    'title',
    'classification.id',
    'popularity'
], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(KeywordFilter::class, properties: [
    'id',
    'prozorroId',
    'description',
    'title.title',
])]
readonly class CategoryView
{
    #[ApiProperty(identifier: true)]
    public ?string $id;

    public ?string $prozorroId;

    public ?ProcuringEntityView $procuringEntity;

    public ?string $status;

    public ?string $title;

    public ?string $description;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    /** @var ImageView[] */
    public array $images;

    public MarketClassificationView $classification;

    public array $products;

    public ?array $criteria;

    public ?array $additionalClassifications;

    public ?array $profiles;

    public ?AgreementView $agreement;

    public function __construct(
        Category $category,
        private CriteriaFilterService $criteriaFilterService,
        array $productViews = [],
        ?string $awsCdnUrl = null,
        ?array $categoryProfiles = [],
        ?Agreement $agreement = null,
    )
    {
        $this->id = $category->getId();
        $this->prozorroId = $category->getProzorroId();
        $procuringEntity = $category->getProcuringEntity();
        $this->procuringEntity = ($category->getProcuringEntity()) ? new ProcuringEntityView($procuringEntity) : null;
        $this->status = $category->getStatus();
        $this->title = $category->getTitle();
        $this->description = $category->getDescription();
        $this->dateModified = $category->getDateModified();
        $this->dateCreated = $category->getDateCreated();
        $this->products = $productViews;
        $criteria = $this->criteriaFilterService->filterArchivedRequirements($category->getCriteria());
        $criteria = $this->criteriaFilterService->addLocalizationCriteria($criteria);
        $this->criteria = $criteria;
        $this->additionalClassifications = $category->getAdditionalClassifications();

        $profiles = [];
        foreach ($categoryProfiles as $categoryProfile) {
            $profiles[] = new ProfileView(
                profile: $categoryProfile,
                criteriaFilterService: $this->criteriaFilterService,
            );
        }
        $this->profiles = [...$profiles];

        $images = [];
        foreach ($category->getImages() as $image) {
            $images[] = new ImageView($image, $awsCdnUrl);
        }
        $this->images = [...$images];
        $this->classification = new MarketClassificationView($category->getClassification());

        if ($agreement) {
            $this->agreement = new AgreementView($agreement);
        }
    }
}
