<?php

namespace App\Command;

use App\Document\Embedded\Bid;
use App\Document\Tender;
use App\Legacy\Repository\TenderBidsRepository;
use App\Repository\TenderRepository;
use App\Service\BidService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:bids:sync',
    description: 'Get bids from old dzo',
)]
class SyncBidsCommand extends Command
{
    private const CHUNK = 100;

    public function __construct(
        private readonly TenderBidsRepository $tenderBidsRepository,
        private readonly TenderRepository $tenderRepository,
        private readonly BidService $bidService,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function configure(): void
    {
        $this->addArgument('start', InputArgument::REQUIRED, 'Id');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $start = $input->getArgument('start');

        do {
            $bids = $this->tenderBidsRepository->getBids($start, self::CHUNK);
            foreach ($bids as $key => $item) {
                if ($tender = $this->tenderRepository->find($item['tender_api_id'])) {
                    $this->bidService->addBidToTender($tender, $item);
                }

                if ($key === array_key_last($bids)) {
                    $start = $item['id'];
                    $output->writeln("Last item in chunk is " . $start);
                }
            }
        } while (count($bids) >= self::CHUNK);

        return Command::SUCCESS;
    }
}
