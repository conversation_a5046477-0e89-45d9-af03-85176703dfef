<?php

namespace App\Classification;

use App\Document\Classification;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class GmdnParser.
 */
class GmdnParser extends ClassificationParser
{
    protected string $scheme = 'gmdn';

    /**
     * @throws GuzzleException
     * @throws \Doctrine\ODM\MongoDB\LockException
     * @throws \Doctrine\ODM\MongoDB\Mapping\MappingException
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    public function parse(array $urls): void
    {
        $url = $urls[0];

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Starting with '.$this->scheme);
        }
        try {
            $io = $this->inputOutput;
            if (null !== $io) {
                $io->progressStart();
            }// @todo progress file download
            $response = $this->client->request(
                'GET',
                $url, [
                    'progress' => function ($dl_total_size, $dl_size_so_far, $ul_total_size, $ul_size_so_far) use ($io) {
                        if (null !== $io) {
                            $io->progressAdvance(1);
                        }
                    },
                ]);
        } catch (GuzzleException $exception) {
            $this->logger->error($exception->getMessage(), [$exception]);
            throw $exception;
        }

        $result = json_decode((string) $response->getBody(), true);
        $totalDocuments = count($result);

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment($totalDocuments.' items found...');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsCount = 0;
        $parentClassifications = [];

        foreach ($result as $id => $descriptions) {
            $id = strval($id);

            $topParentDescription = substr_replace($id, 'xxx', -3);
            $topParentId = $this->scheme.$topParentDescription;
            $parentDescription = substr_replace($id, 'x', -1);
            $parentId = $this->scheme.$parentDescription;

            $this->buildParentClassification($topParentId, $topParentDescription, '', $parentClassifications);
            $this->buildParentClassification($parentId, $parentDescription, $topParentId, $parentClassifications);

            $document = new Classification();
            $document->setScheme($this->scheme);
            $document->setId($id);
            $document->setName($descriptions['name_uk']);
            $document->setNameEn($descriptions['name_en']);
            $document->setDescription($descriptions['description_uk']);
            $document->setDescriptionEn($descriptions['description_uk']);
            $document->setParentId($parentId);
            $document->setSelectable(true);
            $document->setTreeLevel(2);

            ++$documentsCount;

            $needFlush = (0 === $documentsCount % 50) || ($documentsCount === $totalDocuments);
            $this->manager->save($document, $needFlush);

            if (null !== $this->inputOutput) {
                $this->inputOutput->progressAdvance(1);
            }
        }
    }
}
