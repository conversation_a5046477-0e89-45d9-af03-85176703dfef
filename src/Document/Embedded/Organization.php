<?php

namespace App\Document\Embedded;

use App\Helper\RestrictedDataHelper;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Organization
{
    use RestrictedDataHelper;

    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $name = null;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedIdentifier::class)]
    private ?EmbeddedIdentifier $identifier = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $additionalIdentifiers;

    #[MongoDB\Field(type: Type::RAW)]
    private $address;

    #[MongoDB\Field(type: Type::RAW)]
    private $contactPoint;

    #[MongoDB\Field(type: Type::RAW)]
    private $additionalContactPoint;

    #[MongoDB\Field(type: Type::RAW)]
    private $scale;

    #[MongoDB\Field(type: Type::RAW)]
    private $signerInfo;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
        if (null !== $this->identifier) {
            $this->identifier->setRestrictedMode($restrictedMode);
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getIdentifier(): ?EmbeddedIdentifier
    {
        return $this->identifier;
    }

    /**
     * @param mixed $identifier
     */
    public function setIdentifier(?EmbeddedIdentifier $identifier = null): void
    {
        $this->identifier = $identifier;
    }

    public function getAdditionalIdentifiers()
    {
        return $this->additionalIdentifiers;
    }

    public function setAdditionalIdentifiers($additionalIdentifiers): void
    {
        $this->additionalIdentifiers = $additionalIdentifiers;
    }

    public function getAddress()
    {
        if ($this->restrictedMode && is_array($this->address)) {
            return $this->restrictAddressData($this->address);
        }

        return $this->address;
    }

    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getContactPoint()
    {
        if ($this->restrictedMode && is_array($this->contactPoint)) {
            return $this->restrictContactPointData($this->contactPoint);
        }

        return $this->contactPoint;
    }

    public function setContactPoint($contactPoint): void
    {
        $this->contactPoint = $contactPoint;
    }

    public function getAdditionalContactPoint()
    {
        return $this->additionalContactPoint;
    }

    public function setAdditionalContactPoint($additionalContactPoint): void
    {
        $this->additionalContactPoint = $additionalContactPoint;
    }

    public function getScale()
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->scale;
    }

    public function setScale($scale): void
    {
        $this->scale = $scale;
    }

    public function getSignerInfo()
    {
        if ($this->restrictedMode && is_array($this->signerInfo)) {
            return $this->restrictSignerInfoData($this->signerInfo);
        }

        return $this->signerInfo;
    }

    public function setSignerInfo($signerInfo): void
    {
        $this->signerInfo = $signerInfo;
    }
}
