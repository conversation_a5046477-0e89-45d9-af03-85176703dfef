<?php

namespace App\Manager;

use App\Document\Classification;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

/**
 * Class ClassificationManager.
 */
class ClassificationManager extends AbstractManager
{
    use ManagerTrait;

    /** @var DocumentRepository */
    private $repository;

    public function getRepository(): DocumentRepository
    {
        return $this->repository;
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    /**
     * ClassificationManager constructor.
     */
    public function __construct(private readonly DocumentManager $documentManager)
    {
        $this->repository = $documentManager->getRepository(Classification::class);
    }
}
