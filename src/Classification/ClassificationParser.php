<?php

namespace App\Classification;

use App\Document\Classification;
use App\Manager\ClassificationManager;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Class ClassificationParser.
 */
abstract class ClassificationParser
{
    protected string $scheme = '';

    public function __construct(
        protected LoggerInterface $apiLogger,
        protected ClassificationManager $manager,
        protected ?SymfonyStyle $inputOutput,
        protected Client $client = new Client()
    ) {
    }

    /**
     * @throws GuzzleException
     * @throws \Doctrine\ODM\MongoDB\LockException
     * @throws \Doctrine\ODM\MongoDB\Mapping\MappingException
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    abstract public function parse(array $urls): void;

    /**
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    protected function buildParentClassification(string $id, string $description, string $parentId, array $parentClassifications): void
    {
        if (array_key_exists($id, $parentClassifications)) {
            return;
        }

        $document = new Classification();
        $document->setScheme($this->scheme);
        $document->setParentId($parentId);
        $document->setTreeLevel(('' === $parentId) ? 0 : 1);
        $document->setId($id);
        $document->setDescription($description);
        $document->setChildren(true);

        $this->manager->save($document, false);
        $parentClassifications[$id] = $document;
    }
}
