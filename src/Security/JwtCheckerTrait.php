<?php

namespace App\Security;

use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Contracts\Service\Attribute\Required;

trait JwtCheckerTrait
{
    private readonly TokenStorageInterface $tokenStorage;

    private readonly JWTTokenManagerInterface $jwtManager;

    #[Required]
    public function setTokenStorage(TokenStorageInterface $tokenStorage): void
    {
        $this->tokenStorage = $tokenStorage;
    }

    #[Required]
    public function setTokenManager(JWTTokenManagerInterface $jwtManager): void
    {
        $this->jwtManager = $jwtManager;
    }

    public function checkJwt(): ?string
    {
        if (!$token = $this->tokenStorage->getToken()) {
            return null;
        }
        $decodedJwtToken = $this->jwtManager->decode($token);

        return $decodedJwtToken['id'];
    }
}
