<?php

namespace App\Resource\Embedded;

use App\Document\CartItem;
use App\Document\Embedded\DzoData;
use MongoDB\Collection;

class DzoDataView
{
    public Collection|array $bids;

    public function __construct(DzoData $dzoData)
    {
        $bids = [];
        foreach ($dzoData->getBids() as $bid) {
            $bids[] = new DzoDataBidView($bid);
        }
        $this->bids = [...$bids];
    }
}
