<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\CartManager;
use App\Resource\CartView;
use App\Security\JwtCheckerTrait;
use App\Service\CartItemService;

final readonly class CartData<PERSON>rovider implements ProviderInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private CartManager $cartManager,
        private CartItemService $cartItemService
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        if (!$userId = $this->checkJwt()) {
            return null;
        }

        $cart = $this->cartManager->getUserCart($userId);

        return new CartView($cart, $this->cartItemService->getCartItems($cart));
    }
}
