# Read the documentation: https://github.com/FriendsOfSymfony/FOSElasticaBundle/blob/master/doc/setup.md
parameters:
    fos_elastica_default_analysis:
        analyzer:
            custom_ru_en:
                tokenizer: ru_en_tokenizer
                filter: [ "lowercase", "custom_ru_stop_words" ]
            custom_uk:
                tokenizer: ru_en_tokenizer
                type: ukrainian
                filter: [ "lowercase", "custom_uk_stop_words" ]
                min_token_length: 3
            autocomplete:
                tokenizer: autocomplete_tokenizer
                filter: [ lowercase ]
                max_token_length: 10
                min_token_length: 2
        tokenizer:
            ru_en_tokenizer:
                type: standard
                min_token_length: 2
            autocomplete_tokenizer:
                type: edge_ngram
                min_gram: 2
                max_gram: 10
        filter:
            custom_uk_stop_words:
                "type": "stop"
                "stopwords": "е,є,і,аж,ви,де,до,за,зі,ми,на,не,ну,нх,ні,по,та,ти,то,ту,ті,це,цю,ця,ці,чи,ще,що,як,їй,їм,їх,її,а,без,более,бы,был,была,были,было,быть,в,вам,вас,весь,во,вот,все,всего,всех,вы,где,да,даже,для,до,его,ее,если,есть,еще,же,за,здесь,и,из,или,им,их,к,как,ко,когда,кто,ли,либо,мне,может,мы,на,надо,наш,не,него,нее,нет,ни,них,но,ну,о,об,однако,он,она,они,оно,от,очень,по,под,при,с,со,так,также,такой,там,те,тем,то,того,тоже,той,только,том,ты,у,уже,хотя,чего,чей,чем,что,чтобы,чье,чья,эта,эти,это,я,a,an,and,are,as,at,be,but,by,for,if,in,into,is,it,no,not,of,on,or,such,that,the,their,then,there,these,they,this,to,was,will,with"
            custom_ru_stop_words:
                "type": "stop"
                "stopwords": "а,без,более,бы,был,была,были,было,быть,в,вам,вас,весь,во,вот,все,всего,всех,вы,где,да,даже,для,до,его,ее,если,есть,еще,же,за,здесь,и,из,или,им,их,к,как,ко,когда,кто,ли,либо,мне,может,мы,на,надо,наш,не,него,нее,нет,ни,них,но,ну,о,об,однако,он,она,они,оно,от,очень,по,под,при,с,со,так,также,такой,там,те,тем,то,того,тоже,той,только,том,ты,у,уже,хотя,чего,чей,чем,что,чтобы,чье,чья,эта,эти,это,я,a,an,and,are,as,at,be,but,by,for,if,in,into,is,it,no,not,of,on,or,such,that,the,their,then,there,these,they,this,to,was,will,with"
        normalizer:
            lowercase_normalizer:
                type: custom
                filter: [lowercase]

fos_elastica:
    clients:
        default: { url: '%env(ELASTICSEARCH_URL)%/' }
    indexes:
        dzo_tender:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
                    mapping.nested_objects.limit: 100000
            properties:
                id: { fielddata: true }
                tenderID: { type: keyword }
                tenderIDText: ~
                mode: ~
                isMasked: { type: keyword }
                title: { analyzer: custom_uk }
                titleEn: { analyzer: standard }
                description: { analyzer: custom_uk }
                descriptionEn: { analyzer: standard }
                procurementMethod: ~
                procurementMethodTypeFilter: { analyzer: keyword }
                procurementMethodType: ~
                mainProcurementCategory: ~
                procurementMethodRationale: ~
                numberOfBids: { type: integer }
                submissionMethod: ~
                hasActiveMonitoring: ~
                status: ~
                owner: ~
                dateModified: { type: date }
                dateCreated: { type: date }
                date: { type: date }
                milestonesCode: { type: keyword }
                oldId: { type: integer }
                dzoData:
                    type: nested
                    include_in_root: true
                    properties:
                        favoriteUsers:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                        bidUsers:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                        bids:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword}
                                apiId: { type: keyword }
                                pay: { type: integer }
                                signed: { type: integer }
                                userId: { type: keyword }
                        ownerId: { type: keyword }
                value:
                    type: nested
                    include_in_root: true
                    properties:
                        currency: { type: keyword }
                        amount: { type: float }
                        valueAddedTaxIncluded: ~
                procuringEntity:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                enquiryPeriod:
                    type: nested
                    include_in_root: true
                    properties:
                        startDate: { type: date }
                        endDate: { type: date }
                tenderPeriod:
                    type: nested
                    include_in_root: true
                    properties:
                        startDate: { type: date }
                        endDate: { type: date }
                qualificationPeriod:
                    type: nested
                    include_in_root: true
                    properties:
                        startDate: { type: date }
                        endDate: { type: date }
                auctionPeriod:
                    type: nested
                    include_in_root: true
                    properties:
                        startDate: { type: date }
                        endDate: { type: date }
                        shouldStartAfter: { type: date }
                lots:
                    type: nested
                    include_in_root: true
                    properties:
                        id: ~
                        description: { analyzer: custom_uk }
                        descriptionEn: { analyzer: standard }
                funders:
                    type: nested
                    include_in_root: true
                    properties:
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                items:
                    type: nested
                    include_in_root: true
                    properties:
                        id: ~
                        description: { analyzer: custom_uk }
                        descriptionEn: { analyzer: standard }
                        quantity: { type: float }
                        deliveryAddress:
                            type: nested
                            include_in_root: true
                            properties:
                                region: { type: keyword }
                        classification:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                description: ~
                        additionalClassifications:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                description: ~
                        unit:
                            type: nested
                            include_in_root: true
                            properties:
                                code: { type: keyword }
                                name: ~
            persistence:
                driver: mongodb
                model: App\Document\Tender
                provider: ~
                finder: ~
        dzo_plan:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                status: { type: keyword }
                planId: { type: keyword }
                planIdText: ~
                mode: ~
                tenderId: { type: keyword }
                isMasked: { type: keyword }
                dateModified: { type: date }
                dateCreated: { type: date }
                datePublished: { type: date }
                tenderPeriodDate: { type: date }
                owner: { type: keyword }
                oldId: { type: integer }
                procuringEntity:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                budget:
                    type: nested
                    include_in_root: true
                    properties:
                        description: { analyzer: custom_uk }
                        currency: { type: keyword }
                        amount: { type: float }
                        amountNet: { type: float }
                        year: { type: integer }
                        period:
                            type: nested
                            include_in_root: true
                            properties:
                                startDate: { type: date }
                                endDate: { type: date }
                        breakdown:
                            type: nested
                            include_in_root: true
                            properties:
                                title: { analyzer: keyword }
                                #                                        description: { analyzer: whitespace }
                                value:
                                    type: nested
                                    include_in_root: true
                                    properties:
                                        currency: { type: keyword }
                                        amount: { type: float }
                                        valueAddedTaxIncluded: ~
                classification:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
                #                                scheme: ~
                additionalClassifications:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
                #                                scheme: ~
                items:
                    type: nested
                    include_in_root: true
                    properties:
                        id: ~
                        description: { analyzer: custom_uk }
                        descriptionEn: { analyzer: standard }
                        quantity: { type: float }
                        deliveryAddress:
                            type: nested
                            include_in_root: true
                            properties:
                                region: { type: keyword }
                        classification:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                #                                        scheme: ~
                                description: ~
                        additionalClassifications:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                description: ~
                        #                                        scheme: ~
                        unit:
                            type: nested
                            include_in_root: true
                            properties:
                                code: { type: keyword }
                                name: ~
            persistence:
                driver: mongodb
                model: App\Document\Plan
                provider: ~
                finder: ~
        dzo_contract:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                status: ~
                contractNumber: { type: keyword }
                contractId: { type: keyword }
                contractIdText: ~
                mode: ~
                tenderId: { type: keyword }
                isMasked: { type: keyword }
                awardId: { type: keyword }
                title: { analyzer: custom_uk }
                description: { analyzer: custom_uk }
                dateSigned: { type: date }
                dateModified: { type: date }
                dateCreated: { type: date }
                date: { type: date }
                owner: ~
                bidOwner: ~
                procurementMethodType: ~
                procurementMethodRationale: ~
                procurementMethodTypeFilter: { analyzer: keyword }
                oldId: { type: integer }
                period:
                    type: nested
                    include_in_root: true
                    properties:
                        startDate: { type: date }
                        endDate: { type: date }
                value:
                    type: nested
                    include_in_root: true
                    properties:
                        currency: { type: keyword }
                        amount: { type: float }
                        valueAddedTaxIncluded: ~
                amountPaid:
                    type: nested
                    include_in_root: true
                    properties:
                        currency: { type: keyword }
                        amount: { type: float }
                        valueAddedTaxIncluded: ~
                buyer:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                procuringEntity:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                suppliers:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                items:
                    type: nested
                    include_in_root: true
                    properties:
                        id: ~
                        description: { analyzer: custom_uk }
                        descriptionEn: { analyzer: standard }
                        quantity: { type: float }
                        deliveryAddress:
                            type: nested
                            include_in_root: true
                            properties:
                                region: { type: keyword }
                        classification:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                description: ~
                        #                                        scheme: ~
                        additionalClassifications:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                description: ~
                        #                                        scheme: ~
                        unit:
                            type: nested
                            include_in_root: true
                            properties:
                                code: { type: keyword }
                                name: ~
            persistence:
                driver: mongodb
                model: App\Document\Contract
                provider: ~
                finder: ~
        dzo_classification:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id:
                    fielddata: true
                    analyzer: autocomplete
                #                            index_prefixes:
                #                                min_chars: 3
                #                                max_chars: 10
                iri: ~
                parentId: { type: keyword }
                searchId: { type: keyword }
                treeLevel: ~
                children: ~
                selectable: ~
                scheme: { type: keyword }
                description: { analyzer: custom_uk }
                descriptionEn: { analyzer: standard }
                name: { analyzer: custom_uk }
                nameEn: { analyzer: standard }
            #                        parent:
            #                            type: nested
            #                            include_in_root: true
            #                            properties:
            #                                id: ~
            #                                scheme: ~
            #                                classificationId: { analyzer: custom_uk, search_analyzer: custom_uk }
            persistence:
                elastica_to_model_transformer:
                    ignore_missing: true
                model: App\Document\Classification
                driver: mongodb
                provider: ~
                finder: ~
        dzo_monitoring:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                tenderId: { type: keyword }
                dateModified: { type: date }
            persistence:
                driver: mongodb
                model: App\Document\Monitoring
                provider: ~
                finder: ~
        dzo_inspection:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                monitoringIds: { type: keyword }
                dateModified: { type: date }
            persistence:
                driver: mongodb
                model: App\Document\Inspection
                provider: ~
                finder: ~
        dzo_budgetary:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true, analyzer: keyword }
                code: { type: keyword }
                title: { analyzer: custom_uk }
                titleEn: { analyzer: standard }
            persistence:
                elastica_to_model_transformer:
                    ignore_missing: true
                model: App\Document\Budgetary
                driver: mongodb
                provider: ~
                finder: ~

        dzo_tender_identifier:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true, analyzer: keyword }
                scheme: { type: keyword }
                legalName: { analyzer: custom_uk }
            persistence:
                model: App\Document\TenderIdentifier
                driver: mongodb
                provider: ~
                finder: ~

        dzo_plan_identifier:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true, analyzer: keyword }
                scheme: { type: keyword }
                legalName: { analyzer: custom_uk }
            persistence:
                model: App\Document\PlanIdentifier
                driver: mongodb
                provider: ~
                finder: ~

        dzo_contract_identifier:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true, analyzer: keyword }
                scheme: { type: keyword }
                legalName: { analyzer: custom_uk }
            persistence:
                model: App\Document\ContractIdentifier
                driver: mongodb
                provider: ~
                finder: ~

        dzo_contract_supplier:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true, analyzer: keyword }
                scheme: { type: keyword }
                legalName: { analyzer: custom_uk }
            persistence:
                model: App\Document\ContractSupplier
                driver: mongodb
                provider: ~
                finder: ~

        dzo_category:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                prozorroId: {type: keyword}
                title: {
                    type: 'icu_collation_keyword',
                    language: 'uk',
                    country: 'UK',
                    fields: {
                        title: {
                            type: text, analyzer: custom_uk
                        }
                    }
                }
                description: { type: keyword }
                status: { type: keyword }
                popularity: { type: integer }
                dateModified: { type: date }
                dateCreated: { type: date }
                procuringEntity:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { fielddata: true }
                        name: { analyzer: custom_uk }
                        identifier:
                            type: nested
                            include_in_root: true
                            properties:
                                id: { type: keyword }
                                scheme: { type: keyword }
                                legalname: ~
                classification:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
            persistence:
                driver: mongodb
                model: App\Document\Category
                provider: ~
                finder: ~

        dzo_market_classification:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                scheme: { type: keyword }
                activeCategories: {type: integer}
                popularity: { type: integer }
                description: {
                    type: 'icu_collation_keyword',
                    language: 'uk',
                    country: 'UK',
                    fields: {
                        description: {
                            type: text, analyzer: custom_uk
                        }
                    }
                }
            persistence:
                elastica_to_model_transformer:
                    ignore_missing: true
                model: App\Document\MarketClassification
                driver: mongodb
                provider: ~
                finder: ~

        dzo_profile:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                prozorroId: {type: keyword}
                scheme: { type: keyword }
                status: { type: keyword }
                popularity: { type: integer }
                description: { type: keyword }
                dateModified: { type: date }
                dateCreated: { type: date }
                classification:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
                relatedCategory: { type: keyword }
                relatedCategoryId: { type: keyword }
                relatedCategoryStatus: { type: keyword }
            persistence:
                elastica_to_model_transformer:
                    ignore_missing: true
                model: App\Document\Profile
                driver: mongodb
                provider: ~
                finder: ~

        dzo_product:
            settings:
                index:
                    analysis: '%fos_elastica_default_analysis%'
            properties:
                id: { fielddata: true }
                prozorroId: { type: keyword }
                scheme: { type: keyword }
                status: { type: keyword }
                description: { type: keyword }
                popularity: { type: integer }
                title: {
                    type: 'icu_collation_keyword',
                    language: 'uk',
                    country: 'UK',
                    fields: {
                        title: {
                            type: text, analyzer: custom_uk
                        },
                        title_keyword: {
                            type: keyword,
                            normalizer: lowercase_normalizer
                        }
                    }
                }
                classification:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
                relatedCategory:  { type: keyword }
                relatedCategoryId: {type: keyword }
                relatedCategoryStatus: { type: keyword }
                relatedProfiles: {type: keyword}
                relatedProfileIds: {type: keyword}
                dateModified: { type: date }
                dateCreated: { type: date }
                expirationDate: { type: date }
                requirementResponses:
                    type: nested
                    include_in_root: true
                    properties:
                        requirement: {type: keyword}
                        classification:
                            type: nested
                            include_in_root: true
                            properties:
                                id: {type: keyword}
                productRequirements:
                    type: nested
                    include_in_root: true
                    properties:
                        requirement: {type: keyword}
                        values: {type: keyword}
                textRequirements:
                    type: nested
                    include_in_root: true
                    properties:
                        requirement: { type: keyword }
                        values: {type: keyword}
                numberRequirements:
                    type: nested
                    include_in_root: true
                    properties:
                        requirement: { type: keyword }
                        values: {type: float}
                booleanRequirements:
                    type: nested
                    include_in_root: true
                    properties:
                        requirement: { type: keyword }
                        values: {type: keyword}
                dzoCategoryDataTypesMatch: { type: boolean }
                documents:
                    type: nested
                    include_in_root: true
                    properties:
                        id: { type: keyword }
                        title: { type: keyword }
                        format: { type: keyword }
            persistence:
                elastica_to_model_transformer:
                    ignore_missing: true
                model: App\Document\Product
                driver: mongodb
                provider: ~
                finder: ~