<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Buyer
{
    #[MongoDB\Id]
    private ?string $id = null;
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $name = null;
    #[MongoDB\EmbedOne(targetDocument: EmbeddedIdentifier::class)]
    private ?EmbeddedIdentifier $identifier = null;
    #[MongoDB\Field(type: Type::RAW)]
    private $address;

    public function getAddress()
    {
        return $this->address;
    }

    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getIdentifier(): ?EmbeddedIdentifier
    {
        return $this->identifier;
    }

    public function setIdentifier(?EmbeddedIdentifier $identifier): void
    {
        $this->identifier = $identifier;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }
}
