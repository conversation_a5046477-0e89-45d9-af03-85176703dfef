<?php

namespace App\Service;

use App\Document\Category;
use App\Document\Product;
use App\Manager\CategoryManager;
use App\Manager\ProductManager;
use App\Resource\Embedded\EmbeddedCategoryView;
use App\Resource\MarketClassificationView;
use Doctrine\Common\Collections\Criteria;
use Predis\ClientInterface;
use Symfony\Component\Serializer\SerializerInterface;

readonly class MarketCacheService
{
    public const MARKET_CACHE_KEY = 'market_cache';
    private const CACHE_TTL = 3600;

    private const PRODUCT_CACHE_TTL = 86400; // 24 hours

    public function __construct(
        private ClientInterface $redisClient,
        private CategoryManager $categoryManager,
        private SerializerInterface $serializer,
        private ProductManager $productManager,
        private string $awsCdnUrl
    ) {
    }

    public function getProductsCacheByCategoryId(string $categoryId): array
    {
        if ($cache = $this->redisClient->get(self::MARKET_CACHE_KEY . $categoryId)) {
            return json_decode($cache, true);
        }

        return [];
    }

    public function generateMarketProductsCache(): void
    {
        $categories = $this->categoryManager->getRepository()->findBy(['status' => 'active']);
        foreach ($categories as $category) {
            $products = $this->productManager
                ->getRepository()
                ->findBy(
                    ['relatedCategoryId' => $category->getId()],
                    ['title' => Criteria::ASC], // @todo by popular
                    5
                );
            if ($products) {
                $productCache = [];
                /** @var Product $product */
                foreach ($products as $product) {
                    $cacheItem = [
                        'id' => $product->getId(),
                        'title' => $product->getTitle(),
                    ];

                    if ($image = $product->getImages()->first()) {
                        $cacheItem['images'] = $this->awsCdnUrl . $image->getUrl();
                    }

                    $productCache[] = $cacheItem;
                }
                $this->redisClient->set(
                    self::MARKET_CACHE_KEY . $category->getId(),
                    json_encode($this->serializer->normalize($productCache)),
                    'EX',
                    self::PRODUCT_CACHE_TTL
                );
            }
        }
    }

    public function generateMarketClassificationsCache(): void
    {
        $categories = $this->categoryManager->getRepository()->findBy(['status' => 'active', 'classification.id' => ['$ne' => '99999999-9']], ['description' => 'DESC']
        );
        foreach ($categories as $category) {
            $this->addCategoryToCache($category);
        }
    }

    public function addCategoryToCache(Category $category): void
    {
        if (!$cache = json_decode($this->redisClient->get(self::MARKET_CACHE_KEY), true)) {
            $cache = [];
        }
        $marketClassification = $category->getClassification();
        if (!isset($cache[$marketClassification->getId()])) {
            $cache[$marketClassification->getId()] = (array)new MarketClassificationView(
                marketClassification: $marketClassification, awsCdnUrl: $this->awsCdnUrl
            );
        }

        $key = array_search(
            $category->getId(),
            array_column($cache[$marketClassification->getId()]['categories'], 'id')
        );

        if (false !== $key) {
            $cache[$marketClassification->getId()]['categories'][$key] = (array)new EmbeddedCategoryView($category);
        } else {
            $cache[$marketClassification->getId()]['categories'][] = (array)new EmbeddedCategoryView($category);
        }

        $this->redisClient->set(
            self::MARKET_CACHE_KEY,
            json_encode($this->serializer->normalize($cache)),
            'EX',
            self::CACHE_TTL
        );
    }

    public function getMarketClassificationsCache(): array
    {
        $cache = $this->redisClient->get(self::MARKET_CACHE_KEY);

        if (!$cache) {
            $this->generateMarketClassificationsCache();
        }

        return json_decode($this->redisClient->get(self::MARKET_CACHE_KEY), true);
    }

    public function sortMarketClassificationsCache(): void
    {
        $cache = json_decode($this->redisClient->get(self::MARKET_CACHE_KEY), true);
        usort($cache, function ($item1, $item2) {
            $collator = collator_create('uk_UA');
            $arr = array($item1['description'], $item2['description']);
            collator_asort($collator, $arr, \Collator::SORT_STRING);

            return array_pop($arr) == $item1['description'];
        });


        $this->redisClient->set(
            self::MARKET_CACHE_KEY,
            json_encode($this->serializer->normalize($cache)),
            'EX',
            self::CACHE_TTL
        );
    }

    public function clearClassificationCache(): void
    {
        $this->redisClient->del(self::MARKET_CACHE_KEY);
    }
}
