<?php

namespace App\Type;

use App\Document\Category;
use App\Document\Contract;
use App\Document\Inspection;
use App\Document\Monitoring;
use App\Document\Plan;
use App\Document\Product;
use App\Document\Profile;
use App\Document\Tender;

class DocumentType
{
    public static string $TENDER = 'tender';

    public static string $CONTRACT = 'contract';

    public static string $PLAN = 'plan';

    public static string $INSPECTION = 'inspection';

    public static string $MONITORING = 'monitoring';

    public static string $CATEGORY = 'category';

    public static string $PROFILE = 'profile';

    public static string $PRODUCT = 'product';

    public static function getDocumentClass(string $type): string
    {
        return match ($type) {
            self::$TENDER => Tender::class,
            self::$CONTRACT => Contract::class,
            self::$PLAN => Plan::class,
            self::$INSPECTION => Inspection::class,
            self::$MONITORING => Monitoring::class,
            self::$CATEGORY => Category::class,
            self::$PROFILE => Profile::class,
            self::$PRODUCT => Product::class,
            default => throw new \Error('Unknown type: '.$type)
        };
    }
}
