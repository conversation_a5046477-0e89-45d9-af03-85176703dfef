App\Resource\Embedded\ItemView:
  attributes:
    id:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    description:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    descriptionEn:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    classification:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    deliveryDate:
      groups: ['tender:read', 'tender:cdb_parse']
    additionalClassifications:
      groups: ['tender:read', 'tender:cdb_parse']
    unit:
      groups: ['tender:read', 'tender:cdb_parse']
    quantity:
      groups: ['tender:read', 'tender:cdb_parse']
    deliveryAddress:
      groups: ['tender:read', 'tender:cdb_parse']
    location:
      groups: ['tender:read', 'tender:cdb_parse']
    relatedLot:
      groups: ['tender:read', 'tender:cdb_parse']
    award:
      groups: ['tender:read', 'tender:cdb_parse']
