<?php

namespace App\Repository;

use App\Document\Cart;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class CartRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Cart::class);
    }

    public function save(Cart $filter)
    {
        $this->dm->persist($filter);
        $this->dm->flush();
    }

    public function remove(Cart $filter)
    {
        $this->dm->remove($filter);
        $this->dm->flush();
    }
}
