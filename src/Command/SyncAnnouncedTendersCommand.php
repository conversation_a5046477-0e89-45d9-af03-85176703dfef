<?php

namespace App\Command;

use App\Legacy\Repository\LegacyTenderRepository;
use App\Repository\TenderRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:sync:announced-tenders',
    description: 'Sync announced tenders from legacy database',
)]
class SyncAnnouncedTendersCommand extends Command
{
    private const TENDER_ANNOUNCED_CHUNK = 100;

    public function __construct(
        private readonly DocumentManager $dm,
        private readonly LegacyTenderRepository $legacyTenderRepository,
        private readonly TenderRepository $tenderRepository,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $announced = $this->legacyTenderRepository->tenderAnnounced();

        $count = 0;
        foreach ($announced as $item) {
            if (0 === $item['user_id']) {
                continue;
            }
            $tender = $this->tenderRepository->findOneBy(['id' => $item['tender_id']]);
            if (!$tender) {
                continue;
            }
            $tender->getDzoData()->addOwnerId($item['user_id']);
            if (self::TENDER_ANNOUNCED_CHUNK === $count) {
                $this->dm->flush();
                $this->dm->clear();
                $count = 0;
            }
            ++$count;
        }

        $this->dm->flush();
        $this->dm->clear();

        return Command::SUCCESS;
    }
}
