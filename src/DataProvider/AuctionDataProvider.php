<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Document\Tender;
use App\Resource\AuctionView;
use Doctrine\ODM\MongoDB\DocumentManager;
use GuzzleHttp\Client;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Class AuctionDataProvider.
 */
final class AuctionDataProvider implements ProviderInterface
{
    private string $prozorroPublicAuctionPointUrl;

    /**
     * AuctionDataProvider constructor.
     */
    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly SerializerInterface $serializer,
        string $prozorroPublicAuctionPointUrl
    ) {
        $this->prozorroPublicAuctionPointUrl = rtrim($prozorroPublicAuctionPointUrl, '/').'/';
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object
    {
        $id = $uriVariables['id'];
        $parts = explode('_', $id);
        $tenderId = $parts[0];
        /** @var Tender|null $tender */
        $tender = $this->documentManager->find(Tender::class, $tenderId);
        if (null === $tender) {
            throw new NotFoundHttpException();
        }

        $client = new Client();

        $response = $client->request('GET', $this->prozorroPublicAuctionPointUrl.$id);

        $result = json_decode((string) $response->getBody(), true);
        foreach ($result as $key => $value) {
            $camelCaseKey = str_replace(' ', '', ucwords(str_replace('_', ' ', $key)));
            $camelCaseKey[0] = strtolower($camelCaseKey[0]);
            if ($camelCaseKey !== $key) {
                $result[$camelCaseKey] = $result[$key];
            }
        }

        return $this
            ->serializer
            ->deserialize(
                json_encode($result),
                AuctionView::class,
                'json'
            );
    }
}
