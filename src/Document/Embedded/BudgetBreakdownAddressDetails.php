<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class BudgetBreakdownAddressDetails
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;
    
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;
    
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $scheme = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getScheme(): ?string
    {
        return $this->scheme;
    }

    public function setScheme(?string $scheme): void
    {
        $this->scheme = $scheme;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
