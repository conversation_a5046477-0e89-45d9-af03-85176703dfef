<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\InspectionManager;
use App\Resource\InspectionView;

/**
 * Class MonitoringDataProvider.
 */
final class InspectionDataProvider implements ProviderInterface
{
    public function __construct(
        private readonly InspectionManager $inspectionManager
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->inspectionManager->find($uriVariables['id']);
        if (!$document) {
            return null;
        }

        return new InspectionView($document);
    }
}
