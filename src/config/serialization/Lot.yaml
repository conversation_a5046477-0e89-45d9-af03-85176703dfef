App\Document\Embedded\Lot:
  attributes:
    id:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    description:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    descriptionEn:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    title:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    titleEn:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    value:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    guarantee:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    date:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    minimalStep:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    auctionPeriod:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    auctionUrl:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    status:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    minimalStepPercentage:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    fundingKind:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
    yearlyPaymentsPercentageRange:
      groups: ['tender:list', 'tender:read', 'tender:cdb_parse']
