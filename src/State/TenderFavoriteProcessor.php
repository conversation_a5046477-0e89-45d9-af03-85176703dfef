<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Document\Tender;
use App\Repository\TenderRepository;
use App\Resource\TenderView;
use App\Utils\CurrentUserService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TenderFavoriteProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly TenderRepository $tenderRepository,
        private readonly CurrentUserService $currentUserService
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        $userId = $this->currentUserService->userIdAsString();
        $tenderId = $data->tenderId;

        /** @var Tender $tender */
        $tender = $this->tenderRepository->findOneBy(['id' => $tenderId]);
        if (!$tender) {
            throw new NotFoundHttpException('Tender not found');
        }
        $status = $tender->getStatus();

        if ('active.enquiries' === $status || 'active.tendering' === $status) {
            $dzoData = $tender->getDzoData();
            if ($dzoData->isFavorite($userId)) {
                $dzoData->removeFavoriteUser($userId);
            } else {
                $dzoData->addFavoriteUser($userId);
            }
            $this->tenderRepository->save($tender);
        } else {
            return new JsonResponse('Status must be active.enquiries or active.tendering');
        }

        return new TenderView($tender, $userId);
    }
}
