<?php

namespace App\Messenger;

use App\Message\ProductMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class ProductSerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $product = new ProductMessage($data['id']);

        return new Envelope($product);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();
        if ($message instanceof ProductMessage) {
            $data = ['id' => $message->getId()];
        } else {
            throw new \Exception('Unsupported message class');
        }

        return [
            'body' => json_encode($data),
            'headers' => [],
        ];
    }
}
