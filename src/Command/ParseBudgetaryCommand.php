<?php

namespace App\Command;

use App\Document\Budgetary;
use App\Manager\BudgetaryManager;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Class ParseBudgetaryCommand.
 */
class ParseBudgetaryCommand extends Command
{
    protected static $defaultName = 'api:budgetary:parse';

    private SymfonyStyle $inputOutput;

    private array $classificationSources;

    protected function configure()
    {
        $this
            ->setDescription('Update budgetary list in database')
            ->addArgument('file', InputArgument::REQUIRED, 'json file with data')
        ;
    }

    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * ParseBudgetaryCommand constructor.
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly BudgetaryManager $manager,
        array $classificationSources,
        private Client $client = new Client()
    ) {
        parent::__construct(self::$defaultName);
        $this->classificationSources = $classificationSources;
    }

    /**
     * @throws GuzzleException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->inputOutput = new SymfonyStyle($input, $output);

        $file = $input->getArgument('file');

        $raw = file_get_contents($file);
        $this->inputOutput->comment('Starting file '.$file);

        $decoded = json_decode($raw, true);
        $this->inputOutput->progressStart(count($decoded));
        $documentsCount = 0;

        foreach ($decoded as $item) {
            $budgetary = new Budgetary();
            $budgetary
                ->setId($item['code'])
                ->setCode($item['code'])
                ->setTitle($item['title'])
                ->setTitleEn($item['title_en'])
                ->setType($item['type']);

            ++$documentsCount;
            $this->inputOutput->progressAdvance();
            $this->manager->save($budgetary, ($documentsCount % 50) === 0);
        }

        $this->manager->getDocumentManager()->flush();

        $this->inputOutput->text('done!');

        return 0;
    }
}
