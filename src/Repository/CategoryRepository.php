<?php

namespace App\Repository;

use App\Document\Category;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class CategoryRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Category::class);
    }

    public function save(Category $category)
    {
        $this->dm->persist($category);
        $this->dm->flush();
    }

    public function remove(Category $category)
    {
        $this->dm->remove($category);
        $this->dm->flush();
    }

    public function findActiveByClassificationId(string $classificationId)
    {
        return $this->createQueryBuilder()
            ->field('classification._id')->equals($classificationId)
            ->field('status')->equals('active')
            ->getQuery()
            ->execute()
            ->count();
    }
}
