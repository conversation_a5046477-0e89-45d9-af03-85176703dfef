<?php

namespace App\MessageHandler;

use App\Message\CategoryMessage;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class CategoryHandler extends BaseHandler
{
    /**
     * @throws \JsonException
     * @throws \Throwable
     */
    public function __invoke(CategoryMessage $categoryMessage): void
    {
        $this->processCategory($categoryMessage->getId(), true);
    }
}
