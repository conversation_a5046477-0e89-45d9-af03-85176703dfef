<?php

namespace App\Command;

use App\Message\ContractMessage;
use App\Message\PlanMessage;
use App\Message\TenderMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:fix:date-created',
    description: 'Fix dateCreated field in documents',
)]
class FixDateCreatedCommand extends Command
{
    private const CONTRACT = 'contract';
    private const PLAN = 'plan';
    private const TENDER = 'tender';

    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly MessageBusInterface $messageBus,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function configure()
    {
        $this
            ->addArgument('type', InputArgument::REQUIRED, 'Type of document to fix dateCreated field: plan, contract or tender');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $type = $input->getArgument('type');

        if (self::CONTRACT !== $type && self::PLAN !== $type && self::TENDER !== $type) {
            throw new \InvalidArgumentException('Invalid type of document: must be plan, contract or tender');
        }

        $inputOutput = new SymfonyStyle($input, $output);
        $pageSize = 20000;

        $documentClass = DocumentType::getDocumentClass($type);
        $queryBuilder = $this->documentManager->createQueryBuilder($documentClass)
            ->select('id')
            ->limit($pageSize);

        $queryBuilder->addAnd(
            $queryBuilder->expr()->field('dateCreated')->exists(false)
        );

        $result = $queryBuilder->getQuery()->execute();
        if (0 === count($result)) {
            $output->writeln("All $type documents have dateCreated field set. Nothing to fix.");

            return Command::SUCCESS;
        }

        foreach ($result as $document) {
            $id = $document->getId();

            $message = match ($type) {
                self::CONTRACT => new ContractMessage($id),
                self::PLAN => new PlanMessage($id),
                self::TENDER => new TenderMessage($id),
                default => null
            };

            if ($message) {
                $this->messageBus->dispatch($message);
            }
        }

        $this->documentManager->clear();
        $output->writeln("Fixing dateCreated for $type documents finished");

        return Command::SUCCESS;
    }
}
