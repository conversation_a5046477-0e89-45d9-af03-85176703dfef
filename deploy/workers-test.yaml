phpWorkers:
  enabled: true
  workers:
    - name: tenders
      args: [ "messenger:consume", "tenders", "--memory-limit=256M", "--time-limit=3600" ]
    - name: contracts
      args: [ "messenger:consume", "contracts", "--memory-limit=256M", "--time-limit=3600" ]
    - name: inspections
      args: [ "messenger:consume", "inspections", "--memory-limit=256M", "--time-limit=3600" ]
    - name: monitorings
      args: [ "messenger:consume", "monitorings", "--memory-limit=256M", "--time-limit=3600" ]
    - name: plans
      args: [ "messenger:consume", "plans", "--memory-limit=256M", "--time-limit=3600" ]
    - name: categories
      args: [ "messenger:consume", "categories", "--memory-limit=256M", "--time-limit=3600" ]
    - name: profiles
      args: [ "messenger:consume", "profiles", "--memory-limit=256M", "--time-limit=3600" ]
    - name: products
      args: [ "messenger:consume", "products", "--memory-limit=256M", "--time-limit=3600" ]
    - name: search-mongo-to-es
      args: [ "messenger:consume", "search_mongo_to_es" ]
    - name: category-status-setter
      args: [ "messenger:consume", "category_status_setter", "--memory-limit=256M", "--time-limit=3600" ]
    - name: old-id-setter
      args: [ "messenger:consume", "old_id_setter", "--memory-limit=256M", "--time-limit=3600" ]
    - name: agreements
      args: [ "messenger:consume", "agreements", "--memory-limit=256M", "--time-limit=3600" ]
    - name: bid-errors
      args: [ "messenger:consume", "bid_errors", "--memory-limit=256M", "--time-limit=3600" ]
    - name: bids
      args: [ "messenger:consume", "bids", "--memory-limit=256M", "--time-limit=3600" ]
    - name: popularity-setter
      args: [ "messenger:consume", "popularity_setter", "--memory-limit=256M", "--time-limit=3600" ]
    - name: update-visibility-flags
      args: [ "messenger:consume", "update_visibility_flags", "--memory-limit=256M", "--time-limit=3600" ]

  configMapName: dzo-search-worker

  serviceAccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dzo-market-eks-role
