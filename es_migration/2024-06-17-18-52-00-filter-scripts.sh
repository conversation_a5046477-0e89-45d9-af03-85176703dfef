PUT _scripts/textRequirements
{
  "script": {
    "lang": "painless",
    "source": """
                         for (int i = 0; i < doc['textRequirements.values'].length; i++) {
                          boolean found = false;
                          for(int j = 0; j < params.values.length; j++) {
                            if (doc['textRequirements.values'][i] == params.values[j]) {
                              found = true;
                              break;
                            }
                          }
                          if (found == false) return false;
                        }
                        return true;
                        """
  }
}

PUT _scripts/numberRequirements
{
  "script": {
    "lang": "painless",
    "source": """
                         for (int i = 0; i < doc['numberRequirements.values'].length; i++) {
                          boolean found = false;
                          for(int j = 0; j < params.values.length; j++) {
                            if (doc['numberRequirements.values'][i] == params.values[j]) {
                              found = true;
                              break;
                            }
                          }
                          if (found == false) return false;
                        }
                        return true;
                        """
  }
}

PUT _scripts/booleanRequirements
{
  "script": {
    "lang": "painless",
    "source": """
                         for (int i = 0; i < doc['booleanRequirements.values'].length; i++) {
                          boolean found = false;
                          for(int j = 0; j < params.values.length; j++) {
                            if (doc['booleanRequirements.values'][i] == params.values[j]) {
                              found = true;
                              break;
                            }
                          }
                          if (found == false) return false;
                        }
                        return true;
                        """
  }
}
