<?php

namespace App\Command;

use App\Service\MarketCacheService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:market:generate-cache',
    description: 'Generate cache for market categories',
)]
class MarketCacheCommand extends Command
{
    protected static $defaultName = 'app:market:generate-cache';

    public function __construct(
        private readonly MarketCacheService $marketCacheService
    ) {
        parent::__construct(self::$defaultName);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->marketCacheService->clearClassificationCache();
        $this->marketCacheService->generateMarketClassificationsCache();
        $this->marketCacheService->sortMarketClassificationsCache();

        return Command::SUCCESS;
    }
}
