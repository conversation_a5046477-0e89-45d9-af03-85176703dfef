<?php

namespace App\Legacy\Repository;

use App\Legacy\Dto\LegacyTenderDto;
use App\Legacy\Helper\DatabaseHelper;

class LegacyTenderRepository extends LegacyRepository implements LegacyOldIdSetterInterface
{
    public function tenderAnnounced()
    {
        $query = $this->connection->prepare('SELECT user_id, tender_id FROM ' . DatabaseHelper::getTableName('tender'));

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function tenderOwnerByTenderId(string $tenderId)
    {
        $query = $this->connection->prepare(
            'SELECT user_id 
            FROM ' . DatabaseHelper::getTableName('tender') . ' AS tender ' .
            'WHERE tender.tender_id = :tenderId'
        );

        $query->bindValue('tenderId', $tenderId);

        return $query->executeQuery()->fetchOne();
    }

    public function createPriceQuotingTender(LegacyTenderDto $tenderDto)
    {
        $this->connection->insert(DatabaseHelper::getTableName('tender'), $tenderDto->getArray());

        return $this->connection->lastInsertId();
    }

    public function findUnitId(string $unitCode)
    {
        $query = 'SELECT r.id FROM ' . DatabaseHelper::getTableName('rtl') . ' AS rtl ' .
            'join ' . DatabaseHelper::getTableName('records') . ' r on (rtl.record_id=r.id) ' .
            'left join ' . DatabaseHelper::getTableName('text') . ' t on (t.record_id=r.id and t.lang_id="2")' .
            'where r.sys LIKE BINARY :unitCode and r.visible=1';

        $query = $this->connection->prepare($query);
        $query->bindValue('unitCode', $unitCode);

        return $query->executeQuery()->fetchOne();
    }

    public function getForOldIdSetter(int $start, int $limit, bool $isArchive = false): false|array
    {
        $query = 'SELECT t.id, t.tender_id FROM ' . DatabaseHelper::getTableName('tender') . ' AS t ' .
        'WHERE t.id < :start AND t.tender_id IS NOT NULL AND t.tender_id != "" ORDER BY t.id DESC LIMIT ' . $limit;

        if (!$isArchive) {
            $query = $this->connection->prepare($query);
        } else {
            $query = $this->archiveConnection->prepare($query);
        }

        $query->bindValue('start', $start);

        return $query->executeQuery()->fetchAllAssociative();
    }
}
