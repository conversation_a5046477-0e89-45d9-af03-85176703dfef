<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Bid;

class DzoDataBidView
{
    public ?string $id = null;

    public ?string $apiId = null;

    public ?string $userId = null;

    public ?float $value = null;

    public ?string $currency = null;

    public bool $valueAddedTax;

    public function __construct(
        Bid $bid
    ) {
        $this->id = $bid->getId();
        $this->apiId = $bid->getApiId();
        $this->userId = $bid->getUserId();
        $this->value = $bid->getValue();
        $this->currency = $bid->getCurrency();
        $this->valueAddedTax = $bid->getValueAddedTax();
    }
}