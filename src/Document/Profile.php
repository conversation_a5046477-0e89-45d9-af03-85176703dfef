<?php

namespace App\Document;

use App\Document\Embedded\EmbeddedClassification;
use App\Document\Embedded\Image;
use App\Document\Embedded\Unit;
use App\Document\Embedded\Value;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document]
class Profile
{
    #[MongoDB\Id]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $prozorroId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $status = null;

    #[MongoDB\EmbedOne(nullable: true, targetDocument: EmbeddedClassification::class)]
    protected ?EmbeddedClassification $classification = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $relatedCategory = null;

    #[MongoDB\Field(type: Type::ID, nullable: true)]
    private ?string $relatedCategoryId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $relatedCategoryStatus = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $agreementID = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::DESC)]
    protected ?\DateTimeInterface $dateModified = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $owner = null;

    #[MongoDB\EmbedOne(targetDocument: Value::class)]
    protected ?Value $value = null;

    #[MongoDB\EmbedOne(targetDocument: Unit::class)]
    private ?Unit $unit = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $criteria;

    #[MongoDB\EmbedMany(targetDocument: Image::class)]
    private Collection|array $images;

    /**
     * @var Collection|EmbeddedClassification[]
     */
    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    private mixed $additionalClassifications = null;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private int $popularity = 0;

    public function __construct()
    {
        $this->images = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getProzorroId(): ?string
    {
        return $this->prozorroId;
    }

    public function setProzorroId(?string $prozorroId): void
    {
        $this->prozorroId = $prozorroId;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getClassification(): ?EmbeddedClassification
    {
        return $this->classification;
    }

    public function setClassification(?EmbeddedClassification $classification): Profile
    {
        $this->classification = $classification;

        return $this;
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    public function setDateModified(?\DateTimeInterface $dateModified): void
    {
        $this->dateModified = $dateModified;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    public function setDateCreated(?\DateTimeInterface $dateCreated): void
    {
        $this->dateCreated = $dateCreated;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): void
    {
        $this->owner = $owner;
    }

    public function getValue(): ?Value
    {
        return $this->value;
    }

    public function setValue(?Value $value): void
    {
        $this->value = $value;
    }

    public function getUnit(): ?Unit
    {
        return $this->unit;
    }

    public function setUnit(?Unit $unit): void
    {
        $this->unit = $unit;
    }

    public function getCriteria()
    {
        return $this->criteria;
    }

    public function setCriteria($criteria): void
    {
        $this->criteria = $criteria;
    }

    public function getAgreementID(): ?string
    {
        return $this->agreementID;
    }

    public function setAgreementID(?string $agreementID): void
    {
        $this->agreementID = $agreementID;
    }

    public function getRelatedCategory(): ?string
    {
        return $this->relatedCategory;
    }

    public function setRelatedCategory(?string $relatedCategory): Profile
    {
        $this->relatedCategory = $relatedCategory;

        return $this;
    }

    public function getRelatedCategoryId(): ?string
    {
        return $this->relatedCategoryId;
    }

    public function setRelatedCategoryId(?string $relatedCategoryId): Profile
    {
        $this->relatedCategoryId = $relatedCategoryId;

        return $this;
    }

    public function getImages(): Collection|array
    {
        return $this->images;
    }

    public function setImages(Collection|array $images): void
    {
        $this->images = $images;
    }

    public function addImage(Image $image): void
    {
        if (!$this->images->contains($image)) {
            $this->images->add($image);
        }
    }

    public function getAdditionalClassifications(): mixed
    {
        return $this->additionalClassifications;
    }

    public function setAdditionalClassifications(mixed $additionalClassifications): Profile
    {
        $this->additionalClassifications = $additionalClassifications;

        return $this;
    }

    public function getRelatedCategoryStatus(): ?string
    {
        return $this->relatedCategoryStatus;
    }

    public function setRelatedCategoryStatus(?string $relatedCategoryStatus): Profile
    {
        $this->relatedCategoryStatus = $relatedCategoryStatus;

        return $this;
    }

    public function getPopularity(): int
    {
        return $this->popularity;
    }

    public function setPopularity(int $popularity): Profile
    {
        $this->popularity = $popularity;
        return $this;
    }
}
