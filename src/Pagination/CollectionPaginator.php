<?php

namespace App\Pagination;

use ApiPlatform\State\Pagination\PaginatorInterface;

class CollectionPaginator implements \IteratorAggregate, PaginatorInterface
{
    private \Traversable $iterator;
    private readonly float $currentPage;
    private readonly float $lastPage;
    private readonly float $totalItems;
    private readonly float $itemsPerPage;

    public function __construct(array $results, float $currentPage, float $lastPage, float $itemsPerPage, float $totalItems)
    {
        if ($totalItems > 0) {
            $this->iterator = new \ArrayIterator($results);
        } else {
            $this->iterator = new \EmptyIterator();
        }
        $this->currentPage = $currentPage;
        $this->lastPage = $lastPage;
        $this->itemsPerPage = $itemsPerPage;
        $this->totalItems = $totalItems;
    }

    public function getCurrentPage(): float
    {
        return $this->currentPage;
    }

    public function getLastPage(): float
    {
        return $this->lastPage;
    }

    public function getItemsPerPage(): float
    {
        return $this->itemsPerPage;
    }

    public function getTotalItems(): float
    {
        return $this->totalItems;
    }

    public function count(): int
    {
        return iterator_count($this->iterator);
    }

    public function getIterator(): \Traversable
    {
        return $this->iterator;
    }
}
