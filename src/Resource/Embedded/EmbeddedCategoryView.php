<?php

namespace App\Resource\Embedded;

use ApiPlatform\Metadata\ApiProperty;
use App\Document\Category;

class EmbeddedCategoryView
{
    #[ApiProperty(identifier: true)]
    public ?string $id;

    public ?string $prozorroId;

    public ?string $status;

    public ?string $title;

    public function __construct(Category $category)
    {
        $this->id = $category->getId();
        $this->prozorroId = $category->getProzorroId();
        $this->status = $category->getStatus();
        $this->title = $category->getTitle();
    }
}
