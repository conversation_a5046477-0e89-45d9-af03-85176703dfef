<?php

namespace App\Manager;

use App\Document\HasDateModifiedInterface;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\MongoDBException;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;

trait ManagerTrait
{
    abstract public function getRepository(): DocumentRepository;

    abstract public function getDocumentManager(): DocumentManager;

    public function getLastOneDocument(): ?HasDateModifiedInterface
    {
        $objects = $this->getRepository()->findBy([], ['dateModified' => 'DESC'], 1);

        return array_pop($objects);
    }

    /**
     * @param bool $andFlush
     *
     * @throws MongoDBException
     */
    public function save($document, $andFlush = true)
    {
        $this->getDocumentManager()->persist($document);
        if ($andFlush) {
            $this->getDocumentManager()->flush();
            $this->getDocumentManager()->clear();
        }

        return $document;
    }
}
