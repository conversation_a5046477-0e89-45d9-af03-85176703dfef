<?php

namespace App\Document\Embedded;

use App\Uuid\Uuid;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
#[MongoDB\HasLifecycleCallbacks]
class Lot
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\Field(name: 'title_en', type: Type::STRING, nullable: true)]
    private ?string $titleEn = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(name: 'description_en', type: Type::STRING, nullable: true)]
    private ?string $descriptionEn = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $value;

    #[MongoDB\Field(type: Type::RAW)]
    private $guarantee;

    #[MongoDB\Field(type: Type::RAW)]
    private $date;

    #[MongoDB\Field(type: Type::RAW)]
    private $minimalStep;

    #[MongoDB\Field(type: Type::RAW)]
    private $auctionPeriod;

    #[MongoDB\Field(type: Type::RAW)]
    private $auctionUrl;

    #[MongoDB\Field(type: Type::RAW)]
    private $status;

    #[MongoDB\Field(type: Type::RAW)]
    private $minimalStepPercentage;

    #[MongoDB\Field(type: Type::RAW)]
    private $fundingKind;

    #[MongoDB\Field(type: Type::RAW)]
    private $yearlyPaymentsPercentageRange;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function setTitleEn(?string $titleEn): void
    {
        $this->descriptionEn = $titleEn;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function setDescriptionEn(?string $descriptionEn): void
    {
        $this->descriptionEn = $descriptionEn;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value)
    {
        $this->value = $value;
    }

    public function getGuarantee()
    {
        return $this->guarantee;
    }

    public function setGuarantee($guarantee)
    {
        $this->guarantee = $guarantee;
    }

    public function getDate()
    {
        return $this->date;
    }

    public function setDate($date)
    {
        $this->date = $date;
    }

    public function getMinimalStep()
    {
        return $this->minimalStep;
    }

    public function setMinimalStep($minimalStep)
    {
        $this->minimalStep = $minimalStep;
    }

    public function getAuctionPeriod()
    {
        return $this->auctionPeriod;
    }

    public function setAuctionPeriod($auctionPeriod)
    {
        $this->auctionPeriod = $auctionPeriod;
    }

    public function getAuctionUrl()
    {
        return $this->auctionUrl;
    }

    public function setAuctionUrl($auctionUrl)
    {
        $this->auctionUrl = $auctionUrl;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;
    }

    public function getMinimalStepPercentage()
    {
        return $this->minimalStepPercentage;
    }

    public function setMinimalStepPercentage($minimalStepPercentage)
    {
        $this->minimalStepPercentage = $minimalStepPercentage;
    }

    public function getFundingKind()
    {
        return $this->fundingKind;
    }

    public function setFundingKind($fundingKind)
    {
        $this->fundingKind = $fundingKind;
    }

    public function getYearlyPaymentsPercentageRange()
    {
        return $this->yearlyPaymentsPercentageRange;
    }

    public function setYearlyPaymentsPercentageRange($yearlyPaymentsPercentageRange)
    {
        $this->yearlyPaymentsPercentageRange = $yearlyPaymentsPercentageRange;
    }

    #[MongoDB\PrePersist]
    public function generateIdIfNotExists(): void
    {
        if (null === $this->id) {
            $this->id = Uuid::v4();
        }
    }
}
