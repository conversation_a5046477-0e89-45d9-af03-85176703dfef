<?php

namespace App\Repository;

use App\Document\CartItem;
use Doctrine\Bundle\MongoDBBundle\ManagerRegistry;
use Doctrine\Bundle\MongoDBBundle\Repository\ServiceDocumentRepository;

class CartItemRepository extends ServiceDocumentRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CartItem::class);
    }

    public function save(CartItem $cartItem)
    {
        $this->dm->persist($cartItem);
        $this->dm->flush();
    }

    public function remove(CartItem $cartItem)
    {
        $this->dm->remove($cartItem);
        $this->dm->flush();
    }

    public function findByIds(array $ids)
    {
        return $this->createQueryBuilder(CartItem::class)
            ->field('id')->in($ids)
            ->getQuery()
            ->execute();
    }
}
