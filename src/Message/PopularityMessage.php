<?php

namespace App\Message;

class PopularityMessage
{
    protected string $id;

    protected bool $increase;

    /**
     * @param string $id
     * @param bool $increase
     */
    public function __construct(string $id, bool $increase)
    {
        $this->id = $id;
        $this->increase = $increase;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): PopularityMessage
    {
        $this->id = $id;
        return $this;
    }

    public function isIncrease(): bool
    {
        return $this->increase;
    }

    public function setIncrease(bool $increase): PopularityMessage
    {
        $this->increase = $increase;
        return $this;
    }
}