<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Lot;

/**
 * Class LotView.
 */
class LotView
{
    private ?string $id = null;

    private ?string $title = null;

    private ?string $titleEn = null;

    private ?string $description = null;

    private ?string $descriptionEn = null;

    private $value;

    private $guarantee;

    private $date;

    private $minimalStep;

    private $auctionPeriod;

    private $auctionUrl;

    private $status;

    private $minimalStepPercentage;

    private $fundingKind;

    private $yearlyPaymentsPercentageRange;

    /**
     * LotView constructor.
     */
    public function __construct(Lot $lot)
    {
        $this->id = $lot->getId();
        $this->title = $lot->getTitle();
        $this->titleEn = $lot->getTitleEn();
        $this->description = $lot->getDescription();
        $this->descriptionEn = $lot->getDescriptionEn();
        $this->value = $lot->getValue();
        $this->guarantee = $lot->getGuarantee();
        $this->date = $lot->getDate();
        $this->minimalStep = $lot->getMinimalStep();
        $this->auctionPeriod = $lot->getAuctionPeriod();
        $this->auctionUrl = $lot->getAuctionUrl();
        $this->status = $lot->getStatus();
        $this->minimalStepPercentage = $lot->getMinimalStepPercentage();
        $this->fundingKind = $lot->getFundingKind();
        $this->yearlyPaymentsPercentageRange = $lot->getYearlyPaymentsPercentageRange();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function getGuarantee()
    {
        return $this->guarantee;
    }

    public function getDate()
    {
        return $this->date;
    }

    public function getMinimalStep()
    {
        return $this->minimalStep;
    }

    public function getAuctionPeriod()
    {
        return $this->auctionPeriod;
    }

    public function getAuctionUrl()
    {
        return $this->auctionUrl;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function getMinimalStepPercentage()
    {
        return $this->minimalStepPercentage;
    }

    public function getFundingKind()
    {
        return $this->fundingKind;
    }

    public function getYearlyPaymentsPercentageRange()
    {
        return $this->yearlyPaymentsPercentageRange;
    }
}
