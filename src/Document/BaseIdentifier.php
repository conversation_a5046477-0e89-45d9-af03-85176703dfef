<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\MappedSuperclass]
abstract class BaseIdentifier
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $scheme = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $legalName = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $uri = null;

    protected bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getScheme(): ?string
    {
        return $this->scheme;
    }

    public function setScheme(?string $scheme): void
    {
        $this->scheme = $scheme;
    }

    public function getLegalName(): ?string
    {
        if ($this->restrictedMode) {
            return 'Приховано';
        }

        return $this->legalName;
    }

    public function setLegalName(?string $legalName): void
    {
        $this->legalName = $legalName;
    }

    public function getUri(): ?string
    {
        return $this->uri;
    }

    public function setUri(?string $uri): void
    {
        $this->uri = $uri;
    }
}
