<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class ProcuringEntity
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $name = null;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedIdentifier::class)]
    private ?EmbeddedIdentifier $identifier = null;

    #[MongoDB\Field(type: Type::RAW)]
    private $additionalIdentifiers;

    #[MongoDB\Field(type: Type::RAW)]
    private $address;

    #[MongoDB\Field(type: Type::RAW)]
    private $contactPoint;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $kind = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getIdentifier(): ?EmbeddedIdentifier
    {
        return $this->identifier;
    }

    /**
     * @param mixed $identifier
     */
    public function setIdentifier(?EmbeddedIdentifier $identifier = null): void
    {
        $this->identifier = $identifier;
    }

    public function getAdditionalIdentifiers()
    {
        return $this->additionalIdentifiers;
    }

    public function setAdditionalIdentifiers($additionalIdentifiers): void
    {
        $this->additionalIdentifiers = $additionalIdentifiers;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getContactPoint()
    {
        return $this->contactPoint;
    }

    public function setContactPoint($contactPoint): void
    {
        $this->contactPoint = $contactPoint;
    }

    public function getKind(): ?string
    {
        return $this->kind;
    }

    public function setKind(?string $kind): void
    {
        $this->kind = $kind;
    }
}
