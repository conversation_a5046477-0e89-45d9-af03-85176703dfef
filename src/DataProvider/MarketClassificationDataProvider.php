<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Manager\CategoryManager;
use App\Manager\MarketClassificationManager;
use App\Resource\Embedded\EmbeddedCategoryView;
use App\Resource\MarketClassificationView;

final readonly class MarketClassificationDataProvider implements ProviderInterface
{
    private const LIMIT = 5;

    public function __construct(
        private MarketClassificationManager $marketClassificationManager,
        private CategoryManager $categoryManager,
        private string $awsCdnUrl
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $document = $this->marketClassificationManager->find($uriVariables['id']);
        if (!$document) {
            return null;
        }

        $limit = null;
        if ($context['operation'] instanceof GetCollection) {
            $limit = self::LIMIT;
        }

        $categories = $this->categoryManager->getRepository()->findBy(['classification.id' => $document->getId(), 'status' => 'active'], ['title' => 'ASC'], $limit);
        $categoryViews = [];
        foreach ($categories as $category) {
            $categoryViews[] = new EmbeddedCategoryView($category);
        }

        return new MarketClassificationView($document, $categoryViews, $this->awsCdnUrl);
    }
}
