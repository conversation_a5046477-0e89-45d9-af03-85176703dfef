<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Period
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    private ?\DateTimeInterface $startDate = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    private ?\DateTimeInterface $endDate = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    private ?\DateTimeInterface $shouldStartAfter = null;

    private bool $restrictedMode = false;

    public function setRestrictedMode(bool $restrictedMode): void
    {
        $this->restrictedMode = $restrictedMode;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @return $this
     */
    public function setId(?string $id): Period
    {
        $this->id = $id;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        if ($this->restrictedMode) {
            return new \DateTime('1970-01-01T00:00:00Z');
        }

        return $this->startDate;
    }

    /**
     * @return $this
     *
     * @throws \Exception
     */
    public function setStartDate($startDate): Period
    {
        if (is_string($startDate)) {
            $startDate = new \DateTime($startDate);
        }
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        if ($this->restrictedMode) {
            return new \DateTime('1970-01-01T00:00:00Z');
        }

        return $this->endDate;
    }

    /**
     * @param \DateTimeInterface|string $endDate
     *
     * @return $this
     *
     * @throws \Exception
     */
    public function setEndDate($endDate): Period
    {
        if (is_string($endDate)) {
            $endDate = new \DateTime($endDate);
        }
        $this->endDate = $endDate;

        return $this;
    }

    public function getShouldStartAfter(): ?\DateTimeInterface
    {
        return $this->shouldStartAfter;
    }

    /**
     * @throws \Exception
     */
    public function setShouldStartAfter(?\DateTimeInterface $shouldStartAfter): Period
    {
        if (is_string($shouldStartAfter)) {
            $shouldStartAfter = new \DateTime($shouldStartAfter);
        }
        $this->shouldStartAfter = $shouldStartAfter;

        return $this;
    }
}
