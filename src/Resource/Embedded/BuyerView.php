<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Buyer;
use App\Resource\BaseIdentifierView;

class BuyerView
{
    private ?string $id = null;

    private ?string $name = null;

    private ?BaseIdentifierView $identifier = null;

    private $address;

    public function __construct(Buyer $buyer)
    {
        $this->id = $buyer->getId();
        $this->name = $buyer->getName();

        $identifier = $buyer->getIdentifier();
        if ($identifier) {
            $this->identifier = new BaseIdentifierView($identifier);
        }

        $this->address = $buyer->getAddress();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getIdentifier(): ?BaseIdentifierView
    {
        return $this->identifier;
    }

    public function getAddress()
    {
        return $this->address;
    }
}
