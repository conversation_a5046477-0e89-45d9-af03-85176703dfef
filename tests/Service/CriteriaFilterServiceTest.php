<?php

namespace App\Tests\Service;

use App\Service\CriteriaFilterService;
use PHPUnit\Framework\TestCase;

class CriteriaFilterServiceTest extends TestCase
{
    private CriteriaFilterService $service;

    protected function setUp(): void
    {
        $this->service = new CriteriaFilterService();
    }

    public function testFilterArchivedRequirementsWithMixedData(): void
    {
        $unfilteredCriteria = [
            [
                'requirementGroups' => [
                    [
                        'requirements' => [
                            ['id' => 'req1', 'title' => 'Active Req 1', 'isArchived' => false],
                            ['id' => 'req2', 'title' => 'Archived Req 1', 'isArchived' => true],
                            ['id' => 'req3', 'title' => 'No Archive Flag Req'],
                        ],
                    ],
                ],
            ],
            [
                'requirementGroups' => [
                    [
                        'description' => 'Group without requirements'
                    ]
                ]
            ],
            [
                'requirementGroups' => [
                    [
                        'requirements' => [],
                    ],
                ],
            ],
            [
                'title' => 'Criterion without groups'
            ]
        ];

        $expectedFilteredCriteria = [
            [
                'requirementGroups' => [
                    [
                        'requirements' => [
                            ['id' => 'req1', 'title' => 'Active Req 1', 'isArchived' => false],
                            ['id' => 'req3', 'title' => 'No Archive Flag Req'],
                        ],
                    ],
                ],
            ],
            [
                'requirementGroups' => [
                    [
                        'description' => 'Group without requirements'
                    ]
                ]
            ],
            [
                'requirementGroups' => [
                    [
                        'requirements' => [],
                    ],
                ],
            ],
            [
                'title' => 'Criterion without groups'
            ]
        ];

        $actualFilteredCriteria = $this->service->filterArchivedRequirements($unfilteredCriteria);

        $this->assertEquals($expectedFilteredCriteria, $actualFilteredCriteria);
    }

    public function testFilterArchivedRequirementsWithNullInput(): void
    {
        $this->assertNull($this->service->filterArchivedRequirements(null));
    }

    public function testFilterArchivedRequirementsWithEmptyArrayInput(): void
    {
        $this->assertEquals([], $this->service->filterArchivedRequirements([]));
    }

    public function testFilterArchivedRequirementsOnlyArchived(): void
    {
        $unfiltered = [
            ['requirementGroups' => [['requirements' => [['id' => 'a1', 'isArchived' => true]]]]]
        ];
        $expected = [
            ['requirementGroups' => [['requirements' => []]]]
        ];
        $this->assertEquals($expected, $this->service->filterArchivedRequirements($unfiltered));
    }

    public function testFilterArchivedRequirementsOnlyActive(): void
    {
        $unfiltered = [
            ['requirementGroups' => [['requirements' => [['id' => 'a1', 'isArchived' => false]]]]]
        ];
        $this->assertEquals($unfiltered, $this->service->filterArchivedRequirements($unfiltered));
    }
}
