<?php

namespace App\Resource;

use App\Document\BaseIdentifier;

/**
 * Class BaseIdentifierView.
 */
readonly class BaseIdentifierView
{
    public ?string $id;

    public ?string $scheme;

    public ?string $legalName;

    public ?string $uri;

    /**
     * BaseIdentifierView constructor.
     */
    public function __construct(BaseIdentifier $baseIdentifier)
    {
        $this->id = ('' === $baseIdentifier->getId()) ? '_' : $baseIdentifier->getId();
        $this->scheme = $baseIdentifier->getScheme();
        $this->legalName = $baseIdentifier->getLegalName();
        $this->uri = $baseIdentifier->getUri();
    }
}
