<?php

namespace App\MessageHandler;

use App\Document\Embedded\ProductRequirement;
use App\Document\Product;
use App\Message\ProductMessage;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;

#[AsMessageHandler]
class ProductHandler extends BaseHandler
{
    public function __invoke(ProductMessage $productMessage): void
    {
        try {
            $categoryId = null;
            $response = $this->requestMarketApi('products/'.$productMessage->getId());

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            $prozorroId = $data['id'];
            unset($data['id']);

            if (!empty($data['relatedCategory'])) {
                $categoryId = $data['relatedCategory'];
            }

            if (!empty($data['images'])) {
                $imagesArray = $data['images'];
                unset($data['images']);
            }

            if (!empty($data['relatedProfiles'])) {
                $profilesArray = $data['relatedProfiles'];
            }
            unset($data['classification']);

            /** @var Product $product */
            $product = $this->getDocumentManager()->getRepository(Product::class)->findOneBy(
                ['prozorroId' => $prozorroId]
            );

            $product = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data, JSON_THROW_ON_ERROR),
                    Product::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $product]
                );
//            echo json_encode($product->getRequirementResponses());
//            die();

            $product->setProzorroId($prozorroId);

            if ($categoryId) {
                if ($category = $this->processCategory($categoryId, false)) {
                    $product->setRelatedCategoryId($category->getId());

                    if ($classification = $category->getClassification()) {
                        $product->setClassification($classification);
                    }

                    if ($category->getStatus()) {
                        $product->setRelatedCategoryStatus($category->getStatus());
                    }
                } else {
                    $product->setRelatedCategoryId(null);
                }
                $product->setRelatedCategory($categoryId);
            }

            if (!empty($imagesArray)) {
                $this->getMarketImages($product, $imagesArray);
            }

            if (!empty($profilesArray)) {
                $profileIds = [];
                foreach ($profilesArray as $k => $profile) {
                    $profile = $this->processProfile($profile, false);
                    $profileIds[] = $profile->getId();
                }
                $product->setRelatedProfileIds($profileIds);
            } else {
                $product->setRelatedProfileIds([]);
                $product->setRelatedProfiles([]);
            }

            $product = $this->processRequirements($product);

            if (!$product->getDateCreated()) {
                $product->setDateCreated($product->getDateModified());
            }

            $this->save($product);
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
        }
    }

    private function processRequirements(Product $product): Product
    {
        $product->setProductRequirements(new ArrayCollection());
        $requirements = $product->getRequirementResponses();
        foreach ($requirements as $requirement) {
            $productRequirement = new ProductRequirement();
            $productRequirement->setRequirement($requirement->getRequirement());

            $values = $requirement->getValues();
            if (is_array($values) && count($values) > 0) {
                $productRequirement->setType('text');
                $productRequirement->setValues($values);
            } else {
                $value = $requirement->getValue();
                $productRequirement->setValues([$value]);
                if (is_int($value) || is_float($value)) {
                    $productRequirement->setType('number');
                } elseif (is_bool($value)) {
                    $productRequirement->setType('boolean');
                } else {
                    // do not store string value - can be only array
                    continue;
                }
            }


            $product->addProductRequirement($productRequirement);
        }

        return $product;
    }
}
