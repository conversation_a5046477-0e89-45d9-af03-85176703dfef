<?php

namespace App\Document;

use App\Document\Embedded\Brand;
use App\Document\Embedded\Document;
use App\Document\Embedded\EmbeddedClassification;
use App\Document\Embedded\EmbeddedIdentifier;
use App\Document\Embedded\Image;
use App\Document\Embedded\ProductRequirement;
use App\Document\Embedded\RequirementResponse;
use App\Repository\ProductRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document(repositoryClass: ProductRepository::class)]
class Product
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $prozorroId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $owner = null;

    #[MongoDB\EmbedOne(nullable: true, targetDocument: EmbeddedClassification::class)]
    private ?EmbeddedClassification $classification = null;

    #[MongoDB\EmbedOne(targetDocument: EmbeddedIdentifier::class)]
    private ?EmbeddedIdentifier $identifier = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $status = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\EmbedMany(targetDocument: Image::class)]
    private Collection|array $images;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::DESC)]
    private ?\DateTimeInterface $dateModified = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    #[MongoDB\Index(order: Criteria::ASC)]
    protected ?\DateTimeInterface $dateCreated = null;

    #[MongoDB\Field(type: Type::DATE, nullable: true)]
    protected ?\DateTimeInterface $expirationDate = null;

    #[MongoDB\Field(type: Type::RAW)]
    private array $relatedProfiles = []; // id з прозорро

    #[MongoDB\Field(type: Type::RAW)]
    private array $relatedProfileIds = []; // внутрішні id

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $relatedCategory = null; // id з прозорро

    #[MongoDB\Field(type: Type::ID, nullable: true)]
    private ?string $relatedCategoryId = null; // внутрішній id

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $relatedCategoryStatus = null;

    #[MongoDB\EmbedOne(nullable: true, targetDocument: Brand::class)]
    private ?Brand $brand = null;

    #[MongoDB\EmbedMany(targetDocument: RequirementResponse::class)]
    private Collection|array $requirementResponses;

    #[MongoDB\EmbedMany(targetDocument: ProductRequirement::class)]
    private Collection|array $productRequirements;

    #[MongoDB\Field(type: Type::RAW)]
    private array $manufacturers = [];

    #[MongoDB\Field(type: Type::RAW)]
    private array $additionalProperties = [];

    /**
     * @var Collection|EmbeddedClassification[]
     */
    #[MongoDB\Field(type: Type::RAW, nullable: true)]
    private mixed $additionalClassifications = null;

    /**
     * @var Collection|EmbeddedIdentifier[]
     */
    #[MongoDB\EmbedMany(targetDocument: EmbeddedIdentifier::class)]
    private Collection|array $alternativeIdentifiers;

    #[MongoDB\Field(type: Type::RAW)]
    private $vendor;

    /**
     * @var Collection|Document[]
     */
    #[MongoDB\EmbedMany(targetDocument: Document::class)]
    protected Collection|array $documents;

    #[MongoDB\Field(type: Type::RAW)]
    private $product;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private int $popularity = 0;

    #[MongoDB\Field(type: Type::BOOL, nullable: true)]
    private ?bool $dzoCategoryDataTypesMatch = null;

    public function __construct()
    {
        $this->images = new ArrayCollection();
        $this->productRequirements = new ArrayCollection();
        $this->alternativeIdentifiers = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->requirementResponses = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getProzorroId(): ?string
    {
        return $this->prozorroId;
    }

    public function setProzorroId(?string $prozorroId): void
    {
        $this->prozorroId = $prozorroId;
    }

    public function getOwner(): ?string
    {
        return $this->owner;
    }

    public function setOwner(?string $owner): void
    {
        $this->owner = $owner;
    }

    public function getClassification(): ?EmbeddedClassification
    {
        return $this->classification;
    }

    public function setClassification(?EmbeddedClassification $classification): void
    {
        $this->classification = $classification;
    }

    public function getIdentifier(): ?EmbeddedIdentifier
    {
        return $this->identifier;
    }

    public function setIdentifier(?EmbeddedIdentifier $identifier): void
    {
        $this->identifier = $identifier;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): void
    {
        $this->status = $status;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getImages(): Collection|array
    {
        return $this->images;
    }

    public function setImages(Collection|array $images): void
    {
        $this->images = $images;
    }

    public function addImage(Image $image): void
    {
        if (!$this->images->contains($image)) {
            $this->images->add($image);
        }
    }

    public function getDateModified(): ?\DateTimeInterface
    {
        return $this->dateModified;
    }

    public function setDateModified(?\DateTimeInterface $dateModified): void
    {
        $this->dateModified = $dateModified;
    }

    public function getRelatedProfiles(): array
    {
        return $this->relatedProfiles;
    }

    public function setRelatedProfiles(array $relatedProfiles): void
    {
        $this->relatedProfiles = $relatedProfiles;
    }

    public function getBrand(): ?Brand
    {
        return $this->brand;
    }

    public function setBrand(?Brand $brand): void
    {
        $this->brand = $brand;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    public function setDateCreated(?\DateTimeInterface $dateCreated): void
    {
        $this->dateCreated = $dateCreated;
    }

    public function getManufacturers(): array
    {
        return $this->manufacturers;
    }

    public function setManufacturers(array $manufacturers): void
    {
        $this->manufacturers = $manufacturers;
    }

    public function getAdditionalProperties(): array
    {
        return $this->additionalProperties;
    }

    public function setAdditionalProperties(array $additionalProperties): void
    {
        $this->additionalProperties = $additionalProperties;
    }

    public function getTextRequirements()
    {
        $result = [];
        /* @var ProductRequirement $value */
        foreach ($this->productRequirements as $pRequirement) {
            if ('text' === $pRequirement->getType()) {
                $result[] = $pRequirement;
            }
        }

        return $result;
    }

    public function getNumberRequirements()
    {
        $result = [];
        /* @var ProductRequirement $value */
        foreach ($this->productRequirements as $pRequirement) {
            if ('number' === $pRequirement->getType()) {
                $result[] = $pRequirement;
            }
        }

        return $result;
    }

    public function getBooleanRequirements()
    {
        $result = [];
        /* @var ProductRequirement $value */
        foreach ($this->productRequirements as $pRequirement) {
            if ('boolean' === $pRequirement->getType()) {
                $result[] = $pRequirement;
            }
        }

        return $result;
    }

    public function getProductRequirements(): Collection|array
    {
        return $this->productRequirements;
    }

    public function setProductRequirements(Collection|array $productRequirements): void
    {
        $this->productRequirements = $productRequirements;
    }

    public function addProductRequirement(ProductRequirement $productRequirement): void
    {
        if (!$this->productRequirements->contains($productRequirement)) {
            $this->productRequirements->add($productRequirement);
        }
    }

    public function getRelatedProfileIds(): array
    {
        return $this->relatedProfileIds;
    }

    public function setRelatedProfileIds(array $relatedProfileIds): void
    {
        $this->relatedProfileIds = $relatedProfileIds;
    }

    public function getAlternativeIdentifiers(): Collection|array
    {
        return $this->alternativeIdentifiers;
    }

    public function setAlternativeIdentifiers(Collection|array $alternativeIdentifiers): void
    {
        $this->alternativeIdentifiers = $alternativeIdentifiers;
    }

    public function getDocuments(): Collection|array
    {
        return $this->documents;
    }

    public function setDocuments(Collection|array $documents): void
    {
        $this->documents = $documents;
    }

    public function getProduct()
    {
        return $this->product;
    }

    public function setProduct($product): void
    {
        $this->product = $product;
    }

    public function getVendor()
    {
        return $this->vendor;
    }

    public function setVendor($vendor): void
    {
        $this->vendor = $vendor;
    }

    public function getRelatedCategory(): ?string
    {
        return $this->relatedCategory;
    }

    public function setRelatedCategory(?string $relatedCategory): Product
    {
        $this->relatedCategory = $relatedCategory;

        return $this;
    }

    public function getRelatedCategoryId(): ?string
    {
        return $this->relatedCategoryId;
    }

    public function setRelatedCategoryId(?string $relatedCategoryId): Product
    {
        $this->relatedCategoryId = $relatedCategoryId;

        return $this;
    }

    public function getAdditionalClassifications(): mixed
    {
        return $this->additionalClassifications;
    }

    public function setAdditionalClassifications(mixed $additionalClassifications): Product
    {
        $this->additionalClassifications = $additionalClassifications;

        return $this;
    }

    public function getRelatedCategoryStatus(): ?string
    {
        return $this->relatedCategoryStatus;
    }

    public function setRelatedCategoryStatus(?string $relatedCategoryStatus): Product
    {
        $this->relatedCategoryStatus = $relatedCategoryStatus;

        return $this;
    }

    public function getExpirationDate(): ?\DateTimeInterface
    {
        return $this->expirationDate;
    }

    public function setExpirationDate(?\DateTimeInterface $expirationDate): Product
    {
        $this->expirationDate = $expirationDate;
        return $this;
    }

    public function getPopularity(): int
    {
        return $this->popularity;
    }

    public function setPopularity(int $popularity): Product
    {
        $this->popularity = $popularity;
        return $this;
    }

    public function getRequirementResponses(): Collection|array
    {
        return $this->requirementResponses;
    }

    public function setRequirementResponses(Collection|array $requirementResponses): void
    {
        $this->requirementResponses = $requirementResponses;
    }

    public function addRequirementResponse(RequirementResponse $requirementResponse): void
    {
        if (!$this->requirementResponses->contains($requirementResponse)) {
            $this->requirementResponses->add($requirementResponse);
        }
    }
    public function isDzoCategoryDataTypesMatch(): ?bool
    {
        return $this->dzoCategoryDataTypesMatch;
    }

    public function setDzoCategoryDataTypesMatch(bool $dzoCategoryDataTypesMatch): void
    {
        $this->dzoCategoryDataTypesMatch = $dzoCategoryDataTypesMatch;
    }
}
