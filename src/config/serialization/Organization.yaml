App\Document\Embedded\Organization:
  attributes:
    id:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    name:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    identifier:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    additionalIdentifiers:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    address:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    contactPoint:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    additionalContactPoin:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    scale:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]
    signerInfo:
      groups: [ 'tender:list', 'tender:read', 'tender:cdb_parse' ]

