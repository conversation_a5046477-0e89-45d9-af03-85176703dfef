<?php

namespace App\Filter;

use ApiPlatform\Api\IriConverterInterface;
use ApiPlatform\Api\ResourceClassResolverInterface;
use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\Metadata\Property\Factory\PropertyMetadataFactoryInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyNameCollectionFactoryInterface;
use App\Utils\CurrentUserService;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;
use App\Resource\TenderView;

class FavoriteTenderFilter extends AbstractSearchFilter
{
    public function __construct(
        private CurrentUserService $currentUserService,
        PropertyNameCollectionFactoryInterface $propertyNameCollectionFactory,
        PropertyMetadataFactoryInterface $propertyMetadataFactory,
        ResourceClassResolverInterface $resourceClassResolver,
        IriConverterInterface $iriConverter,
        PropertyAccessorInterface $propertyAccessor,
        ?NameConverterInterface $nameConverter = null,
        ?array $properties = null
    ) {
        parent::__construct($propertyNameCollectionFactory, $propertyMetadataFactory, $resourceClassResolver, $iriConverter, $propertyAccessor, $nameConverter, $properties);
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $termQuery = ['term' => [$property => $this->currentUserService->userIdAsString()]];

        if (null !== $nestedPath) {
            $termQuery = ['nested' => ['path' => $nestedPath, 'query' => $termQuery]];
        }

        return $termQuery;
    }

    public function apply(array $clauseBody, string $resourceClass, ?Operation $operationName = null, array $context = []): array
    {
        $favoriteQueryConditions = [];
        $isFavoriteActive = false;

        foreach ($context['filters'] ?? [] as $property => $value) {

            if (!isset($this->properties[$property])) {
                continue;
            }
            if ($property !== 'isFavorite') {
                continue;
            }
            if ('1' !== $value && 'true' !== $value) {
                continue;
            }

            $isFavoriteActive = true;
            $searchProperty = $this->properties[$property];
            $favoriteQueryConditions[] = $this->getQuery($searchProperty, [$value], null);
            break;
        }

        if (!$isFavoriteActive) {
            return $clauseBody;
        }

        $boolQuery = $clauseBody['bool'] ?? [];
        $mustClauses = array_merge($boolQuery['must'] ?? [], $favoriteQueryConditions);
        $mustNotClauses = $boolQuery['must_not'] ?? [];
        $filterClauses = $boolQuery['filter'] ?? [];

        $filterClauses[] = [
            'terms' => ['status' => ['active.enquiries', 'active.tendering']]
        ];

        $currentUserId = $this->currentUserService->userIdAsString();
        if ('' !== $currentUserId) {
            $mustNotClauses[] = [
                'nested' => [
                    'path' => 'dzoData.bids',
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['dzoData.bids.userId' => $currentUserId]],
                                ['exists' => ['field' => 'dzoData.bids.apiId']]
                            ]
                        ]
                    ]
                ]
            ];
        }

        unset($clauseBody['bool']);
        $newBoolQuery = [];
        if (!empty($mustClauses)) $newBoolQuery['must'] = $mustClauses;
        if (!empty($mustNotClauses)) $newBoolQuery['must_not'] = $mustNotClauses;
        if (!empty($filterClauses)) $newBoolQuery['filter'] = $filterClauses;

        if (!empty($newBoolQuery)) {
            $clauseBody['bool'] = $newBoolQuery;
        }

        return $clauseBody;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];
        if ($this->properties) {
            foreach ($this->properties as $key => $related) {
                if ($key === 'isFavorite') {
                    $description[$key] = [
                        'property' => $key,
                        'type' => 'boolean',
                        'required' => false,
                    ];
                }
            }
        }
        return $description;
    }
}
