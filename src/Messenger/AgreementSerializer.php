<?php

namespace App\Messenger;

use App\Message\AgreementMessage;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\MessageDecodingFailedException;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;

class AgreementSerializer implements SerializerInterface
{
    public function decode(array $encodedEnvelope): Envelope
    {
        $body = $encodedEnvelope['body'];

        try {
            $data = json_decode($body, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new MessageDecodingFailedException('invalid json');
        }

        $contract = new AgreementMessage($data['id']);

        return new Envelope($contract);
    }

    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();
        if ($message instanceof AgreementMessage) {
            $data = ['id' => $message->getId()];
        } else {
            throw new \Exception('Unsupported message class');
        }

        return [
            'body' => json_encode($data),
            'headers' => [],
        ];
    }
}
