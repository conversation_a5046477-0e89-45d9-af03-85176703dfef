<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\ContractDataProvider;
use App\Document\Contract;
use App\Filter\BuyerFilter;
use App\Filter\ClassificationFilter;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;
use App\Filter\OrderFilter;
use App\Filter\TestModeFilter;
use App\Filter\ValueRangeFilter;
use App\Resource\Embedded\BuyerView;
use App\Resource\Embedded\DocumentView;
use App\Resource\Embedded\ItemView;
use App\Resource\Embedded\OrganizationView;
use App\Resource\Embedded\PeriodView;
use App\Resource\Embedded\ProcuringEntityView;
use App\Resource\Embedded\ValueView;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ApiResource(
    shortName: 'contract',
    operations: [
        new GetCollection(provider: CollectionProvider::class, stateOptions: new Options(index: 'dzo_contract')),
        new Get(provider: ContractDataProvider::class),
    ],
    order: ['dateSigned' => 'DESC']
)]
#[ApiFilter(OrderFilter::class, properties: [
    'dateSigned',
    'dateCreated',
    'value.amount',
], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(MatchFilter::class, properties: [
    'status' => 'exact',
    'items.deliveryAddress.region' => 'exact',
    'procurementMethodTypeFilter' => 'exact',
    'tenderId' => 'exact',
])]
#[ApiFilter(KeywordFilter::class, properties: [
    'contractIdText',
    'title',
    'items.classification.description',
    'items.description',
    'items.descriptionEn',
])]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification' => 'items.classification.id',
    'supplier' => 'suppliers.identifier.id',
    'additionalClassification' => 'items.additionalClassifications.id',
])]
#[ApiFilter(BuyerFilter::class, properties: [
    'procuringEntity' => 'procuringEntity.identifier.id',
    'buyer' => 'buyer.identifier.id',
])]
#[ApiFilter(ValueRangeFilter::class, properties: ['value'])]
#[ApiFilter(TestModeFilter::class, properties: ['testMode'])]
readonly class ContractView
{
    public ?string $id;

    #[SerializedName('is_masked')]
    public ?bool $isMasked;

    public ?string $mode;

    public ?string $status;

    public ?ValueView $value;

    /**
     * @var DocumentView[]
     */
    public array $documents;

    #[SerializedName('tender_id')]
    public ?string $tenderId;

    /**
     * @var ItemView[]
     */
    public array $items;

    /**
     * @var OrganizationView[]
     */
    public array $suppliers;

    public ?string $contractNumber;

    public ?PeriodView $period;

    public mixed $dateSigned;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    public ?ProcuringEntityView $procuringEntity;

    public ?BuyerView $buyer;

    public ?string $owner;

    #[SerializedName('awardID')]
    public ?string $awardId;

    #[SerializedName('contractID')]
    public ?string $contractId;

    public ?string $title;

    public ?string $description;

    public mixed $date;

    public mixed $changes;

    public ?ValueView $amountPaid;

    public mixed $terminationDetails;

    public ?string $procurementMethodType;

    public ?string $procurementMethodRationale;

    #[SerializedName('bid_owner')]
    public ?string $bidOwner;

    public bool $testMode;

    /**
     * ContractView constructor.
     */
    public function __construct(Contract $contract)
    {
        $config = $contract->getContractConfig();
        if (is_array($config) && array_key_exists('restricted', $config) && $config['restricted']) {
            $contract->setRestrictedMode(true);
        }

        $this->id = $contract->getId();
        $this->isMasked = $contract->getIsMasked();
        $this->mode = $contract->getMode();
        $this->status = $contract->getStatus();

        $value = $contract->getValue();
        if ($value) {
            $this->value = new ValueView($value);
        }

        $documents = [];
        foreach ($contract->getDocuments() as $document) {
            $documents[] = new DocumentView($document);
        }
        $this->documents = [...$documents];

        $this->tenderId = $contract->getTenderId();

        $items = [];
        foreach ($contract->getItems() as $item) {
            $items[] = new ItemView($item);
        }
        $this->items = [...$items];

        $suppliers = [];
        foreach ($contract->getSuppliers() as $supplier) {
            $suppliers[] = new OrganizationView($supplier);
        }
        $this->suppliers = [...$suppliers];

        $this->contractNumber = $contract->getContractNumber();

        $period = $contract->getPeriod();
        if ($period) {
            $this->period = new PeriodView($period);
        }

        $this->dateSigned = $contract->getDateSigned();
        $this->dateModified = $contract->getDateModified();
        $this->dateCreated = $contract->getDateCreated();

        $procuringEntity = $contract->getProcuringEntity();
        if ($procuringEntity) {
            $this->procuringEntity = new ProcuringEntityView($procuringEntity);
        }

        $buyer = $contract->getBuyer();
        if ($buyer) {
            $this->buyer = new BuyerView($buyer);
        }

        $this->owner = $contract->getOwner();
        $this->bidOwner = $contract->getBidOwner();
        $this->awardId = $contract->getAwardId();
        $this->contractId = $contract->getContractId();
        $this->title = $contract->getTitle();
        $this->description = $contract->getDescription();
        $this->date = $contract->getDate();
        $this->changes = $contract->getChanges();

        $amountPaid = $contract->getAmountPaid();
        if ($amountPaid) {
            $this->amountPaid = new ValueView($amountPaid);
        }

        $this->terminationDetails = $contract->getTerminationDetails();
        $this->procurementMethodType = $contract->getProcurementMethodType();
        $this->procurementMethodRationale = $contract->getProcurementMethodRationale();
    }
}
