<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Unit
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $code = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $name = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $symbol = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): void
    {
        $this->code = $code;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getSymbol(): ?string
    {
        return $this->symbol;
    }

    public function setSymbol(?string $symbol): void
    {
        $this->symbol = $symbol;
    }
}
