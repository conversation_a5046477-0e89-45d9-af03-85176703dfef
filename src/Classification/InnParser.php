<?php

namespace App\Classification;

use App\Document\Classification;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class InnParser.
 */
class InnParser extends ClassificationParser
{
    protected string $scheme = 'INN';

    /**
     * @throws GuzzleException
     * @throws \Doctrine\ODM\MongoDB\LockException
     * @throws \Doctrine\ODM\MongoDB\Mapping\MappingException
     * @throws \Doctrine\ODM\MongoDB\MongoDBException
     */
    public function parse(array $urls): void
    {
        $url = $urls[0];

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment('Starting with '.$this->scheme);
        }
        try {
            $response = $this->client->request('GET', $url);
        } catch (GuzzleException $exception) {
            $this->logger->error($exception->getMessage(), [$exception]);
            throw $exception;
        }

        $result = json_decode((string) $response->getBody(), true);
        $result = $result['data'];
        $totalDocuments = count($result);

        if (null !== $this->inputOutput) {
            $this->inputOutput->comment($totalDocuments.' items found...');
            $this->inputOutput->progressStart($totalDocuments);
        }

        $documentsCount = 0;
        $parentClassifications = [];

        foreach ($result as $id => $subIds) {
            $parentDescription = strtoupper($id[0]);
            $parentId = $this->scheme.$parentDescription;
            $this->buildParentClassification($parentId, $parentDescription, '', $parentClassifications);

            foreach ($subIds as $subId) {
                $subDocument = new Classification();
                $subDocument->setScheme($this->scheme);
                $subDocument->setId($subId);
                $subDocument->setDescription($subId);
                $subDocument->setParentId($id);
                $subDocument->setSelectable(true);
                $subDocument->setTreeLevel(2);
                $this->manager->save($subDocument, false);
            }

            $document = new Classification();
            $document->setScheme($this->scheme);
            $document->setId($id);
            $document->setDescription($id);
            $document->setParentId($parentId);
            $document->setSelectable(true);
            $document->setTreeLevel(1);
            $document->setChildren(true);

            ++$documentsCount;

            $needFlush = (0 === $documentsCount % 50) || ($documentsCount === $totalDocuments);
            $this->manager->save($document, $needFlush);
            if (null !== $this->inputOutput) {
                $this->inputOutput->progressAdvance(1);
            }
        }
    }
}
