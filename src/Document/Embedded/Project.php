<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class Project
{
    #[MongoDB\Id(strategy: 'NONE')]
    private ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $title = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $uri = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getUri(): ?string
    {
        return $this->uri;
    }

    public function setUri(?string $uri): void
    {
        $this->uri = $uri;
    }
}
