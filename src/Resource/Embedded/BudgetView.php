<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Budget;

/**
 * Class BudgetView.
 */
class BudgetView
{
    private ?string $id = null;

    private ?string $description = null;

    private ?float $amount = null;

    private ?float $amountNet = null;

    private ?string $currency = null;

    private $project;

    private ?PeriodView $period = null;

    private ?int $year = null;

    private ?string $notes = null;

    /**
     * @var BudgetBreakdownView[]
     */
    private array $breakdown;

    /**
     * BudgetView constructor.
     */
    public function __construct(Budget $budget)
    {
        $this->id = $budget->getId();
        $this->description = $budget->getDescription();
        $this->amount = $budget->getAmount();
        $this->amountNet = $budget->getAmountNet();
        $this->currency = $budget->getCurrency();
        $this->project = $budget->getProject();

        $period = $budget->getPeriod();
        if ($period) {
            $this->period = new PeriodView($period);
        }

        $this->year = $budget->getYear();
        $this->notes = $budget->getNotes();

        $this->breakdown = [];
        foreach ($budget->getBreakdown() as $budgetBreakdown) {
            $this->breakdown[] = new BudgetBreakdownView($budgetBreakdown);
        }
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function getAmountNet(): ?float
    {
        return $this->amountNet;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function getProject()
    {
        return $this->project;
    }

    /**
     * @return PeriodView|null
     */
    public function getPeriod()
    {
        return $this->period;
    }

    public function getYear(): ?int
    {
        return $this->year;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    /**
     * @return BudgetBreakdownView[]
     */
    public function getBreakdown()
    {
        return $this->breakdown;
    }
}
