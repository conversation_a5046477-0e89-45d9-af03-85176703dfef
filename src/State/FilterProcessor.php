<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Document\Filter;
use App\Repository\FilterRepository;
use App\Resource\FilterView;
use App\Security\JwtCheckerTrait;

class FilterProcessor implements ProcessorInterface
{
    use JwtCheckerTrait;

    public function __construct(
        private readonly FilterRepository $filterRepository
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        if (!$userId = $this->checkJwt()) {
            return null;
        }

        $filter = new Filter();
        $filter->setUserId($userId);
        $filter->setName($data->name);
        $filter->setType($data->type);
        $filter->setFilter($data->filter);
        $filter->setCreateDate(new \DateTime());

        $this->filterRepository->save($filter);

        return new FilterView($filter);
    }
}
