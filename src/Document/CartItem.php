<?php

namespace App\Document;

use App\Repository\CartItemRepository;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document(repositoryClass: CartItemRepository::class)]
class CartItem
{
    #[MongoDB\Id]
    private string $id;

    #[MongoDB\Field(type: Type::INT)]
    private int $quantity = 0;

    #[MongoDB\ReferenceOne(targetDocument: Cart::class, inversedBy: 'cartItems')]
    private Cart $cart;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $profileId = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $categoryId = null;

    #[MongoDB\Field(type: Type::RAW)]
    private ?array $config = null;

    public function getId(): string
    {
        return $this->id;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): void
    {
        $this->quantity = $quantity;
    }

    public function getProfileId(): ?string
    {
        return $this->profileId;
    }

    public function setProfileId(?string $profileId): void
    {
        $this->profileId = $profileId;
    }

    public function getCart(): Cart
    {
        return $this->cart;
    }

    public function setCart(Cart $cart): void
    {
        $this->cart = $cart;
    }

    public function getConfig(): ?array
    {
        return $this->config;
    }

    public function setConfig(?array $config): void
    {
        $this->config = $config;
    }

    public function getCategoryId(): ?string
    {
        return $this->categoryId;
    }

    public function setCategoryId(?string $categoryId): CartItem
    {
        $this->categoryId = $categoryId;

        return $this;
    }
}
