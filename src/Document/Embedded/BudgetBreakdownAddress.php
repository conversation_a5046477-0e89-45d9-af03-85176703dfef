<?php

namespace App\Document\Embedded;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class BudgetBreakdownAddress
{
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $streetAddress = null;
    
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $locality = null;
    
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $postalCode = null;
    
    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    private ?string $countryName = null;
    
    /**
     * @var Collection|BudgetBreakdownAddressDetails[]
     */
    #[MongoDB\EmbedMany(targetDocument: BudgetBreakdownAddressDetails::class)]
    private Collection|array $addressDetails;
    
    public function __construct()
    {
        $this->addressDetails = new ArrayCollection();
    }

    public function getStreetAddress(): ?string
    {
        return $this->streetAddress;
    }

    public function setStreetAddress(?string $streetAddress): void
    {
        $this->streetAddress = $streetAddress;
    }

    public function getLocality(): ?string
    {
        return $this->locality;
    }

    public function setLocality(?string $locality): void
    {
        $this->locality = $locality;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getCountryName(): ?string
    {
        return $this->countryName;
    }

    public function setCountryName(?string $countryName): void
    {
        $this->countryName = $countryName;
    }

    /**
     * @return Collection|BudgetBreakdownAddressDetails[]
     */
    public function getAddressDetails(): Collection
    {
        return $this->addressDetails;
    }

    public function addAddressDetail(BudgetBreakdownAddressDetails $addressDetail): void
    {
        if (!$this->addressDetails->contains($addressDetail)) {
            $this->addressDetails->add($addressDetail);
        }
    }

    public function removeAddressDetail(BudgetBreakdownAddressDetails $addressDetail): void
    {
        $this->addressDetails->removeElement($addressDetail);
    }
}
