<?php

namespace App\Service;

use App\Document\Cart;
use App\Document\Category;
use App\Document\Profile;
use App\Manager\CategoryManager;
use App\Manager\ProfileManager;
use App\Resource\Embedded\CartItemView;

class CartItemService
{
    public function __construct(
        private readonly ProfileManager $profileManager,
        private readonly CategoryManager $categoryManager
    ) {
    }

    public function getCartItems(Cart $cart): array
    {
        $items = [];
        foreach ($cart->getCartItems() as $cartItem) {
            if ($cartItem->getProfileId()) {
                /** @var Profile $profile */
                $profile = $this->profileManager->find($cartItem->getProfileId());
                $classification = $profile->getClassification();
                $agreementId = $profile->getAgreementID();
                $cartItemView = new CartItemView($cartItem, $profile->getTitle(), $classification->getId());
            }

            if ($cartItem->getCategoryId()) {
                /** @var Category $category */
                $category = $this->categoryManager->find($cartItem->getCategoryId());
                $classification = $category->getClassification();
                $agreementId = $category->getAgreementID();
                $cartItemView = new CartItemView($cartItem, $category->getTitle(), $classification->getId());
            }

            if (!isset($items[$agreementId])) {
                $items[$agreementId] = [
                    'id' => $classification->getId(),
                    'title' => $classification->getDescription(),
                    'scheme' => $classification->getScheme(),
                ];
            }
            $items[$agreementId]['items'][] = $cartItemView;
        }

        return $items;
    }
}
