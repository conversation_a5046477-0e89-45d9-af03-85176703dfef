<?php

namespace App\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\Document]
class MarketClassification
{
    #[MongoDB\Id(strategy: 'NONE')]
    protected ?string $id = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $description = null;

    #[MongoDB\Field(type: Type::STRING, nullable: true)]
    protected ?string $scheme = null;

    #[MongoDB\Field(type: Type::INT)]
    protected int $activeCategories = 0;

    #[MongoDB\Field(type: Type::INT, nullable: true)]
    private int $popularity = 0;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getScheme(): ?string
    {
        return $this->scheme;
    }

    public function setScheme(?string $scheme): void
    {
        $this->scheme = $scheme;
    }

    public function getActiveCategories(): int
    {
        return $this->activeCategories;
    }

    public function setActiveCategories(int $activeCategories): MarketClassification
    {
        $this->activeCategories = $activeCategories;
        return $this;
    }

    public function getPopularity(): int
    {
        return $this->popularity;
    }

    public function setPopularity(int $popularity): MarketClassification
    {
        $this->popularity = $popularity;
        return $this;
    }
}
