<?php

namespace App\Filter;

use ApiPlatform\Api\ResourceClassResolverInterface;
use ApiPlatform\Elasticsearch\Filter\AbstractFilter;
use ApiPlatform\Elasticsearch\Filter\ConstantScoreFilterInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\Metadata\Property\Factory\PropertyMetadataFactoryInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyNameCollectionFactoryInterface;
use DateTime;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;

/**
 * Class BaseRangeFilter.
 */
abstract class BaseRangeFilter extends AbstractFilter implements ConstantScoreFilterInterface
{
    /** Field type for description^ date, float, integer, etc. */
    abstract public function getRangeFieldType(): string;

    public function getFilterProperties(): ?array
    {
        return $this->filterProperties;
    }

    public function setFilterProperties(?array $filterProperties): void
    {
        $this->filterProperties = $filterProperties;
    }

    /**
     * @param \DateTimeInterface|null $from
     * @param \DateTimeInterface|null $to
     */
    abstract public function getRangeQuery(string $property, $from = null, $to = null): array;

    public function __construct(
        PropertyNameCollectionFactoryInterface $propertyNameCollectionFactory,
        PropertyMetadataFactoryInterface $propertyMetadataFactory,
        ResourceClassResolverInterface $resourceClassResolver,
        ?NameConverterInterface $nameConverter = null,
        protected ?array $properties = null
    ) {
        parent::__construct($propertyNameCollectionFactory, $propertyMetadataFactory, $resourceClassResolver, $nameConverter, $properties);
        $this->filterProperties = array_keys($properties);
    }

    /**
     * @todo update? remove? replace with getRangeQuery?
     */
    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $matches = [];

        foreach ($values as $value) {
            $matches[] = ['match' => [$property => $value]];
        }

        $matchQuery = isset($matches[1]) ? ['bool' => ['should' => $matches]] : $matches[0];

        if (null !== $nestedPath) {
            $matchQuery = ['nested' => ['path' => $nestedPath, 'query' => $matchQuery]];
        }

        return $matchQuery;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];
        foreach ($this->getProperties(self::class) as $property) {
            $description[$property.'.from'] = [
                'property' => $property.'.from',
                'type' => $this->getRangeFieldType(),
                'required' => false,
            ];
            $description[$property.'.to'] = [
                'property' => $property.'.to',
                'type' => $this->getRangeFieldType(),
                'required' => false,
            ];
        }

        return $description;
    }

    /**
     * @throws \Exception
     *
     * @todo move to base class
     * @todo refactor
     */
    public function apply(array $clauseBody, string $resourceClass, ?Operation $operationName = null, array $context = []): array
    {
        $searches = [];
        $ranges = [];
        foreach ($context['filters'] ?? [] as $property => $value) {
            $keys = explode('.', $property);

            if (count($keys) < 2) {
                continue;
            }

            $rangeKey = array_pop($keys);
            if ('from' !== $rangeKey && 'to' !== $rangeKey) {
                continue;
            }

            $searchKey = implode('.', $keys);
            if (!in_array($searchKey, $this->getFilterProperties())) {
                continue;
            }
            $ranges[$searchKey][$rangeKey] = $value; // new DateTime($value);
        }

        foreach ($ranges as $rangeField => $rangeKeys) {
            $from = (array_key_exists('from', $rangeKeys)) ? $rangeKeys['from'] : null;
            $to = (array_key_exists('to', $rangeKeys)) ? $rangeKeys['to'] : null;

            if ('date' === $this->getRangeFieldType()) {
                if ($from) {
                    $from = new \DateTime(str_replace(' ', '+', trim($rangeKeys['from'])));
                    $from = new \DateTime(
                        $from->format('Y-m-d').'T00:00:00Z'
                    );
                }

                if ($to) {
                    $to = new \DateTime(str_replace(' ', '+', trim($rangeKeys['to'])));
                    $to = new \DateTime(
                        $to->format('Y-m-d').'T23:59:59Z'
                    );
                }
            }

            $searches[] = $this->getRangeQuery($rangeField, $from, $to);
        }

        //        foreach ($context['filters'] ?? [] as $property => $values) {
        //            [$type, $hasAssociation, $nestedResourceClass, $nestedProperty] = $this->getMetadata($resourceClass, $property);
        //
        // //            if ($property === self::KEY) {
        // //                $searches[] = $this->getQuery($property, (array)$values, '');
        // //                continue;
        // //            }
        //
        //            if (!$type || !$values = (array)$values) {
        //                continue;
        //            }
        //
        //            if ($hasAssociation || $this->isIdentifier($nestedResourceClass, $nestedProperty)) {
        //                $values = array_map([$this, 'getIdentifierValue'], $values, array_fill(0, \count($values), $nestedProperty));
        //            }
        //
        //            if (!$this->hasValidValues($values, $type)) {
        //                continue;
        //            }
        //
        //            $property = null === $this->nameConverter ?
        //                $property : $this->nameConverter->normalize($property, $resourceClass, null, $context);
        //            $nestedPath = $this->getNestedFieldPath($resourceClass, $property);
        //            $nestedPath = null === $nestedPath ||
        //            null === $this->nameConverter ?
        //                $nestedPath : $this->nameConverter->normalize($nestedPath, $resourceClass, null, $context);
        //
        //            $searches[] = $this->getQuery($property, $values, $nestedPath);
        //        }
        //
        //        if (!$searches) {
        //            return $clauseBody;
        //        }

        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches,
            ],
        ]);
        //        Uncomment to debug query:
        //        echo json_encode($query);exit;
        //        die(var_dump($query));

        return $query;
    }
}
