<?php

namespace App\Serializer\Normalizer;

use <PERSON>ymfony\Component\Serializer\Exception\NotNormalizableValueException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

/**
 * Class DateTimeNormalizer.
 */
readonly class NullableDateTimeNormalizer implements DenormalizerInterface
{
    public function __construct(private DenormalizerInterface $denormalizer)
    {
    }

    /**
     * @throws NotNormalizableValueException
     */
    public function denormalize($data, string $type, ?string $format = null, array $context = []): \DateTimeInterface
    {
        try {
            return ('' === $data || null === $data) ? new \DateTime() : new \DateTime($data);
        } catch (\Exception $e) {
            throw new NotNormalizableValueException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = [],
    ): bool {
        if ('' === $data || null === $data) {
            return $this->denormalizer->supportsDenormalization($data, $type, $format);
        }

        return false;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            'object' => true,
        ];
    }
}
