<?php

namespace App\Legacy\Repository;

use App\Legacy\Helper\DatabaseHelper;

class LegacyUserRepository extends LegacyRepository
{
    public function getUserByNewId(string $userId)
    {
        $query = $this->connection->prepare(
            'SELECT u.* 
            FROM '.DatabaseHelper::getTableName('users').' AS u
            LEFT JOIN '.DatabaseHelper::getTableName('users_new_dzo').' as und ON u.id = und.user_id
            WHERE und.new_user_id = :newUserId'
        );

        $query->bindValue('newUserId', $userId);

        return $query->executeQuery()->fetchAssociative();
    }
}
