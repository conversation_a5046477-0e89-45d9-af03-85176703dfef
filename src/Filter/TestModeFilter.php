<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

final class TestModeFilter extends AbstractSearchFilter
{
    public const FIELD = 'mode';
    public const FIELD_VALUE = 'test';

    private function getName(): string
    {
        return 'testMode';
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        return [
            'term' => [
                self::FIELD => self::FIELD_VALUE,
            ],
        ];
    }

    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operationName = null,
        array $context = []
    ): array {
        $match = $search = null;
        foreach ($context['filters'] ?? [] as $property => $values) {
            if ($property !== $this->getName()) {
                continue;
            }

            $match = filter_var($values, FILTER_VALIDATE_BOOLEAN) ? 'must' : 'must_not';
            $search = $this->getQuery($property, (array) $values, false);
        }

        if ($search) {
            return array_merge_recursive($clauseBody, [
                'bool' => [
                    $match => [$search],
                ],
            ]);
        }

        return $clauseBody;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        $description['testMode'] = [
            'property' => $this->getName(),
            'type' => 'bool',
            'required' => false,
            'openapi' => [
                'example' => false,
            ],
        ];

        return $description;
    }
}
