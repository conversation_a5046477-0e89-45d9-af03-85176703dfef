# Please change elasticsearch url to proper one before applying this migration (!)

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_product/_mapping?pretty" -H 'Content-Type: application/json' -d'
{
  "properties": {
    "popularity": {
      "type": "integer"
    }
  }
}
'

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_profile/_mapping?pretty" -H 'Content-Type: application/json' -d'
{
  "properties": {
    "popularity": {
      "type": "integer"
    }
  }
}
'

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_category/_mapping?pretty" -H 'Content-Type: application/json' -d'
{
  "properties": {
    "popularity": {
      "type": "integer"
    }
  }
}
'

curl -X PUT "https://opensearch-stage.dzo.com.ua/dzo_market_classification/_mapping?pretty" -H 'Content-Type: application/json' -d'
{
  "properties": {
    "popularity": {
      "type": "integer"
    }
  }
}
'
