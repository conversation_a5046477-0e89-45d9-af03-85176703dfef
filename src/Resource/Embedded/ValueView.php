<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Value;

/**
 * Class ValueView.
 */
class ValueView
{
    private ?string $id = null;

    private ?string $currency = null;

    private ?float $amount = null;

    private ?bool $valueAddedTaxIncluded = true;

    /**
     * ValueView constructor.
     */
    public function __construct(Value $value)
    {
        $this->id = $value->getId();
        $this->currency = $value->getCurrency();
        $this->amount = $value->getAmount();
        $this->valueAddedTaxIncluded = $value->getValueAddedTaxIncluded();
    }

    public function __toString(): string
    {
        return (string) $this->amount.' '.$this->currency;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function getValueAddedTaxIncluded(): ?bool
    {
        return $this->valueAddedTaxIncluded;
    }
}
