<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\DataProvider\ProductDataProvider;
use App\DataProvider\ProductProzorroIdDataProvider;
use App\Document\Agreement;
use App\Document\Category;
use App\Document\Product;
use App\Filter\ClassificationFilter;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;
use App\Filter\MnnClassificationFilter;
use App\Filter\OrderFilter;
use App\Filter\RequirementsProductFilter;
use App\Resource\Embedded\BrandView;
use App\Resource\Embedded\ImageView;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use App\Service\CriteriaFilterService;

#[ApiResource(
    shortName: 'product',
    operations: [
        new GetCollection(normalizationContext: ['groups' => 'product:list'],
            provider: CollectionProvider::class,
            stateOptions: new Options(index: 'dzo_product')),
        new Get(normalizationContext: ['groups' => 'product:read'],
            provider: ProductDataProvider::class,
            stateOptions: new Options(index: 'dzo_product')),
        new Get(uriTemplate: '/product-by-prozorro-id/{id}', normalizationContext: ['groups' => 'product:read'],
            provider: ProductProzorroIdDataProvider::class,
            stateOptions: new Options(index: 'dzo_product')),
    ],
    order: ['classification.id' => Criteria::ASC],
    paginationItemsPerPage: 10,
    paginationType: 'page'
)]
#[ApiFilter(OrderFilter::class, properties: [
    'id',
    'prozorroId',
    'dateCreated',
    'title',
    'classification.id',
    'popularity'
], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(KeywordFilter::class, properties: [
    'id',
    'prozorroId',
    'scheme',
    'description',
    'title.title',
])]
#[ApiFilter(MatchFilter::class, properties: [
    'relatedCategoryId' => 'exact',
    'relatedProfileIds' => 'exact',
])]
#[ApiFilter(RequirementsProductFilter::class, properties: [
    'textRequirements' => 'exact',
    'booleanRequirements' => 'exact',
    'numberRequirements' => 'exact',
])]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification' => 'classification.id',
])]
#[ApiFilter(MnnClassificationFilter::class, properties: [
    'additionalClassification' => [
        MnnClassificationFilter::REQUIREMENT => 'textRequirements.requirement',
        MnnClassificationFilter::VALUES => 'textRequirements.values',
    ],
])]
readonly class ProductView
{
    #[ApiProperty(identifier: true)]
    public ?string $id;

    public ?string $prozorroId;

    public ?MarketClassificationView $classification;
    public ?string $categoryId;

    public ?BaseIdentifierView $identifier;

    public ?string $status;

    public ?string $owner;

    public ?string $title;

    public ?string $description;

    /** @var ImageView[] */
    public array $images;

    public array $profileIds;

    public array $profiles;

    public array $categoryProfiles;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    public ?\DateTimeInterface $expirationDate;

    public ?BrandView $brand;

    public Collection|array $requirementResponses;

    public ?Collection $productRequirements;

    public ?array $manufacturers;

    public ?array $additionalProperties;

    public mixed $additionalClassifications;

    public ?CategoryView $category;

    public Collection|array $documents;

    public ?array $vendor;

    public function __construct(
        Product $product,
        array $profilesData,
        private CriteriaFilterService $criteriaFilterService,
        ?string $awsCdnUrl = null,
        ?Category $category = null,
        ?array $categoryProfilesData = null,
        ?Agreement $agreement = null,
    ) {
        $this->id = $product->getId();
        $this->prozorroId = $product->getProzorroId();
        $this->classification = $product->getClassification() ? new MarketClassificationView(
            $product->getClassification()
        ) : null;
        $this->categoryId = $product->getRelatedCategoryId();
        $this->identifier = $product->getIdentifier() ? new BaseIdentifierView($product->getIdentifier()) : null;
        $this->status = $product->getStatus();
        $this->owner = $product->getOwner();
        $this->title = $product->getTitle();
        $this->description = $product->getDescription();
        $this->profileIds = $product->getRelatedProfileIds();
        $this->productRequirements = $product->getProductRequirements();
        $this->additionalClassifications = $product->getAdditionalClassifications();
        if ($category) {
            $this->category = new CategoryView(
                category: $category,
                criteriaFilterService: $this->criteriaFilterService,
                agreement: $agreement,
            );
        }

        $images = [];
        foreach ($product->getImages() as $image) {
            $images[] = new ImageView($image, $awsCdnUrl);
        }
        $this->images = [...$images];

        $profiles = [];
        foreach ($profilesData as $profileDoc) {
            $profiles[] = new ProfileView(
                $profileDoc,
                $this->criteriaFilterService,
            );
        }
        $this->profiles = [...$profiles];

        $categoryProfiles = [];
        if ($categoryProfilesData) {
            foreach ($categoryProfilesData as $categoryProfileDoc) {
                $categoryProfiles[] = new ProfileView(
                    $categoryProfileDoc,
                    $this->criteriaFilterService,
                );
            }
        }

        $this->categoryProfiles = [...$categoryProfiles];
        $this->dateModified = $product->getDateModified();
        $this->dateCreated = $product->getDateCreated();
        $this->brand = $product->getBrand() ? new BrandView($product->getBrand()) : null;
        $this->requirementResponses = $product->getRequirementResponses();
        $this->manufacturers = $product->getManufacturers();
        $this->additionalProperties = $product->getAdditionalProperties();
        $this->expirationDate = $product->getExpirationDate();
        $this->documents = $product->getDocuments();
        $this->vendor = $product->getVendor();
    }
}
