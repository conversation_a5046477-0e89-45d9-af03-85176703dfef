<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Document;

/**
 * Class DocumentView.
 */
class DocumentView
{
    private ?string $id = null;

    private ?string $hash = null;

    private ?string $description = null;

    private ?string $title = null;

    private ?string $url = null;

    private ?string $format = null;

    private ?string $titleEn = null;

    private ?string $titleRu = null;

    private ?string $documentOf = null;

    private ?string $datePublished = null;

    private ?string $author = null;

    private ?string $dateModified = null;

    private $documentType;

    private $index;

    private $language;

    private $relatedItem;

    /**
     * DocumentView constructor.
     */
    public function __construct(Document $document)
    {
        $this->id = $document->getId();
        $this->hash = $document->getHash();
        $this->description = $document->getDescription();
        $this->title = $document->getTitle();
        $this->url = $document->getUrl();
        $this->format = $document->getFormat();
        $this->titleEn = $document->getTitleEn();
        $this->titleRu = $document->getTitleRu();
        $this->documentOf = $document->getDocumentOf();
        $this->datePublished = $document->getDatePublished();
        $this->author = $document->getAuthor();
        $this->dateModified = $document->getDateModified();
        $this->documentType = $document->getDocumentType();
        $this->index = $document->getIndex();
        $this->language = $document->getLanguage();
        $this->relatedItem = $document->getRelatedItem();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getFormat(): ?string
    {
        return $this->format;
    }

    public function getTitleEn(): ?string
    {
        return $this->titleEn;
    }

    public function getTitleRu(): ?string
    {
        return $this->titleRu;
    }

    public function getDocumentOf(): ?string
    {
        return $this->documentOf;
    }

    public function getDatePublished(): ?string
    {
        return $this->datePublished;
    }

    public function getAuthor(): ?string
    {
        return $this->author;
    }

    public function getDateModified(): ?string
    {
        return $this->dateModified;
    }

    public function getDocumentType()
    {
        return $this->documentType;
    }

    public function getIndex()
    {
        return $this->index;
    }

    public function getLanguage()
    {
        return $this->language;
    }

    public function getRelatedItem()
    {
        return $this->relatedItem;
    }
}
