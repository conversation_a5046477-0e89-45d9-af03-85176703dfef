<?php

namespace App\Resource\Embedded;

use App\Document\Embedded\Item;
use App\Resource\BaseClassificationView;

/**
 * Class ItemView.
 */
class ItemView
{
    private ?string $id = null;

    private ?string $description = null;

    private ?string $descriptionEn = null;

    private ?BaseClassificationView $classification = null;

    private ?PeriodView $deliveryDate = null;

    /**
     * @var BaseClassificationView[]
     */
    private array $additionalClassifications = [];

    private ?UnitView $unit = null;

    private ?float $quantity = null;

    private ?AddressView $deliveryAddress = null;

    private $location;

    private $relatedLot;

    private $award;

    private $tender;

    /**
     * ItemView constructor.
     */
    public function __construct(Item $item)
    {
        $this->id = $item->getId();
        $this->description = $item->getDescription();
        $this->descriptionEn = $item->getDescriptionEn();

        $classification = $item->getClassification();
        if ($classification) {
            $this->classification = new BaseClassificationView($classification);
        }

        $deliveryDate = $item->getDeliveryDate();
        if ($deliveryDate) {
            $this->deliveryDate = new PeriodView($deliveryDate);
        }

        $this->additionalClassifications = [];
        foreach ($item->getAdditionalClassifications() as $additionalClassification) {
            $this->additionalClassifications[] = new BaseClassificationView($additionalClassification);
        }

        $unit = $item->getUnit();
        if ($unit) {
            $this->unit = new UnitView($unit);
        }

        $this->quantity = $item->getQuantity();

        $deliveryAddress = $item->getDeliveryAddress();
        if ($deliveryAddress) {
            $this->deliveryAddress = new AddressView($deliveryAddress);
        }

        $this->location = $item->getLocation();
        $this->relatedLot = $item->getRelatedLot();
        $this->award = $item->getAward();
        $this->tender = $item->getTender();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getClassification(): ?BaseClassificationView
    {
        return $this->classification;
    }

    public function getDeliveryDate(): ?PeriodView
    {
        return $this->deliveryDate;
    }

    /**
     * @return BaseClassificationView[]
     */
    public function getAdditionalClassifications(): array
    {
        return $this->additionalClassifications;
    }

    public function getUnit(): ?UnitView
    {
        return $this->unit;
    }

    public function getQuantity(): ?float
    {
        return $this->quantity;
    }

    public function getDeliveryAddress(): ?AddressView
    {
        return $this->deliveryAddress;
    }

    public function getLocation()
    {
        return $this->location;
    }

    public function getRelatedLot()
    {
        return $this->relatedLot;
    }

    public function getAward()
    {
        return $this->award;
    }

    public function getTender()
    {
        return $this->tender;
    }

    public function getDescriptionEn(): ?string
    {
        return $this->descriptionEn;
    }
}
