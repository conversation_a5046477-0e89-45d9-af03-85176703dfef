App\Resource\Embedded\DocumentView:
  attributes:
    id:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    hash:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    description:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    title:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    url:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    format:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    titleEn:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
      serialized_name: 'title_en'
    titleRu:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
      serialized_name: 'title_ru'
    documentOf:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    datePublished:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    author:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    dateModified :
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    documentType:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    index:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    language:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
    relatedItem:
      groups: [
          'tender:read',
          'tender:cdb_parse',
          'tender:list'
      ]
