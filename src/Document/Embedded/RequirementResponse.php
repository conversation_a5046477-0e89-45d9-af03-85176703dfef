<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class RequirementResponse
{
    #[MongoDB\Field(type: Type::STRING)]
    private string $requirement;
    #[MongoDB\Field(type: Type::RAW)]
    private $value = null;
    #[MongoDB\Field(type: Type::RAW)]
    private array $values = [];
    #[MongoDB\EmbedOne(targetDocument: EmbeddedClassification::class)]
    private ?EmbeddedClassification $classification = null;

    public function getRequirement(): string
    {
        return $this->requirement;
    }

    public function setRequirement(string $requirement): RequirementResponse
    {
        $this->requirement = $requirement;
        return $this;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value)
    {
        $this->value = $value;
        return $this;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    public function setValues(array $values): RequirementResponse
    {
        $this->values = $values;
        return $this;
    }

    public function getClassification()
    {
        return $this->classification;
    }

    public function setClassification($classification)
    {
        $this->classification = $classification;
        return $this;
    }
}
