<?php

namespace App\Tests\Resource;

use App\Document\Category;
use App\Document\Embedded\EmbeddedClassification;
use App\Resource\CategoryView;
use App\Service\CriteriaFilterService;
use PHPUnit\Framework\TestCase;
use Doctrine\Common\Collections\ArrayCollection;
use App\Document\Agreement;
use App\Document\Profile;
use App\Resource\ProfileView;

/**
 * @covers \App\Resource\CategoryView
 */
class CategoryViewTest extends TestCase
{
    private $criteriaFilterServiceMock;
    private $categoryMock;
    private $classificationMock;
    private $agreementMock;

    protected function setUp(): void
    {
        $this->criteriaFilterServiceMock = $this->createMock(CriteriaFilterService::class);
        $this->categoryMock = $this->createMock(Category::class);
        $this->classificationMock = $this->createMock(EmbeddedClassification::class);
        $this->agreementMock = $this->createMock(Agreement::class);

        $this->categoryMock->method('getClassification')->willReturn($this->classificationMock);
        $this->categoryMock->method('getImages')->willReturn(new ArrayCollection());

        $this->categoryMock->method('getAdditionalClassifications')->willReturn([]);
        $this->categoryMock->method('getAgreementID')->willReturn('test-agreement-id');
        $this->categoryMock->method('getProcuringEntity')->willReturn(null);

        $this->classificationMock->method('getId')->willReturn('test-class-id');
        $this->classificationMock->method('getDescription')->willReturn('Test Classification');
        $this->classificationMock->method('getScheme')->willReturn('CPV');
    }

    public function testConstructorFiltersCriteria(): void
    {
        $unfilteredCriteria = [
            ['requirementGroups' => [['requirements' => [['id' => 'req1', 'isArchived' => false], ['id' => 'req2', 'isArchived' => true]]]]]
        ];
        $expectedFilteredCriteria = [
            ['requirementGroups' => [['requirements' => [['id' => 'req1', 'isArchived' => false]]]]]
        ];

        $this->categoryMock->method('getCriteria')->willReturn($unfilteredCriteria);

        $this->criteriaFilterServiceMock
            ->expects($this->once())
            ->method('filterArchivedRequirements')
            ->with($this->equalTo($unfilteredCriteria))
            ->willReturn($expectedFilteredCriteria);

        $categoryView = new CategoryView(
            category: $this->categoryMock,
            criteriaFilterService: $this->criteriaFilterServiceMock,
            agreement: $this->agreementMock
        );

        $this->assertEquals($expectedFilteredCriteria, $categoryView->criteria);
    }

    public function testConstructorSetsBasicPropertiesAndHandlesProfiles(): void
    {
        $this->categoryMock->method('getCriteria')->willReturn([]);
        $profileMockForList = $this->createMock(Profile::class);
        $profileMockForList->method('getClassification')->willReturn($this->classificationMock);

        $this->criteriaFilterServiceMock
            ->method('filterArchivedRequirements')
            ->willReturn([]);

        $categoryView = new CategoryView(
            category: $this->categoryMock,
            criteriaFilterService: $this->criteriaFilterServiceMock,
            categoryProfiles: [$profileMockForList],
            agreement: $this->agreementMock
        );
        
        $this->assertIsArray($categoryView->profiles);
        $this->assertCount(1, $categoryView->profiles);
        $this->assertInstanceOf(ProfileView::class, $categoryView->profiles[0]);
    }
}
