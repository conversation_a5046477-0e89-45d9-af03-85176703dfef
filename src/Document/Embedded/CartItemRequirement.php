<?php

namespace App\Document\Embedded;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Doctrine\ODM\MongoDB\Types\Type;

#[MongoDB\EmbeddedDocument]
class CartItemRequirement
{
    #[MongoDB\Field(type: Type::STRING)]
    private string $dataType;

    #[MongoDB\Field(type: Type::STRING)]
    private string $title;

    #[MongoDB\Field(type: Type::RAW)]
    private mixed $expectedValue;

    #[MongoDB\Field(type: Type::RAW)]
    private array $expectedValues;

    #[MongoDB\Field(type: Type::INT)]
    private int $minValue;

    #[MongoDB\Field(type: Type::INT)]
    private int $maxValue;

    public function getDataType(): string
    {
        return $this->dataType;
    }

    public function setDataType(string $dataType): void
    {
        $this->dataType = $dataType;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getExpectedValue(): mixed
    {
        return $this->expectedValue;
    }

    public function setExpectedValue(mixed $expectedValue): void
    {
        $this->expectedValue = $expectedValue;
    }

    public function getExpectedValues(): array
    {
        return $this->expectedValues;
    }

    public function setExpectedValues(array $expectedValues): void
    {
        $this->expectedValues = $expectedValues;
    }

    public function getMinValue(): int
    {
        return $this->minValue;
    }

    public function setMinValue(int $minValue): void
    {
        $this->minValue = $minValue;
    }

    public function getMaxValue(): int
    {
        return $this->maxValue;
    }

    public function setMaxValue(int $maxValue): void
    {
        $this->maxValue = $maxValue;
    }
}
