<?php

namespace App\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class SlackNotifier
{
    private string $webhookUrl;
    private Client $httpClient;

    public function __construct(string $webhookUrl)
    {
        $this->webhookUrl = $webhookUrl;
        $this->httpClient = new Client(); // Guzzle HTTP-клієнт
    }

    public function send(string $message, array $context = []): void
    {
        $payload = [
            'text' => $message,
        ];

        if (!empty($context)) {
            $payload['attachments'] = [[
                'text' => json_encode($context, JSON_PRETTY_PRINT),
            ]];
        }

        try {
            $this->httpClient->post($this->webhookUrl, [
                'json' => $payload,
            ]);
        } catch (RequestException $e) {
            throw new \RuntimeException('Failed to send Slack message: ' . $e->getMessage());
        }
    }
}
