<?php

namespace App\Extension;

use ApiPlatform\Doctrine\Odm\Extension\AggregationCollectionExtensionInterface;
use ApiPlatform\Metadata\Operation;
use App\Document\Filter;
use App\Security\JwtCheckerTrait;
use Doctrine\ODM\MongoDB\Aggregation\Builder;

/**
 * Class CurrentUserExtension.
 */
readonly class CurrentUserExtension implements AggregationCollectionExtensionInterface
{
    use JwtCheckerTrait;

    public function applyToCollection(Builder $aggregationBuilder, string $resourceClass, ?Operation $operation = null, array &$context = []): void
    {
        if (Filter::class !== $resourceClass) {
            return;
        }
        if (!$userId = $this->checkJwt()) {
            return;
        }

        $aggregationBuilder
            ->match()
            ->field('userId')
            ->equals($userId);
    }
}
