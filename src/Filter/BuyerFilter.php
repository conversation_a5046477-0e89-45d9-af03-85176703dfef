<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

class BuyerFilter extends AbstractSearchFilter
{
    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = [],
    ): array {
        $searches = [];

        $values = $this->getValue($context['filters']);

        foreach ($this->properties ?? [] as $property) {
            $searches[] = $this->getQuery($property, (array) $values, null);
        }

        if (!$values || empty($searches)) {
            return $clauseBody;
        }

        $query = array_merge_recursive($clauseBody, [
            'bool' => [
                'should' => $searches,
                'minimum_should_match' => 1,
            ],
        ]);

        return $query;
    }

    private function getValue(array $filters)
    {
        foreach (array_keys($this->properties ?? []) as $key) {
            if (!array_key_exists($key, $filters)) {
                continue;
            }

            return $filters[$key];
        }

        return null;
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $termQuery = ['terms' => [$property => $values]];

        if (null !== $nestedPath) {
            $termQuery = ['nested' => ['path' => $nestedPath, 'query' => $termQuery]];
        }

        return $termQuery;
    }
}
