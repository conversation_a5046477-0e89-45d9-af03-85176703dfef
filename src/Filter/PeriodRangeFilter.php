<?php

namespace App\Filter;

use ApiPlatform\Api\ResourceClassResolverInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyMetadataFactoryInterface;
use ApiPlatform\Metadata\Property\Factory\PropertyNameCollectionFactoryInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;

/**
 * Class PeriodRangeFilter.
 */
final class PeriodRangeFilter extends BaseRangeFilter
{
    private array $startProperties;

    private array $endProperties;

    public function __construct(
        PropertyNameCollectionFactoryInterface $propertyNameCollectionFactory,
        PropertyMetadataFactoryInterface $propertyMetadataFactory,
        ResourceClassResolverInterface $resourceClassResolver,
        ?NameConverterInterface $nameConverter = null,
        ?array $properties = null,
        ?array $startProperties = null,
        ?array $endProperties = null
    ) {
        parent::__construct($propertyNameCollectionFactory, $propertyMetadataFactory, $resourceClassResolver, $nameConverter, $properties);
        $this->startProperties = (is_array($startProperties)) ? $startProperties : [];
        $this->endProperties = (is_array($endProperties)) ? $endProperties : [];
    }

    public function getRangeFieldType(): string
    {
        return 'date';
    }

    /**
     * @param \DateTimeInterface|null $from
     * @param \DateTimeInterface|null $to
     */
    public function getRangeQuery(string $property, $from = null, $to = null): array
    {
        if (in_array($property, $this->startProperties)) {
            $field = $property.'.startDate';
        } elseif (in_array($property, $this->endProperties)) {
            $field = $property.'.endDate';
        }

        $query = [];

        if ($from) {
            if ($to) {
                // both dateFrom and dateTo selected
                $query = [
                    'bool' => [
                        'should' => [
                            ['range' => [$field => ['gte' => $from->format(\DATE_ATOM)]]],
                            ['range' => [$field => ['lte' => $to->format(\DATE_ATOM)]]],
                        ],
                        'minimum_should_match' => 2,
                    ],
                ];
            } else {
                // only dateFrom selected
                $query = [
                    'range' => [
                        $field => ['gte' => $from->format(\DATE_ATOM)],
                    ],
                ];
            }
        } else {
            // only dateTo selected
            $query = [
                'range' => [
                    $field => ['lte' => $to->format(\DATE_ATOM)],
                ],
            ];
        }

        return $query;
    }
}
