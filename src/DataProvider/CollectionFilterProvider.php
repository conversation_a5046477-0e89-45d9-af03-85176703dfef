<?php

namespace App\DataProvider;

use ApiPlatform\Doctrine\Odm\State\CollectionProvider;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\Pagination\PaginatorInterface;
use ApiPlatform\State\ProviderInterface;
use App\Pagination\CollectionPaginator;
use App\Repository\FilterRepository;
use App\Resource\FilterView;

readonly class CollectionFilterProvider implements ProviderInterface
{
    public function __construct(
        private CollectionProvider $collectionProvider,
        private FilterRepository $filterRepository
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $filters = $this->collectionProvider->provide($operation, $uriVariables, $context);

        $views = [];
        foreach ($filters as $filter) {
            $views[] = new FilterView($filter);
        }

        if ($filters instanceof PaginatorInterface) {
            $views = new CollectionPaginator(
                $views,
                $filters->getCurrentPage(),
                $filters->getLastPage(),
                $filters->getItemsPerPage(),
                $filters->getTotalItems()
            );
        }

        return $views;
    }
}
