<?php

namespace App\Filter;

use ApiPlatform\Elasticsearch\Filter\AbstractSearchFilter;
use ApiPlatform\Metadata\Operation;

/**
 * Class ClassificationFilter.
 */
final class MnnClassificationFilter extends AbstractSearchFilter
{
    const REQUIREMENT = 'requirement';

    const VALUES = 'values';

    public function apply(
        array $clauseBody,
        string $resourceClass,
        ?Operation $operationName = null,
        array $context = [],
    ): array {
        $searches = [];

        foreach ($context['filters'] ?? [] as $property => $values) {
            if (!array_key_exists($property, $this->properties)) {
                continue;
            }

            $searchProperty = $this->properties[$property];

            if (!is_array($values)) {
                $values = (array)$values;
            }

            $queryValues = [];
            foreach ($values as $value) {
                $queryValues[] = $value;
                $queryValues[] = ucfirst($value);
            }

            $searches[] = $this->getQuery($searchProperty[self::VALUES], $queryValues, null);
        }

        if (!$searches) {
            return $clauseBody;
        }

        return array_merge_recursive($clauseBody, [
            'bool' => [
                'must' => $searches
            ],
        ]);
    }

    protected function getQuery(string $property, array $values, ?string $nestedPath): array
    {
        $termQuery = ['terms' => [$property => $values]];

        if (null !== $nestedPath) {
            $termQuery = ['nested' => ['path' => $nestedPath, 'query' => $termQuery]];
        }

        return $termQuery;
    }

    public function getDescription(string $resourceClass): array
    {
        $description = [];

        foreach ($this->properties as $key => $related) {
            $description[$key] = [
                'property' => $key,
                'type'     => 'string',
                'required' => false,
            ];
            $description[$key . '[]'] = [
                'property' => $key . '[]',
                'type'     => 'array',
                'required' => false,
            ];
        }

        return $description;
    }
}
