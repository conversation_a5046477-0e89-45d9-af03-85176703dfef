<?php

namespace App\DataProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Document\Agreement;
use App\Manager\CategoryManager;
use App\Manager\ProfileManager;
use App\Resource\CategoryView;
use App\Service\MarketCacheService;
use Doctrine\ODM\MongoDB\DocumentManager;
use App\Service\CriteriaFilterService;

final readonly class CategoryDataProvider implements ProviderInterface
{
    private const PRODUCTS_LIMIT = 5;

    public function __construct(
        private CategoryManager $categoryManager,
        private string $awsCdnUrl,
        private MarketCacheService $marketCacheService,
        private ProfileManager $profileManager,
        private DocumentManager $documentManager,
        private CriteriaFilterService $criteriaFilterService,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $agreement = null;
        $document = $this->categoryManager->find($uriVariables['id']);
        if (!$document || $document->getStatus() === 'hidden') {
            return null;
        }

        if ($document->getAgreementID()) {
            $agreement = $this->documentManager->getRepository(Agreement::class)->findOneBy(['prozorroId' => $document->getAgreementID()]);
        }

        return new CategoryView(
            $document,
            $this->criteriaFilterService,
            $this->marketCacheService->getProductsCacheByCategoryId($document->getId()),
            $this->awsCdnUrl,
            $this->profileManager->findByCategory($uriVariables['id']),
            $agreement,
        );
    }
}
