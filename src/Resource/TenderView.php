<?php

namespace App\Resource;

use ApiPlatform\Elasticsearch\State\CollectionProvider;
use ApiPlatform\Elasticsearch\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Post;
use App\DataProvider\AllMarketClassificationDataProvider;
use App\DataProvider\CategoryProfilesDataProvider;
use App\DataProvider\TenderDataProvider;
use App\DataProvider\TenderSendedBidsDataProvider;
use App\Document\Tender;
use App\Dto\TenderFavoriteDto;
use App\Filter\BidsUserFIlter;
use App\Filter\ClassificationFilter;
use App\Filter\CurrentCompanyFilter;
use App\Filter\CurrentUserFilter;
use App\Filter\DateRangeFilter;
use App\Filter\FavoriteTenderFilter;
use App\Filter\KeywordFilter;
use App\Filter\MatchFilter;
use App\Filter\OrderFilter;
use App\Filter\PeriodRangeFilter;
use App\Filter\TestModeFilter;
use App\Filter\ValueRangeFilter;
use App\Resource\Embedded\DocumentView;
use App\Resource\Embedded\DzoDataView;
use App\Resource\Embedded\ItemView;
use App\Resource\Embedded\LotView;
use App\Resource\Embedded\OrganizationView;
use App\Resource\Embedded\PeriodView;
use App\Resource\Embedded\ProcuringEntityView;
use App\Resource\Embedded\ValueView;
use App\State\TenderFavoriteProcessor;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ApiResource(
    shortName: 'tender',
    operations: [
        new GetCollection(normalizationContext: ['groups' => 'tender:list'],
            provider: CollectionProvider::class,
            stateOptions: new Options(index: 'dzo_tender')),
        new Get(
            uriTemplate: '/tenders/bids/sended',
            normalizationContext: ['groups' => 'tender:list'],
            provider: TenderSendedBidsDataProvider::class
        ),
        new Get(normalizationContext: ['groups' => 'tender:read'], provider: TenderDataProvider::class),
        new Post(
            uriTemplate: 'tenders/favorite',
            description: 'Add or remove tender from favorites',
            security: "is_granted('ROLE_SELLER')",
            input: TenderFavoriteDto::class,
            processor: TenderFavoriteProcessor::class,
        ),
    ],
    order: ['date' => 'DESC']
)]
#[ApiFilter(OrderFilter::class, properties: [
    'date',
    'dateCreated',
    'dateModified',
    'auctionPeriod.startDate',
    'value.amount'
], arguments: ['orderParameterName' => 'order'])]
#[ApiFilter(MatchFilter::class, properties: [
    'tenderID'                     => 'exact',
    'status'                       => 'exact',
    'procurementMethodTypeFilter'  => 'exact',
    'mainProcurementCategory'      => 'exact',
    'items.deliveryAddress.region' => 'exact',
    'milestonesCode'               => 'exact',
    'hasActiveMonitoring'          => 'exact',
])]
#[ApiFilter(KeywordFilter::class, properties: [
    'tenderIDText',
    'titleEn',
    'title',
    'description',
    'descriptionEn',
    'items.description',
    'items.descriptionEn',
    'stage2TenderId',
    'lots.description',
    'lots.descriptionEn',
])]
#[ApiFilter(ClassificationFilter::class, properties: [
    'classification'           => 'items.classification.id',
    'additionalClassification' => 'items.additionalClassifications.id',
    'procuringEntity'          => 'procuringEntity.identifier.id',
    'funder'                   => 'funders.identifier.id',
])]
#[ApiFilter(FavoriteTenderFilter::class, properties: ['isFavorite' => 'dzoData.favoriteUsers.id'])]
#[ApiFilter(CurrentCompanyFilter::class, properties: [
    'isMyAnnounced' => 'dzoData.ownerId',
    'hasMyBid'      => 'dzoData.bidUsers.id'
])]
#[ApiFilter(BidsUserFIlter::class, properties: [
    'isPublicated' => 'isPublicated',
    'isDraft' => 'isDraft',
    'isWaitSign' => 'isWaitSign'
])]
#[ApiFilter(ValueRangeFilter::class, properties: ['value'])]
#[ApiFilter(DateRangeFilter::class, properties: ['date', 'dateCreated'])]
#[ApiFilter(PeriodRangeFilter::class,
    properties: ['enquiryPeriod', 'tenderPeriod', 'auctionPeriod', 'qualificationPeriod'],
    arguments: [
        'endProperties'   => ['enquiryPeriod', 'tenderPeriod'],
        'startProperties' => ['auctionPeriod', 'qualificationPeriod'],
    ]
)]
#[ApiFilter(TestModeFilter::class, properties: ['testMode'])]
readonly class TenderView
{
    public ?string $id;

    public ?string $tenderID;

    public ?string $procurementMethod;

    public ?string $mode;

    #[SerializedName('is_masked')]
    public ?bool $isMasked;

    public ?int $numberOfBids;

    public ?PeriodView $awardPeriod;

    public ?string $mainProcurementCategory;

    public ?string $auctionUrl;

    public ?PeriodView $enquiryPeriod;

    public ?string $submissionMethod;

    public ?ProcuringEntityView $procuringEntity;

    public ?string $owner;

    /**
     * @var DocumentView[]
     */
    public array $documents;

    public ?string $title;

    public ?string $titleEn;

    public ?string $description;

    public ?string $descriptionEn;

    public mixed $plans;

    public ?string $procurementMethodRationale;

    public ?ValueView $guarantee;

    public ?\DateTimeInterface $dateModified;

    public ?\DateTimeInterface $dateCreated;

    public ?\DateTimeInterface $noticePublicationDate;

    public ?string $status;

    public ?PeriodView $tenderPeriod;

    public mixed $contracts;

    public ?PeriodView $auctionPeriod;

    public ?string $procurementMethodType;

    public mixed $awards;

    public mixed $date;

    public mixed $milestones;

    public ?ValueView $minimalStep;

    /**
     * @var ItemView[]
     */
    public array $items;

    public mixed $bids;

    public mixed $reviewRequests;

    public ?ValueView $value;

    public ?string $awardCriteria;

    public mixed $features;

    public mixed $questions;

    public mixed $complaints;

    public mixed $agreements;

    public ?PeriodView $qualificationPeriod;

    public ?PeriodView $complaintPeriod;

    /**
     * @var LotView[]
     */
    public array $lots;

    public mixed $agreementDuration;

    public mixed $maxAwardsCount;

    public mixed $qualifications;

    public mixed $cancellations;

    public mixed $funders;

    public mixed $buyers;

    public mixed $revisions;

    public mixed $cause;

    public mixed $causeDescription;

    public mixed $criteria;

    public ?string $stage2TenderId;

    public ?bool $hasMonitoring;

    public ?bool $hasActiveMonitoring;

    public ?\DateTimeInterface $monitoringDateModified;

    public mixed $tenderConfig;

    public bool $isFavorite;

    public bool $testMode;
    public ?OrganizationView $inspector;

    public ?DzoDataView $dzoData;

    public function __construct(
        Tender $tender,
        private string $currentUserId
    ) {
        // mark tender as restricted if it has restricted config
        $config = $tender->getTenderConfig();
        if (is_array($config) && array_key_exists('restricted', $config) && $config['restricted']) {
            $tender->setRestrictedMode(true);
        }

        $this->id = $tender->getId();
        $this->tenderID = $tender->getTenderID();
        $this->procurementMethod = $tender->getProcurementMethod();
        $this->mode = $tender->getMode();
        $this->isMasked = $tender->getIsMasked();
        $this->numberOfBids = $tender->getNumberOfBids();

        $period = $tender->getAwardPeriod();
        $this->awardPeriod = ($period) ? new PeriodView($period) : null;

        $this->mainProcurementCategory = $tender->getMainProcurementCategory();
        $this->auctionUrl = $tender->getAuctionUrl();

        $period = $tender->getEnquiryPeriod();
        $this->enquiryPeriod = ($period) ? new PeriodView($period) : null;

        $this->submissionMethod = $tender->getSubmissionMethod();

        $procuringEntity = $tender->getProcuringEntity();
        $this->procuringEntity = ($procuringEntity) ? new ProcuringEntityView($procuringEntity) : null;
        $this->inspector = ($tender->getInspector()) ? new OrganizationView($tender->getInspector()) : null;

        $this->owner = $tender->getOwner();

        $documents = [];
        foreach ($tender->getDocuments() as $document) {
            $documents[] = new DocumentView($document);
        }
        $this->documents = [...$documents];

        $this->title = $tender->getTitle();
        $this->titleEn = $tender->getTitleEn();
        $this->description = $tender->getDescription();
        $this->descriptionEn = $tender->getDescriptionEn();
        $this->plans = $tender->getPlans();
        $this->procurementMethodRationale = $tender->getProcurementMethodRationale();

        $value = $tender->getGuarantee();
        $this->guarantee = ($value) ? new ValueView($value) : null;

        $this->dateModified = $tender->getDateModified();
        $this->dateCreated = $tender->getDateCreated();
        $this->noticePublicationDate = $tender->getNoticePublicationDate();
        $this->status = $tender->getStatus();

        $period = $tender->getTenderPeriod();
        $this->tenderPeriod = ($period) ? new PeriodView($period) : null;

        $this->contracts = $tender->getContracts();

        $period = $tender->getAuctionPeriod();
        $this->auctionPeriod = ($period) ? new PeriodView($period) : null;

        $this->procurementMethodType = $tender->getProcurementMethodType();
        $this->awards = $tender->getAwards();
        $this->date = $tender->getDate();
        $this->milestones = $tender->getMilestones();

        $value = $tender->getMinimalStep();
        $this->minimalStep = ($value) ? new ValueView($value) : null;

        $items = [];
        foreach ($tender->getItems() as $item) {
            $items[] = new ItemView($item);
        }
        $this->items = [...$items];

        $funders = [];
        foreach ($tender->getFunders() as $funder) {
            $funders[] = new OrganizationView($funder);
        }
        $this->funders = [...$funders];

        $this->bids = $tender->getBids();
        $this->reviewRequests = $tender->getReviewRequests();

        $value = $tender->getValue();
        $this->value = ($value) ? new ValueView($value) : null;

        $this->awardCriteria = $tender->getAwardCriteria();
        $this->features = $tender->getFeatures();
        $this->questions = $tender->getQuestions();
        $this->complaints = $tender->getComplaints();
        $this->agreements = $tender->getAgreements();

        $period = $tender->getQualificationPeriod();
        $this->qualificationPeriod = ($period) ? new PeriodView($period) : null;

        $period = $tender->getComplaintPeriod();
        $this->complaintPeriod = ($period) ? new PeriodView($period) : null;

        $lots = [];
        foreach ($tender->getLots() as $lot) {
            $lots[] = new LotView($lot);
        }
        $this->lots = [...$lots];

        $this->agreementDuration = $tender->getAgreementDuration();
        $this->maxAwardsCount = $tender->getMaxAwardsCount();
        $this->qualifications = $tender->getQualifications();
        $this->cancellations = $tender->getCancellations();
        $this->buyers = $tender->getBuyers();
        $this->revisions = $tender->getRevisions();
        $this->cause = $tender->getCause();
        $this->causeDescription = $tender->getCauseDescription();
        $this->criteria = $tender->getCriteria();
        $this->stage2TenderId = $tender->getStage2TenderId();
        $this->hasMonitoring = $tender->getHasMonitoring();
        $this->hasActiveMonitoring = $tender->getHasActiveMonitoring();
        $this->monitoringDateModified = $tender->getMonitoringDateModified();
        $this->tenderConfig = $tender->getTenderConfig();
        if ('' === $this->currentUserId) {
            $this->isFavorite = false;
        } else {
            $this->isFavorite = $tender->getDzoData()->isFavorite($this->currentUserId);
        }

        $this->dzoData = $tender->getDzoData() ? new DzoDataView($tender->getDzoData()) : null;
    }

    public function getMilestonesCode()
    {
        if (!is_array($this->milestones)) {
            return [];
        }
        $items = [];
        foreach ($this->milestones as $milestone) {
            if (!is_array($milestone) || !array_key_exists('code', $milestone)) {
                continue;
            }
            $items[] = $milestone['code'];
        }

        return $items;
    }

    public function getProcurementMethodTypeFilter()
    {
        $filter = $this->procurementMethodType ?? '';
        $rationale = $this->procurementMethodRationale ?? '';
        if ('reporting' === $filter && 'catalogue' === substr($rationale, 0, 9)) {
            $rationale = 'catalogue';
        }
        if ('' !== $rationale) {
            $rationale = '.' . $rationale;
        }

        return $filter . $rationale;
    }
}
