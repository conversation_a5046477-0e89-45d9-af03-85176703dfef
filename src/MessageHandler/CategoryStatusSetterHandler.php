<?php

namespace App\MessageHandler;

use App\Document\Product;
use App\Document\Profile;
use App\Message\CategoryStatusSetterMessage;
use App\Type\DocumentType;
use Doctrine\ODM\MongoDB\DocumentManager;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CategoryStatusSetterHandler
{
    private const LIMIT = 100;

    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger,
        private ObjectPersisterInterface $profilePersister,
        private ObjectPersisterInterface $productPersister
    ) {
    }

    public function __invoke(CategoryStatusSetterMessage $message)
    {
        try {
            $categoryDocumentClass = DocumentType::getDocumentClass(DocumentType::$CATEGORY);

            $category = $this->documentManager->getRepository($categoryDocumentClass)->find($message->getId());

            if (!$category) {
                $this->logger->critical('Cannot find Category by id ' . $message->getId());
                throw new \Exception('Cannot find Category by id ' . $message->getId());
            }

            /** @var Product $product */
            foreach ($this->getProducts($category->getId()) as $product) {
                $product->setRelatedCategoryStatus($category->getStatus());
                $this->documentManager->persist($product);
            }

            /** @var Profile $profile */
            foreach ($this->getProfiles($category->getId()) as $profile) {
                $profile->setRelatedCategoryStatus($category->getStatus());
                $this->documentManager->persist($profile);
            }

        } catch (\Throwable $e) {
            $this->logger->critical('Unhandled Error received: ' . $e->getMessage(), [$this, $e]);
            throw $e;
        }
    }

    private function getProducts(string $categoryId): iterable
    {
        $dateCreated = null;
        $query =  [
            'relatedCategoryId' => $categoryId
        ];

        while ($products = $this->documentManager->getRepository(Product::class)->findBy(
            $query,
            ['dateCreated' => 'ASC'],
            self::LIMIT
        )) {
            foreach ($products as $product) {
                $dateCreated = $product->getDateCreated();
                yield $product;
            }
            if ($dateCreated) {
                $query['dateCreated'] = ['$gt' => $dateCreated];
            }
            $this->productPersister->replaceMany($products);
            unset($products);
            $this->documentManager->flush();
            $this->documentManager->clear();
            gc_collect_cycles();
        }
    }

    private function getProfiles(string $categoryId): iterable
    {
        $dateCreated = null;
        $query =  [
            'relatedCategoryId' => $categoryId
        ];

        while ($profiles = $this->documentManager->getRepository(Profile::class)->findBy(
            $query,
            ['dateCreated' => 'ASC'],
            self::LIMIT
        )) {
            foreach ($profiles as $profile) {
                $dateCreated = $profile->getDateCreated();
                yield $profile;
            }

            if ($dateCreated) {
                $query['dateCreated'] = ['$gt' => $dateCreated];
            }
            $this->profilePersister->replaceMany($profiles);
            unset($profiles);
            $this->documentManager->flush();
            $this->documentManager->clear();
            gc_collect_cycles();
        }
    }
}
