<?php

namespace App\MessageHandler;

use App\Document\Category;
use App\Document\Embedded\Image;
use App\Document\MarketClassification;
use App\Document\PlanIdentifier;
use App\Document\Profile;
use App\Document\Tender;
use App\Document\TenderIdentifier;
use App\Message\CategoryStatusSetterMessage;
use App\Service\MarketCacheService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\MongoDBException;
use Elastica\Exception\ClientException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToWriteFile;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

abstract class BaseHandler
{
    protected string $prozorroPublicPointUrl;

    /**
     * TenderHandler constructor.
     */
    public function __construct(
        private readonly DocumentManager $documentManager,
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly MarketCacheService $marketCacheService,
        private readonly string $prozorroPublicPointPassword,
        string $prozorroPublicPointUrl,
        protected readonly string $prozorroMarketApiLink,
        protected readonly string $prozorroMarketApiPassword,
        protected readonly FilesystemOperator $marketStorage,
        private readonly MessageBusInterface $messageBus,
    ) {
        $this->prozorroPublicPointUrl = rtrim($prozorroPublicPointUrl, '/').'/';
    }

    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    public function getSerializer(): SerializerInterface
    {
        return $this->serializer;
    }

    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * @throws MongoDBException
     */
    public function save($document, bool $andFlush = true)
    {
        $this->getDocumentManager()->persist($document);
        try {
            if ($andFlush) {
                $this->getDocumentManager()->flush();
                $this->getDocumentManager()->clear();
            }
        } catch (MongoDBException $exception) {
            $this->getLogger()->alert('Mongo failed:'.$exception->getMessage(), [$this, $exception]);
            throw $exception;
        } catch (ClientException $exception) {
            $this->getLogger()->alert('Elasticsearch failed:'.$exception->getMessage(), [$this, $exception]);
            throw $exception;
        } catch (Exception $exception) {
            $this->getLogger()->alert('Worker failed:'.$exception->getMessage(), [$this, $exception]);
            throw $exception;
        }
    }

    /**
     * @throws MongoDBException
     */
    public function handleIdentifier($data, string $identifierClass, $andFlush)
    {
        $identifier = $this
            ->getSerializer()
            ->deserialize(
                json_encode($data),
                $identifierClass,
                'json',
                [AbstractNormalizer::OBJECT_TO_POPULATE => new $identifierClass()]
            );

        $this->save($identifier, $andFlush);

        return $identifier;
    }

    /**
     * @return \Psr\Http\Message\ResponseInterface
     *
     * @throws GuzzleException
     */
    protected function requestPublicApi($url)
    {
        $client = new Client();

        return $client->request('GET', $this->prozorroPublicPointUrl.$url, [
            'headers' => [
                'Authorization' => 'Basic '.base64_encode($this->prozorroPublicPointPassword),
            ],
        ]
        );
    }

    protected function requestMarketApi($url)
    {
        $client = new Client();

        return $client->request('GET', $this->prozorroMarketApiLink.$url, [
            'headers' => [],
        ]
        );
    }

    /**
     * Fetches tender from the prozorro api.
     *
     * @throws GuzzleException
     */
    protected function fetchTender(string $tenderId): array
    {
        $response = $this->requestPublicApi('tenders/'.$tenderId);

        $result = json_decode((string) $response->getBody(), true);
        $data = $result['data'];
        if (array_key_exists('config', $result)) {
            $data['tenderConfig'] = $result['config'];
        }

        return $data;
    }

    /**
     * Fetches tender from the prozorro api and saves it to the database.
     * If $force is false, it will not fetch tender if it already exists in the database.
     *
     * @throws GuzzleException
     * @throws MongoDBException
     */
    protected function processTender(string $tenderId, bool $force): void
    {
        /** @var Tender $object */
        $object = $this->getDocumentManager()->find(Tender::class, $tenderId);

        if (!$force && $object) {
            return;
        }

        try {
            $data = $this->fetchTender($tenderId);
        } catch (\Exception $e) {
            echo $e->getMessage();

            return;
        }

        if (array_key_exists('value', $data) && is_array($data['value']) && array_key_exists(
            'amount',
            $data['value']
        )) {
            $data['value']['amount'] = floatval($data['value']['amount']);
        }
        if (array_key_exists('minimalStep', $data) && is_array($data['minimalStep']) && array_key_exists(
            'amount',
            $data['minimalStep']
        )) {
            $data['minimalStep']['amount'] = floatval($data['minimalStep']['amount']);
        }

        $object = $this
            ->getSerializer()
            ->deserialize(
                json_encode($data),
                Tender::class,
                'json',
                [AbstractNormalizer::OBJECT_TO_POPULATE => $object]
            );

        //        /** uncomment to debug with raw data */
        //        //@todo remove or comment for prod
        //        $object->setData($data);

        if (array_key_exists('procuringEntity', $data) && array_key_exists('identifier', $data['procuringEntity'])) {
            $this->handleIdentifier($data['procuringEntity']['identifier'], TenderIdentifier::class, false);
        }

        $this->save($object);
    }

    protected function processCategory(string $categoryId, bool $force): ?Category
    {
        try {
            $category = $this->getDocumentManager()->getRepository(Category::class)->findOneBy(
                ['prozorroId' => $categoryId]
            );

            $categoryOldStatus = null;
            if ($category) {
                $categoryOldStatus = $category->getStatus();
            }

            if (!$force && $category) {
                return $category;
            }

            try {
                $response = $this->requestMarketApi('categories/'.$categoryId);
            } catch (\Exception $exception) {
                return null;
            }

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            $classificationData = $data['classification'];
            $imagesArray = [];

            if (array_key_exists('procuringEntity', $data)) {
                unset($data['procuringEntity']['id']);
            }

            if (array_key_exists('images', $data)) {
                $imagesArray = $data['images'];
                unset($data['images']);
            }

            $data['prozorroId'] = $data['id'];
            unset($data['id']);

            if (!array_key_exists('dateCreated', $data) && array_key_exists('dateModified', $data)) {
                $data['dateCreated'] = $data['dateModified'];
            }

            $category = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data, JSON_THROW_ON_ERROR),
                    Category::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $category]
                );

            if (!empty($imagesArray)) {
                $this->getMarketImages($category, $imagesArray);
            }

            if (array_key_exists('procuringEntity', $data) && array_key_exists(
                'identifier',
                $data['procuringEntity']
            )) {
                $this->handleIdentifier($data['procuringEntity']['identifier'], PlanIdentifier::class, false);
            }

            $this->save($category);

            /** @var MarketClassification $marketClassification */
            $marketClassification = $this->getDocumentManager()->getRepository(MarketClassification::class)->findOneBy(
                ['id' => $classificationData['id']]
            );

            $marketClassification = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($classificationData, JSON_THROW_ON_ERROR),
                    MarketClassification::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $marketClassification]
                );

            $categoryCount = $this->getDocumentManager()->getRepository(Category::class)->findActiveByClassificationId($marketClassification->getId());
            $marketClassification->setActiveCategories($categoryCount);
            $this->save($marketClassification);

            if ('hidden' !== $category->getStatus()) {
                $this->marketCacheService->addCategoryToCache($category);
            }

            if ($category->getStatus() !== $categoryOldStatus) {
                $this->messageBus->dispatch(new CategoryStatusSetterMessage($category->getId()));
            }

            return $category;
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
            return null;
        }
    }

    protected function processProfile(string $profileId, bool $force): ?Profile
    {
        try {
            $profile = $this->getDocumentManager()->getRepository(Profile::class)->findOneBy(
                ['prozorroId' => $profileId]
            );

            if (!$force && $profile) {
                return $profile;
            }

            $categoryId = null;
            $response = $this->requestMarketApi('profiles/'.$profileId);

            $result = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            $data = $result['data'];
            $prozorroId = $data['id'];
            unset($data['id']);
            if (!empty($data['relatedCategory'])) {
                $categoryId = $data['relatedCategory'];
            }
            unset($data['classification']);

            if (array_key_exists('images', $data)) {
                $imagesArray = $data['images'];
                unset($data['images']);
            }

            $profile = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($data, JSON_THROW_ON_ERROR),
                    Profile::class,
                    'json',
                    [AbstractNormalizer::OBJECT_TO_POPULATE => $profile]
                );
            $profile->setProzorroId($prozorroId);

            if (!empty($imagesArray)) {
                $this->getMarketImages($profile, $imagesArray);
            }

            if ($categoryId) {
                if ($category = $this->processCategory($categoryId, false)) {
                    $profile->setRelatedCategoryId($category->getId());
                    if ($classification = $category->getClassification()) {
                        $profile->setClassification($classification);
                    }

                    if ($category->getStatus()) {
                        $profile->setRelatedCategoryStatus($category->getStatus());
                    }
                } else {
                    $profile->setRelatedCategoryId(null);
                }
                $profile->setRelatedCategory($categoryId);
            }

            $this->save($profile);

            return $profile;
        } catch (\Throwable $exception) {
            $this->getLogger()->error($exception->getMessage(), [$exception]);
            return null;
        }
    }

    protected function getMarketImages(object $object, array $images): void
    {
        if ($object->getImages()) {
            foreach ($object->getImages() as $image) {
                $this->marketStorage->delete($image->getUrl());
            }
            $object->setImages(new ArrayCollection());
        }

        $client = new Client();
        $urlArray = parse_url($this->prozorroMarketApiLink);
        foreach ($images as $imageArray) {
            try {
                $image = $client->request(
                    'GET',
                    $urlArray['scheme'].'://'.$this->prozorroMarketApiPassword.'@'.$urlArray['host'].$imageArray['url']
                );
                // for test, cant enter prozorro img storage
                //            $image = $client->request(
                //                'GET',
                //                'https://img.freepik.com/premium-photo/a-cute-fluffy-blue-ball-animal-running-through-a-magical-forest-trying-to-catch-a-flying_653449-863.jpg'
                //            );
                $this->marketStorage->write($imageArray['url'], $image->getBody());
            } catch (FilesystemException|UnableToWriteFile|GuzzleException $exception) {
                continue;
            }

            $image = $this
                ->getSerializer()
                ->deserialize(
                    json_encode($imageArray, JSON_THROW_ON_ERROR),
                    Image::class,
                    'json'
                );
            $object->addImage($image);
        }
    }
}
