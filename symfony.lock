{"api-platform/api-pack": {"version": "v1.3.0"}, "api-platform/core": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "a93061567140e386f107be75340ac2aee3f86cbf"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "composer/package-versions-deprecated": {"version": "*********"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.2"}, "doctrine/collections": {"version": "1.6.7"}, "doctrine/common": {"version": "3.0.2"}, "doctrine/dbal": {"version": "2.12.0"}, "doctrine/doctrine-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.0", "ref": "368794356c1fb634e58b38ad2addb36933f2e73e"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "1.4.3"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/mongodb-odm": {"version": "2.1.2"}, "doctrine/mongodb-odm-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.3", "ref": "1fd8598bf42227a1c8905edd32f640e4c5d15eff"}, "files": ["config/packages/doctrine_mongodb.yaml", "config/packages/prod/doctrine_mongodb.yaml", "src/Document/.gitignore"]}, "doctrine/orm": {"version": "2.7.4"}, "doctrine/persistence": {"version": "2.1.0"}, "doctrine/reflection": {"version": "1.2.1"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "elasticsearch/elasticsearch": {"version": "v6.7.2"}, "fig/link-util": {"version": "1.1.1"}, "friendsofphp/php-cs-fixer": {"version": "3.49", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.3"}, "friendsofsymfony/elastica-bundle": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "5.0", "ref": "46c9cd2c1e07f0fcfd97e96f12b03b2e0845c4cd"}, "files": ["config/packages/fos_elastica.yaml"]}, "guzzlehttp/guzzle": {"version": "7.2.0"}, "guzzlehttp/promises": {"version": "1.4.0"}, "guzzlehttp/psr7": {"version": "1.7.0"}, "guzzlehttp/ringphp": {"version": "1.1.1"}, "guzzlehttp/streams": {"version": "3.0.0"}, "jean85/pretty-package-versions": {"version": "1.5.1"}, "laminas/laminas-code": {"version": "3.4.1"}, "laminas/laminas-eventmanager": {"version": "3.3.0"}, "laminas/laminas-zendframework-bridge": {"version": "1.1.1"}, "league/flysystem-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "913dc3d7a5a1af0d2b044c5ac3a16e2f851d7380"}, "files": ["config/packages/flysystem.yaml", "var/storage/.gitignore"]}, "lexik/jwt-authentication-bundle": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "mongodb/mongodb": {"version": "1.4.3"}, "monolog/monolog": {"version": "2.2.0"}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6388de23860284db9acce0a7a5d9d13153bcb571"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nikic/php-parser": {"version": "v4.10.4"}, "ocramius/proxy-manager": {"version": "2.9.1"}, "pagerfanta/pagerfanta": {"version": "v2.5.1"}, "php": {"version": "7.4"}, "phpunit/phpunit": {"version": "11.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "10.0", "ref": "b786e93f0b38027289867faca488b8c511436fe0"}, "files": [".env.test", "phpunit.dist.xml", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.3"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "react/promise": {"version": "v2.8.0"}, "ruflin/elastica": {"version": "6.1.1"}, "sentry/sentry-symfony": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "76afa07d23e76f678942f00af5a6a417ba0816d0"}, "files": ["config/packages/sentry.yaml"]}, "snc/redis-bundle": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "36b3d9ab65be62de4e085a25e6ca899efa96b1f3"}, "files": ["config/packages/snc_redis.yaml"]}, "symfony/amqp-messenger": {"version": "v5.1.10"}, "symfony/asset": {"version": "v5.1.8"}, "symfony/cache": {"version": "v5.1.8"}, "symfony/cache-contracts": {"version": "v2.2.0"}, "symfony/config": {"version": "v5.1.8"}, "symfony/console": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "c6d02bdfba9da13c22157520e32a602dbee8a75c"}, "files": ["bin/console"]}, "symfony/dependency-injection": {"version": "v5.1.8"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v5.1.8"}, "symfony/doctrine-messenger": {"version": "v5.1.10"}, "symfony/dotenv": {"version": "v5.1.8"}, "symfony/error-handler": {"version": "v5.1.8"}, "symfony/event-dispatcher": {"version": "v5.1.8"}, "symfony/event-dispatcher-contracts": {"version": "v2.2.0"}, "symfony/filesystem": {"version": "v5.1.8"}, "symfony/finder": {"version": "v5.1.8"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "5f0d0fd82ffa3580fe0ce8e3b2d18506ebf37a0e"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/preload.php", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client-contracts": {"version": "v2.3.1"}, "symfony/http-foundation": {"version": "v5.1.8"}, "symfony/http-kernel": {"version": "v5.1.8"}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "e9a414b113ceadbf4e52abe37bf8f1b443f06ccb"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bridge": {"version": "v5.1.11"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "d7249f7d560f6736115eee1851d02a65826f0a56"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v5.1.8"}, "symfony/orm-pack": {"version": "v2.0.0"}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.20.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.20.0"}, "symfony/polyfill-mbstring": {"version": "v1.20.0"}, "symfony/polyfill-php73": {"version": "v1.20.0"}, "symfony/polyfill-php80": {"version": "v1.20.0"}, "symfony/profiler-pack": {"version": "v1.0.5"}, "symfony/property-access": {"version": "v5.1.8"}, "symfony/property-info": {"version": "v5.1.8"}, "symfony/redis-messenger": {"version": "v5.1.10"}, "symfony/routing": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "b4f3e7c95e38b606eef467e8a42a8408fc460c43"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "0a4bae19389d3b9cba1ca0102e3b2bccea724603"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.1.8"}, "symfony/security-csrf": {"version": "v5.1.8"}, "symfony/security-guard": {"version": "v5.1.8"}, "symfony/security-http": {"version": "v5.1.8"}, "symfony/serializer": {"version": "v5.1.8"}, "symfony/service-contracts": {"version": "v2.2.0"}, "symfony/stopwatch": {"version": "v5.1.8"}, "symfony/string": {"version": "v5.1.8"}, "symfony/translation-contracts": {"version": "v2.3.0"}, "symfony/twig-bridge": {"version": "v5.1.8"}, "symfony/twig-bundle": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "fab9149bbaa4d5eca054ed93f9e1b66cc500895d"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.1.8"}, "symfony/var-exporter": {"version": "v5.1.8"}, "symfony/web-link": {"version": "v5.1.8"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/yaml": {"version": "v5.1.8"}, "twig/twig": {"version": "v3.1.1"}, "webimpress/safe-writer": {"version": "2.1.0"}, "willdurand/negotiation": {"version": "v2.3.1"}}