<?php

namespace App\Command;

use App\Legacy\Repository\TenderBidsRepository;
use App\Repository\TenderRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:sync:tender-bids',
    description: 'Sync tender bids from legacy database',
)]
class SyncTenderBidsCommand extends Command
{
    private const TENDER_BIDS_CHUNK = 100;

    public function __construct(
        private readonly DocumentManager $dm,
        private readonly TenderBidsRepository $tenderBidsRepository,
        private readonly TenderRepository $tenderRepository,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $bids = $this->tenderBidsRepository->tenderBids();

        $count = 0;
        foreach ($bids as $item) {
            $tender = $this->tenderRepository->findOneBy(['id' => $item['tender_api_id']]);
            if (!$tender) {
                continue;
            }
            $tender->getDzoData()->addBidsUser($item['user_id']);
            if (self::TENDER_BIDS_CHUNK === $count) {
                $this->dm->flush();
                $this->dm->clear();
                $count = 0;
            }
            ++$count;
        }

        $this->dm->flush();
        $this->dm->clear();

        return Command::SUCCESS;
    }
}
